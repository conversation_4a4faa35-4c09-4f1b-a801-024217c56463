package id.co.bri.brimo.payment.core.network.response.qris

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse

internal data class QrisConfirmationResponse(
    @SerializedName("source_account_data_view") val sourceAccountDataView: BillingResponse?,
    @SerializedName("billing_detail") val billingDetail: BillingResponse?,
    @SerializedName("amount_data_view") val amountDataView: List<DataViewResponse>?,
    @SerializedName("total_data_view") val totalDataView: List<DataViewResponse>?,
    @SerializedName("save_as") val saveAs: String?,
    @SerializedName("amount") val amount: String?,
    @SerializedName("amount_string") val amountString: String?,
    @SerializedName("admin_fee") val adminFee: String?,
    @SerializedName("admin_fee_string") val adminFeeString: String?,
    @SerializedName("pay_amount") val payAmount: String?,
    @SerializedName("pay_amount_string") val payAmountString: String?,
    @SerializedName("pfm_category") val pfmCategory: Int?,
    @SerializedName("pfm_description") val pfmDescription: String?,
    @SerializedName("note") val note: String?,
    @SerializedName("reference_number") val referenceNumber: String?,
    @SerializedName("rate_currency") val rateCurrency: String?,
    @SerializedName("nominal_currency") val nominalCurrency: String?
)
