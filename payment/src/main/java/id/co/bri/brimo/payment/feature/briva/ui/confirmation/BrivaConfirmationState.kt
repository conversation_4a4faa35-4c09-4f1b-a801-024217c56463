package id.co.bri.brimo.payment.feature.briva.ui.confirmation

import id.co.bri.brimo.payment.app.BrivaConfirmationRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaPaymentResponse
import id.co.bri.brimo.payment.feature.briva.data.model.BrivaModel

internal data class BrivaConfirmationState(
    val brivaConfirmationRoute: BrivaConfirmationRoute,
    val brivaModel: BrivaModel = BrivaModel(),
    val accountList: List<AccountResponse> = emptyList(),
    val brivaPayment: UiState<BrivaPaymentResponse> = UiState.Init
)

internal sealed class BrivaConfirmationEvent {
    data class Payment(
        val accountNumber: String,
        val note: String,
        val pin: String,
        val saveAs: String
    ) : BrivaConfirmationEvent()

    object ResetPayment : BrivaConfirmationEvent()
    data class RefreshSaldo(val account: String) : BrivaConfirmationEvent()
}

internal sealed class BrivaConfirmationNavigation {
    data class Payment(val brivaData: String) : BrivaConfirmationNavigation()
}
