package id.co.bri.brimo.payment.feature.brizzi.ui.receipt

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.theme.Color_465668
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.BrizziPaymentResponse

@Composable
internal fun ReceiptView(
    data: BrizziPaymentResponse,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .paint(
                painter = painterResource(R.drawable.receipt_background),
                sizeToIntrinsics = false,
                contentScale = ContentScale.Crop
            )
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
            .padding(top = 16.dp)
    ) {
        Image(
            painter = painterResource(R.drawable.image_logo),
            contentDescription = null,
            modifier = Modifier
                .width(124.dp)
                .height(64.dp)
                .align(Alignment.CenterHorizontally),
            contentScale = ContentScale.Fit
        )

        Spacer(modifier = Modifier.height(24.dp))

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 64.dp))
                .background(Color.White)
                .paint(
                    painter = painterResource(R.drawable.image_watermark),
                    sizeToIntrinsics = false,
                    contentScale = ContentScale.Crop
                )
                .padding(16.dp)
        ) {
            Image(
                painter = painterResource(R.drawable.receipt_image_success),
                contentDescription = null,
                modifier = Modifier
                    .size(64.dp)
                    .align(Alignment.CenterHorizontally),
                contentScale = ContentScale.Fit
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = data.title.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = data.totalDataView?.firstOrNull()?.value.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.headlineMedium
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = data.headerDataView?.firstOrNull()?.value.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall
            )

            Spacer(modifier = Modifier.height(16.dp))

            Spacer(modifier = Modifier.height(12.dp))

            DividerHorizontal()

            Spacer(modifier = Modifier.height(12.dp))

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.drawable.image_tabungan),
                    contentDescription = null,
                    modifier = Modifier
                        .size(32.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop
                )

                Spacer(modifier = Modifier.width(12.dp))

                val source = data.dataViewTransaction?.firstOrNull()?.value?.split("\n")
                val sourceName = source?.firstOrNull().orEmpty()
                val sourceValue = source?.lastOrNull().orEmpty()
                Column(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = sourceName,
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(2.dp))

                    Text(
                        text = sourceValue,
                        modifier = Modifier.fillMaxWidth(),
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.drawable.receipt_icon_arrow),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                        .size(16.dp),
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.width(12.dp))

                DividerHorizontal()
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.drawable.image_tabungan),
                    contentDescription = null,
                    modifier = Modifier
                        .size(32.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop
                )

                Spacer(modifier = Modifier.width(12.dp))

                Column(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = data.dataViewTransaction?.getOrNull(1)?.value.orEmpty(),
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(2.dp))

                    Text(
                        text = data.dataViewTransaction?.getOrNull(2)?.value.orEmpty(),
                        modifier = Modifier.fillMaxWidth(),
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Spacer(modifier = Modifier.height(12.dp))

            DividerHorizontal()

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Detail Transaksi",
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = data.headerDataView?.getOrNull(1)?.name.orEmpty(),
                    modifier = Modifier.weight(1f),
                    color = Color_7B90A6,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = data.headerDataView?.getOrNull(1)?.value.orEmpty(),
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.End,
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = data.dataViewTransaction?.getOrNull(1)?.name.orEmpty(),
                    modifier = Modifier.weight(1f),
                    color = Color_7B90A6,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = data.dataViewTransaction?.getOrNull(1)?.value.orEmpty(),
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.End,
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            DividerHorizontal()

            Spacer(modifier = Modifier.height(12.dp))

            data.amountDataView?.forEachIndexed { index, item ->
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = item?.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = item?.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                if (index < data.amountDataView.size - 1) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            DividerHorizontal()

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Biaya Termasuk PPN (Apabila Dikenakan/Apabila Ada)\n" +
                    "PT. Bank Rakyat Indonesia (Persero) Tbk.\n" +
                    "Kantor Pusat BRI - Jakarta Pusat\n" +
                    "NPWP : 01.001.608.7-093.000",
                modifier = Modifier.fillMaxWidth(),
                color = Color_465668,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.labelSmall
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "© 2025 PT. Bank Rakyat Indonesia (Persero), Tbk.",
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelSmall
        )

        Text(
            text = "Terdaftar dan diawasi oleh Otoritas Jasa Keuangan",
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelSmall
        )
    }
}

@Preview(widthDp = 420, heightDp = 840)
@Composable
private fun PreviewReceiptView() {
    MainTheme {
/*        ReceiptView(
            data = BrizziPaymentResponse(
                immediatelyFlag = true,
                headerDataView = listOf(
                    BrizziPaymentResponse.DataView(
                        name = "Tanggal",
                        value = "04 Jul 2025 | 09:20:38 WIB"
                    ),
                    BrizziPaymentResponse.DataView(
                        name = "No Ref",
                        value = "323529112691"
                    )
                ),
                dataViewTransaction = listOf(
                    BrizziPaymentResponse.DataView(
                        name = "Sumber Dana",
                        value = "Infinite\n0230 **** **** 308"
                    ),
                    BrizziPaymentResponse.DataView(
                        name = "Jenis Transaksi",
                        value = "Top Up Deposit BRIZZI"
                    ),
                    BrizziPaymentResponse.DataView(
                        name = "Nomor Kartu",
                        value = "6013 5098 0000 0640"
                    )
                ),
                amountDataView = listOf(
                    BrizziPaymentResponse.DataView(
                        name = "Nominal",
                        value = "Rp20.000"
                    ),
                    BrizziPaymentResponse.DataView(
                        name = "Biaya Admin",
                        value = "Rp0"
                    )
                ),
                totalDataView = listOf(
                    BrizziPaymentResponse.DataView(
                        name = "Total",
                        value = "Rp20.000"
                    )
                ),
                share = true,
                title = "Transaksi Berhasil",
                titleImage = "receipt_00",
                footer = "Silakan lakukan aktivasi deposit kartu BRIZZI Anda.",
                rowDataShow = 3,
                referenceNumber = "323529112691",
                cardNumber = "",
                activatedBalance = "",
                key = "",
                rcHost = "",
                reff = "",
                pendingBalance = ""
            )
        )*/
    }
}
