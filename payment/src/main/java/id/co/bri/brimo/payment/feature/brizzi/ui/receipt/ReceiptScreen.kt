package id.co.bri.brimo.payment.feature.brizzi.ui.receipt

import android.Manifest
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asAndroidBitmap
import androidx.compose.ui.graphics.layer.drawLayer
import androidx.compose.ui.graphics.rememberGraphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.PrimaryButton
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.saveToDisk
import id.co.bri.brimo.payment.core.design.component.shareBitmap
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_FDEDED
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.BrizziPaymentResponse
import id.co.bri.brimo.payment.core.network.response.TransferPayResponse
import id.co.bri.brimo.payment.feature.transfer.ui.confirmation.TransferConfirmationViewModel
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun ReceiptScreen(
    onFinish: () -> Unit = {},
    receiptViewModel: ReceiptViewModel = koinViewModel(),
    transferConfirmationViewModel: TransferConfirmationViewModel = koinViewModel(),
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val paymentState = transferConfirmationViewModel.transferPayment

    LaunchedEffect(Unit) {
        // Ensure we have the payment data when screen loads
//        transferConfirmationViewModel.loadPaymentData()
    }

    val paymentDataState = paymentState.collectAsStateWithLifecycle(
        initialValue = null,
        lifecycle = lifecycleOwner.lifecycle
    ).value

    paymentDataState?.onSuccess { data ->
        ReceiptTransferContent(
            data = data,
            onFinish = onFinish
        )
    }
}


@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun ReceiptContent(
    data: BrizziPaymentResponse,
    onFinish: () -> Unit = {}
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val graphicsLayer = rememberGraphicsLayer()

    val writeStorageAccessState = rememberMultiplePermissionsState(
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            emptyList()
        } else {
            listOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    )

    val density = LocalDensity.current

    fun shareBitmapFromComposable(isShare: Boolean) {
        if (writeStorageAccessState.allPermissionsGranted) {
            coroutineScope.launch {
                // val bitmap = graphicsLayer.toImageBitmap()
                val bitmap = composableToBitmap(
                    coroutineScope,
                    context as ComponentActivity,
                    440.dp,
                    density
                ) {
                    MainTheme {
                        ReceiptView(data)
                    }
                }
                val uri = bitmap.saveToDisk(context)
                if (isShare) {
                    shareBitmap(context, uri)
                }
            }
        } else if (writeStorageAccessState.shouldShowRationale) {
            coroutineScope.launch {
                val result = snackbarHostState.showSnackbar(
                    message = "The storage permission is needed to save the image",
                    actionLabel = "Grant Access"
                )

                if (result == SnackbarResult.ActionPerformed) {
                    writeStorageAccessState.launchMultiplePermissionRequest()
                }
            }
        } else {
            writeStorageAccessState.launchMultiplePermissionRequest()
        }
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .drawWithCache {
                onDrawWithContent {
                    graphicsLayer.record {
                        <EMAIL>()
                    }
                    drawLayer(graphicsLayer)
                }
            },
        bottomBar = {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.drawable.receipt_icon_share),
                    contentDescription = null,
                    modifier = Modifier
                        .size(48.dp)
                        .clickable {
                            shareBitmapFromComposable(true)
                        },
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.width(12.dp))

                Image(
                    painter = painterResource(R.drawable.receipt_icon_download),
                    contentDescription = null,
                    modifier = Modifier
                        .size(48.dp)
                        .clickable {
                            shareBitmapFromComposable(false)
                        },
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.width(12.dp))

                PrimaryButton(
                    label = "Selesai",
                    modifier = Modifier.fillMaxWidth()
                ) {
                    onFinish()
                }
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .paint(
                    painter = painterResource(R.drawable.receipt_background),
                    sizeToIntrinsics = false,
                    contentScale = ContentScale.Crop
                )
                .verticalScroll(rememberScrollState())
                .padding(innerPadding)
                .padding(16.dp)
                .padding(top = 16.dp)
        ) {
            Image(
                painter = painterResource(R.drawable.receipt_image_success),
                contentDescription = null,
                modifier = Modifier
                    .size(120.dp)
                    .align(Alignment.CenterHorizontally),
                contentScale = ContentScale.Fit
            )

            Text(
                text = data.title.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                color = Color.White,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.titleLarge
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = data.totalDataView?.firstOrNull()?.value.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                color = Color.White,
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.headlineLarge
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = data.headerDataView?.firstOrNull()?.value.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                color = Color.White,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(24.dp))

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(16.dp))
                    .padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.image_tabungan),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape),
                        contentScale = ContentScale.Crop
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    val source = data.dataViewTransaction?.firstOrNull()?.value?.split("\n")
                    val sourceName = source?.firstOrNull().orEmpty()
                    val sourceValue = source?.lastOrNull().orEmpty()
                    Column(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = sourceName,
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(2.dp))

                        Text(
                            text = sourceValue,
                            modifier = Modifier.fillMaxWidth(),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.receipt_icon_arrow),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                            .size(16.dp),
                        contentScale = ContentScale.Fit
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    DividerHorizontal()
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.image_tabungan),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape),
                        contentScale = ContentScale.Crop
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Column(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = data.dataViewTransaction?.getOrNull(1)?.value.orEmpty(),
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(2.dp))

                        Text(
                            text = data.dataViewTransaction?.getOrNull(2)?.value.orEmpty(),
                            modifier = Modifier.fillMaxWidth(),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(16.dp))
                    .padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                var showDetail by rememberSaveable { mutableStateOf(false) }
                val textDetail by remember {
                    derivedStateOf {
                        if (showDetail) "Sembunyikan" else "Lihat Detail"
                    }
                }
                val iconDetail by remember {
                    derivedStateOf {
                        if (showDetail) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown
                    }
                }

                Text(
                    text = "Detail Transaksi",
                    modifier = Modifier.fillMaxWidth(),
                    fontWeight = FontWeight.SemiBold,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(12.dp))

                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = data.headerDataView?.getOrNull(1)?.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = data.headerDataView?.getOrNull(1)?.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))

                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = data.dataViewTransaction?.getOrNull(1)?.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = data.dataViewTransaction?.getOrNull(1)?.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))

                DividerHorizontal()

                Spacer(modifier = Modifier.height(12.dp))

                data.amountDataView?.forEachIndexed { index, item ->
                    Row(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = item?.name.orEmpty(),
                            modifier = Modifier.weight(1f),
                            color = Color_7B90A6,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = item?.value.orEmpty(),
                            modifier = Modifier.weight(1f),
                            textAlign = TextAlign.End,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    if (index < data.amountDataView.size - 1) {
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                }

                if (showDetail) {
                    Spacer(modifier = Modifier.height(12.dp))

                    Text(
                        text = "Informasi Hubungi Call Center 789",
                        modifier = Modifier.fillMaxWidth(),
                        color = Color_7B90A6,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodySmall
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "Biaya Termasuk PPN (Apabila Dikenakan/Apabila Ada)\n" +
                            "PT. Bank Rakyat Indonesia (Persero) Tbk.\n" +
                            "Kantor Pusat BRI - Jakarta Pusat\n" +
                            "NPWP : 01.001.608.7-093.000",
                        modifier = Modifier.fillMaxWidth(),
                        color = Color_7B90A6,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .clickable {
                            showDetail = !showDetail
                        },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = textDetail,
                        modifier = Modifier.padding(start = 4.dp),
                        color = Color_0054F3,
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodySmall
                    )

                    Spacer(modifier = Modifier.width(4.dp))

                    Icon(
                        imageVector = iconDetail,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                        tint = Color_0054F3
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            Text(
                text = "Produk Pilihan Untukmu",
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                style = MaterialTheme.typography.bodyLarge
            )

            Spacer(modifier = Modifier.height(16.dp))

            LazyRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(2) {
                    Column(
                        modifier = Modifier
                            .width(250.dp)
                            .background(Color.White, RoundedCornerShape(16.dp))
                            .padding(16.dp)
                    ) {
                        Image(
                            painter = painterResource(R.drawable.thumbnail),
                            contentDescription = null,
                            modifier = Modifier.size(32.dp),
                            contentScale = ContentScale.Crop
                        )

                        Spacer(modifier = Modifier.height(12.dp))

                        Row(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = "Token BRIZZI",
                                modifier = Modifier.weight(1f),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.width(4.dp))

                            Text(
                                text = "Cashback 100rb",
                                modifier = Modifier
                                    .background(Color_FDEDED, RoundedCornerShape(16.dp))
                                    .padding(horizontal = 8.dp, vertical = 2.dp),
                                color = Color_E84040,
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.labelSmall
                            )
                        }

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = "Beli token BRIZZI di Qitta, langsung dapet Cashback s.d. 100rb!",
                            modifier = Modifier.fillMaxWidth(),
                            style = MaterialTheme.typography.bodySmall
                        )

                        Spacer(modifier = Modifier.height(12.dp))

                        Text(
                            text = "Beli Sekarang",
                            color = Color_0054F3,
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun ReceiptTransferContent(
    data: TransferPayResponse,
    onFinish: () -> Unit = {}
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.SUCCESS) }
    val graphicsLayer = rememberGraphicsLayer()

    val writeStorageAccessState = rememberMultiplePermissionsState(
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            emptyList()
        } else {
            listOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    )

    fun shareBitmapFromComposable(isShare: Boolean) {
        if (writeStorageAccessState.allPermissionsGranted) {
            coroutineScope.launch {
                val bitmap = graphicsLayer.toImageBitmap()
                val uri = bitmap.asAndroidBitmap().saveToDisk(context)
                if (isShare) {
                    shareBitmap(context, uri)
                } else {
                    snackbarType = SnackbarType.SUCCESS
                    snackbarHostState.showSnackbar(
                        message = "Bukti pembayaran berhasil diunduh."
                    )
                }
            }
        } else if (writeStorageAccessState.shouldShowRationale) {
            coroutineScope.launch {
                snackbarType = SnackbarType.ERROR
                val result = snackbarHostState.showSnackbar(
                    message = "The storage permission is needed to save the image",
                    actionLabel = "Grant Access"
                )

                if (result == SnackbarResult.ActionPerformed) {
                    writeStorageAccessState.launchMultiplePermissionRequest()
                }
            }
        } else {
            writeStorageAccessState.launchMultiplePermissionRequest()
        }
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .drawWithCache {
                onDrawWithContent {
                    graphicsLayer.record {
                        <EMAIL>()
                    }
                    drawLayer(graphicsLayer)
                }
            },
        bottomBar = {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.drawable.receipt_icon_share),
                    contentDescription = null,
                    modifier = Modifier
                        .size(48.dp)
                        .clickable {
                            shareBitmapFromComposable(true)
                        },
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.width(12.dp))

                Image(
                    painter = painterResource(R.drawable.receipt_icon_download),
                    contentDescription = null,
                    modifier = Modifier
                        .size(48.dp)
                        .clickable {
                            shareBitmapFromComposable(false)
                        },
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.width(12.dp))

                PrimaryButton(
                    label = "Selesai",
                    modifier = Modifier.fillMaxWidth()
                ) {
                    onFinish()
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.receipt_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .verticalScroll(rememberScrollState())
                    .padding(innerPadding)
                    .padding(16.dp)
                    .padding(top = 16.dp)
            ) {
                Image(
                    painter = painterResource(R.drawable.receipt_image_success),
                    contentDescription = null,
                    modifier = Modifier
                        .size(120.dp)
                        .align(Alignment.CenterHorizontally),
                    contentScale = ContentScale.Fit
                )

                Text(
                    text = data.title.orEmpty(),
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.titleLarge
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = data.totalDataView?.firstOrNull()?.value.orEmpty(),
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.headlineLarge
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = data.dateTransaction,
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White, RoundedCornerShape(16.dp))
                        .padding(horizontal = 16.dp, vertical = 12.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(R.drawable.image_tabungan),
                            contentDescription = null,
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape),
                            contentScale = ContentScale.Crop
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        val source = data.dataViewTransaction?.firstOrNull()?.value?.split("\n")
                        val sourceName = source?.firstOrNull().orEmpty()
                        val sourceValue = source?.lastOrNull().orEmpty()
                        Column(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = data.sourceAccountDataView.title,
                                modifier = Modifier.fillMaxWidth(),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(2.dp))

                            Text(
                                text = "${data.sourceAccountDataView.subtitle.substringAfter(" ")} " +
                                    "- ${data.sourceAccountDataView.description}",
                                modifier = Modifier.fillMaxWidth(),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(R.drawable.receipt_icon_arrow),
                            contentDescription = null,
                            modifier = Modifier
                                .padding(horizontal = 8.dp, vertical = 4.dp)
                                .size(16.dp),
                            contentScale = ContentScale.Fit
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        DividerHorizontal()
                    }

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(R.drawable.image_tabungan),
                            contentDescription = null,
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape),
                            contentScale = ContentScale.Crop
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Column(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = data.billingDetail.title,
                                modifier = Modifier.fillMaxWidth(),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(2.dp))

                            Text(
                                text = "${data.billingDetail.subtitle.substringAfter(" ")} " +
                                    "- ${data.billingDetail.description}",
                                modifier = Modifier.fillMaxWidth(),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White, RoundedCornerShape(16.dp))
                        .padding(horizontal = 16.dp, vertical = 12.dp)
                ) {
                    var showDetail by rememberSaveable { mutableStateOf(false) }
                    val textDetail by remember {
                        derivedStateOf {
                            if (showDetail) "Sembunyikan" else "Lihat Detail"
                        }
                    }
                    val iconDetail by remember {
                        derivedStateOf {
                            if (showDetail) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown
                        }
                    }

                    Text(
                        text = "Detail Transaksi",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    Row(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = data.headerDataView.getOrNull(0)?.name.orEmpty(),
                            modifier = Modifier.weight(1f),
                            color = Color_7B90A6,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = data.headerDataView.getOrNull(0)?.value.orEmpty(),
                            modifier = Modifier.weight(1f),
                            textAlign = TextAlign.End,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    Row(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = data.dataViewTransaction.getOrNull(0)?.name.orEmpty(),
                            modifier = Modifier.weight(1f),
                            color = Color_7B90A6,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = data.dataViewTransaction.getOrNull(0)?.value.orEmpty(),
                            modifier = Modifier.weight(1f),
                            textAlign = TextAlign.End,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    if (showDetail) {
                        Row(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = data.dataViewTransaction.getOrNull(1)?.name.orEmpty(),
                                modifier = Modifier.weight(1f),
                                color = Color_7B90A6,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Text(
                                text = data.dataViewTransaction.getOrNull(1)?.value.orEmpty(),
                                modifier = Modifier.weight(1f),
                                textAlign = TextAlign.End,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    DividerHorizontal()

                    Spacer(modifier = Modifier.height(12.dp))

                    data.amountDataView?.forEachIndexed { index, item ->
                        Row(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = item.name.orEmpty(),
                                modifier = Modifier.weight(1f),
                                color = Color_7B90A6,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Text(
                                text = item.value.orEmpty(),
                                modifier = Modifier.weight(1f),
                                textAlign = TextAlign.End,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }

                        if (index < data.amountDataView.size - 1) {
                            Spacer(modifier = Modifier.height(12.dp))
                        }
                    }

                    if (showDetail) {
                        Spacer(modifier = Modifier.height(12.dp))

                        Text(
                            text = "Informasi Hubungi Call Center 789",
                            modifier = Modifier.fillMaxWidth(),
                            color = Color_7B90A6,
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodySmall
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = "Biaya Termasuk PPN (Apabila Dikenakan/Apabila Ada)\n" +
                                "PT. Bank Rakyat Indonesia (Persero) Tbk.\n" +
                                "Kantor Pusat BRI - Jakarta Pusat\n" +
                                "NPWP : 01.001.608.7-093.000",
                            modifier = Modifier.fillMaxWidth(),
                            color = Color_7B90A6,
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .clickable {
                                showDetail = !showDetail
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = textDetail,
                            modifier = Modifier.padding(start = 4.dp),
                            color = Color_0054F3,
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodySmall
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        Icon(
                            imageVector = iconDetail,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                            tint = Color_0054F3
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                Text(
                    text = "Produk Pilihan Untukmu",
                    modifier = Modifier.fillMaxWidth(),
                    fontWeight = FontWeight.SemiBold,
                    style = MaterialTheme.typography.bodyLarge
                )

                Spacer(modifier = Modifier.height(16.dp))

                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(2) {
                        Column(
                            modifier = Modifier
                                .width(250.dp)
                                .background(Color.White, RoundedCornerShape(16.dp))
                                .padding(16.dp)
                        ) {
                            Image(
                                painter = painterResource(R.drawable.thumbnail),
                                contentDescription = null,
                                modifier = Modifier.size(32.dp),
                                contentScale = ContentScale.Crop
                            )

                            Spacer(modifier = Modifier.height(12.dp))

                            Row(modifier = Modifier.fillMaxWidth()) {
                                Text(
                                    text = "Token BRIZZI",
                                    modifier = Modifier.weight(1f),
                                    fontWeight = FontWeight.SemiBold,
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Spacer(modifier = Modifier.width(4.dp))

                                Text(
                                    text = "Cashback 100rb",
                                    modifier = Modifier
                                        .background(Color_FDEDED, RoundedCornerShape(16.dp))
                                        .padding(horizontal = 8.dp, vertical = 2.dp),
                                    color = Color_E84040,
                                    fontWeight = FontWeight.SemiBold,
                                    style = MaterialTheme.typography.labelSmall
                                )
                            }

                            Spacer(modifier = Modifier.height(4.dp))

                            Text(
                                text = "Beli token BRIZZI di Qitta, langsung dapet Cashback s.d. 100rb!",
                                modifier = Modifier.fillMaxWidth(),
                                style = MaterialTheme.typography.bodySmall
                            )

                            Spacer(modifier = Modifier.height(12.dp))

                            Text(
                                text = "Beli Sekarang",
                                color = Color_0054F3,
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }
        }
    }
}

@Preview(heightDp = 1080)
@Composable
private fun PreviewReceipt() {
    MainTheme {
/*        ReceiptContent(
            data = BrizziPaymentResponse(
                immediatelyFlag = true,
                headerDataView = listOf(
                    BrizziPaymentResponse.DataView(
                        name = "Tanggal",
                        value = "04 Jul 2025 | 09:20:38 WIB"
                    ),
                    BrizziPaymentResponse.DataView(
                        name = "No Ref",
                        value = "323529112691"
                    )
                ),
                dataViewTransaction = listOf(
                    BrizziPaymentResponse.DataView(
                        name = "Sumber Dana",
                        value = "Infinite\n0230 **** **** 308"
                    ),
                    BrizziPaymentResponse.DataView(
                        name = "Jenis Transaksi",
                        value = "Top Up Deposit BRIZZI"
                    ),
                    BrizziPaymentResponse.DataView(
                        name = "Nomor Kartu",
                        value = "6013 5098 0000 0640"
                    )
                ),
                amountDataView = listOf(
                    BrizziPaymentResponse.DataView(
                        name = "Nominal",
                        value = "Rp20.000"
                    ),
                    BrizziPaymentResponse.DataView(
                        name = "Biaya Admin",
                        value = "Rp0"
                    )
                ),
                totalDataView = listOf(
                    BrizziPaymentResponse.DataView(
                        name = "Total",
                        value = "Rp20.000"
                    )
                ),
                share = true,
                title = "Transaksi Berhasil",
                titleImage = "receipt_00",
                footer = "Silakan lakukan aktivasi deposit kartu BRIZZI Anda.",
                rowDataShow = 3,
                referenceNumber = "323529112691",
                cardNumber = "",
                activatedBalance = "",
                key = "",
                rcHost = "",
                reff = "",
                pendingBalance = ""
            )
        )*/
    }
}
