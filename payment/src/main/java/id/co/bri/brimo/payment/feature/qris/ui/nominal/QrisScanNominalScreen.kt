package id.co.bri.brimo.payment.feature.qris.ui.nominal

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.QrisNominalRoute
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.ImageAsync
import id.co.bri.brimo.payment.core.design.component.PrimaryButton
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.TextFieldCustom
import id.co.bri.brimo.payment.core.design.component.textFieldColors
import id.co.bri.brimo.payment.core.design.helper.ThousandSeparatorTransformation
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E6EEFF
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.errorSnackbar
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse
import id.co.bri.brimo.payment.core.network.response.qris.InputDataFrom
import id.co.bri.brimo.payment.core.network.response.qris.QrisScanResponse
import id.co.bri.brimo.payment.core.network.response.qris.TipOption
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import id.co.bri.brimo.payment.feature.qris.ui.base.QrisAccountBottomSheet
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun QrisScanNominalScreen(
    navigation: (QrisNominalNavigation) -> Unit = {},
    qrisNominalViewModel: QrisNominalViewModel = koinViewModel()
) {
    val accountList by qrisNominalViewModel.accountList.collectAsStateWithLifecycle()
    val qrisConfirmation by qrisNominalViewModel.qrisConfirmation.collectAsStateWithLifecycle()

    if (qrisNominalViewModel.qrisModel != null) {
        QrisScanNominalContent(
            state = QrisNominalState(
                qrisNominalRoute = qrisNominalViewModel.qrisNominalRoute,
                qrisModel = qrisNominalViewModel.qrisModel,
                accountList = accountList.orEmpty(),
                qrisConfirmation = qrisConfirmation
            ),
            event = qrisNominalViewModel::handleEvent,
            navigation = navigation
        )
    }
}

@Composable
private fun QrisScanNominalContent(
    state: QrisNominalState,
    event: (QrisNominalEvent) -> Unit = {},
    navigation: (QrisNominalNavigation) -> Unit = {}
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current

    // Global
    var nominalField by rememberSaveable { mutableStateOf("") }
    var tipField by rememberSaveable { mutableStateOf("") }
    var noteField by rememberSaveable { mutableStateOf("") }
    val counterNote by remember {
        derivedStateOf {
            "${noteField.length}/30"
        }
    }
    val minimumTransaction =
        state.qrisModel.qrisScan?.minimumTransaction?.toDoubleOrNull() ?: 1.0
    val isMin by remember {
        derivedStateOf {
            nominalField.isNotEmpty() &&
                (nominalField.toDoubleOrNull() ?: 0.0) +
                (tipField.toDoubleOrNull() ?: 0.0) < minimumTransaction
        }
    }
    val isMax by remember {
        derivedStateOf {
            nominalField.isNotEmpty() &&
                (nominalField.toDoubleOrNull() ?: 0.0) +
                (tipField.toDoubleOrNull() ?: 0.0) > ********
        }
    }
    val enableButton by remember {
        derivedStateOf {
            nominalField.isNotEmpty() &&
                (nominalField.toDoubleOrNull() ?: 0.0) +
                (tipField.toDoubleOrNull() ?: 0.0) >= minimumTransaction &&
                (nominalField.toDoubleOrNull() ?: 0.0) +
                (tipField.toDoubleOrNull() ?: 0.0) <= ********
        }
    }
    var selectedAccount: AccountResponse? by remember {
        mutableStateOf(state.accountList.find { it.default == 1 }
            ?: state.accountList.firstOrNull())
    }
    val notEnough = (selectedAccount?.balance?.toDoubleOrNull() ?: 0.0) <
        (nominalField.toDoubleOrNull() ?: 0.0) + (tipField.toDoubleOrNull() ?: 0.0) &&
        !state.qrisNominalRoute.fastMenu

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            event(
                QrisNominalEvent.Confirmation(
                    accountNumber = selectedAccount?.account.orEmpty(),
                    amount = nominalField,
                    tipAmount = tipField,
                    note = noteField
                )
            )
        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Account
    var accountBottomSheet by rememberSaveable { mutableStateOf(false) }

    if (state.accountList.isNotEmpty()) {
        BottomSheet(
            showBottomSheet = accountBottomSheet,
            onShowBottomSheet = { accountBottomSheet = it }
        ) { dismiss ->
            QrisAccountBottomSheet(
                selectedAccount = selectedAccount?.account.orEmpty(),
                nominal = nominalField,
                data = state.accountList,
                onSelect = { item ->
                    dismiss {
                        selectedAccount = item
                    }
                },
                onRefresh = { item ->
                    event(QrisNominalEvent.RefreshSaldo(item.account.orEmpty()))
                },
                onClose = {
                    dismiss {}
                },
                fastMenu = state.qrisNominalRoute.fastMenu
            )
        }
    }

    // Qris Scan Confirmation
    LaunchedEffect(state.qrisConfirmation) {
        state.qrisConfirmation
            .onLoading {
                progressDialog = true
            }
            .onSuccess { data ->
                progressDialog = false
                val qrisModel = QrisModel(
                    openPayment = state.qrisModel.openPayment,
                    typeQr = state.qrisModel.typeQr,
                    qrisConfirmation = data,
                    accountNumber = selectedAccount?.account,
                    accountImage = selectedAccount?.imagePath
                )
                val qrisData = qrisModel.serialize().orEmpty()
                if (qrisData.isEmpty()) {
                    errorBottomSheet = true
                } else {
                    navigation(
                        QrisNominalNavigation.Confirmation(
                            qrisData = qrisData,
                            fastMenu = state.qrisNominalRoute.fastMenu
                        )
                    )
                }
                event(QrisNominalEvent.ResetConfirmation)
            }
            .onError { e ->
                progressDialog = false
                if (e is MessageException && e.errorSnackbar()) {
                    snackbarType = SnackbarType.ERROR
                    snackbarHostState.showSnackbar(e.description)
                } else {
                    error = e
                    errorBottomSheet = true
                }
                event(QrisNominalEvent.ResetConfirmation)
            }
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .pointerInput(Unit) {
                detectTapGestures {
                    focusManager.clearFocus()
                }
            },
        bottomBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, top = 16.dp, end = 16.dp)
                        .clickable {
                            accountBottomSheet = true
                        },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = selectedAccount?.imagePath.orEmpty(),
                        contentDescription = null,
                        modifier = Modifier
                            .width(58.dp)
                            .height(36.dp),
                        placeholder = painterResource(id = R.drawable.thumbnail),
                        error = painterResource(id = R.drawable.thumbnail),
                        contentScale = ContentScale.Inside
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    if (state.qrisNominalRoute.fastMenu) {
                        Text(
                            text = selectedAccount?.accountString.orEmpty(),
                            modifier = Modifier.weight(1f),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    } else {
                        var hide by rememberSaveable { mutableStateOf(false) }
                        val icon = if (hide) {
                            R.drawable.icon_hide_eye
                        } else {
                            R.drawable.icon_unhide_eye
                        }
                        val accountString = selectedAccount?.accountString.orEmpty()
                        val account = if (hide) {
                            "${accountString.take(4)} **** ****${accountString.takeLast(4)}"
                        } else {
                            accountString
                        }
                        val balance = if (hide) {
                            "••••••"
                        } else {
                            selectedAccount?.balanceString.orEmpty()
                        }
                        val balanceError = selectedAccount?.balance == null
                        val color = if (balanceError || notEnough) Color_E84040 else Color.Black

                        Column(modifier = Modifier.weight(1f)) {
                            Row(
                                modifier = Modifier.clickable {
                                    hide = !hide
                                },
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = account,
                                    style = MaterialTheme.typography.labelSmall
                                )

                                if (!balanceError) {
                                    Spacer(modifier = Modifier.width(4.dp))

                                    Image(
                                        painter = painterResource(icon),
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp),
                                        contentScale = ContentScale.Fit
                                    )
                                }
                            }

                            Row(verticalAlignment = Alignment.CenterVertically) {
                                if (!balanceError) {
                                    Text(
                                        text = selectedAccount?.currency.orEmpty() + balance,
                                        color = color,
                                        fontWeight = FontWeight.SemiBold,
                                        style = MaterialTheme.typography.bodyMedium
                                    )

                                    Spacer(modifier = Modifier.width(8.dp))
                                } else {
                                    Text(
                                        text = "Gagal memuat saldo",
                                        color = Color_E84040,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.width(12.dp))

                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp)
                    )
                }

                PrimaryButton(
                    label = "Lanjutkan",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    enabled = enableButton && !notEnough
                ) {
                    focusManager.clearFocus()
                    event(
                        QrisNominalEvent.Confirmation(
                            accountNumber = selectedAccount?.account.orEmpty(),
                            amount = nominalField,
                            tipAmount = tipField,
                            note = noteField
                        )
                    )
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(QrisNominalNavigation.Back)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "Scan QRIS",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Penerima",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        ImageAsync(
                            context = context,
                            url = state.qrisModel.qrisScan?.billingDetailOpen?.firstOrNull()?.iconPath.orEmpty(),
                            initial = state.qrisModel.qrisScan?.billingDetailOpen?.firstOrNull()?.title.orEmpty(),
                            size = 32
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Column(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = state.qrisModel.qrisScan?.billingDetailOpen?.firstOrNull()?.title.orEmpty(),
                                modifier = Modifier.fillMaxWidth(),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(2.dp))

                            val subtitle =
                                state.qrisModel.qrisScan?.billingDetailOpen?.firstOrNull()?.subtitle.orEmpty()
                            val description =
                                state.qrisModel.qrisScan?.billingDetailOpen?.firstOrNull()?.description.orEmpty()

                            Text(
                                text = "$subtitle - $description",
                                modifier = Modifier.fillMaxWidth(),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    var isFocusedNominal by rememberSaveable { mutableStateOf(false) }
                    val borderColorNominal = if (isMin || isMax || notEnough) {
                        Color_E84040
                    } else if (isFocusedNominal) {
                        Color_0054F3
                    } else {
                        Color.Transparent
                    }

                    Box(modifier = Modifier.fillMaxWidth()) {
                        Column(modifier = Modifier.zIndex(1f)) {
                            Text(
                                text = "Nominal",
                                modifier = Modifier.padding(start = 16.dp, top = 16.dp),
                                style = MaterialTheme.typography.bodySmall
                            )

                            Text(
                                text = "Rp",
                                modifier = Modifier.padding(start = 16.dp),
                                style = MaterialTheme.typography.headlineSmall.copy(
                                    fontWeight = FontWeight.SemiBold
                                )
                            )
                        }

                        TextFieldCustom(
                            value = nominalField,
                            onValueChange = { input ->
                                val digit = input.filter { it.isDigit() }
                                val nominal = digit.toDoubleOrNull() ?: 0.0
                                nominalField = if (nominal in 1.0..99999999.0) {
                                    digit
                                } else {
                                    ""
                                }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(80.dp)
                                .onFocusChanged { state ->
                                    isFocusedNominal = state.isFocused
                                }
                                .border(1.dp, borderColorNominal, RoundedCornerShape(16.dp)),
                            textStyle = MaterialTheme.typography.headlineSmall.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            label = {
                                Text(text = "")
                            },
                            trailingIcon = if (nominalField.isNotEmpty()) {
                                {
                                    Image(
                                        painter = painterResource(R.drawable.icon_close),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .size(16.dp)
                                            .clickable {
                                                nominalField = ""
                                            },
                                        contentScale = ContentScale.Fit
                                    )
                                }
                            } else {
                                null
                            },
                            visualTransformation = ThousandSeparatorTransformation(),
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Number
                            ),
                            singleLine = true,
                            shape = RoundedCornerShape(16.dp),
                            colors = textFieldColors(),
                            contentPadding = TextFieldDefaults.contentPaddingWithLabel(
                                start = 48.dp,
                                top = 16.dp
                            )
                        )
                    }

                    val errorText = when {
                        isMin -> "Nominal minimal untuk transaksi adalah ${state.qrisModel.qrisScan?.minimumTransactionString ?: "Rp1"}"
                        isMax -> "Transaksi melebihi batas maksimum Rp10.000.000"
                        notEnough -> "Saldo Tidak Cukup"
                        else -> ""
                    }

                    if (errorText.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = errorText,
                            color = Color_E84040,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    if (state.qrisModel.qrisScan?.tipEditable == true) {
                        Text(
                            text = "Tip Merchant (Opsional)",
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        var isFocusedTip by rememberSaveable { mutableStateOf(false) }
                        val borderColorTip = if (isFocusedTip) {
                            Color_0054F3
                        } else {
                            Color.Transparent
                        }

                        Box(modifier = Modifier.fillMaxWidth()) {
                            Column(modifier = Modifier.zIndex(1f)) {
                                Text(
                                    text = "Nominal Tip",
                                    modifier = Modifier.padding(start = 16.dp, top = 16.dp),
                                    style = MaterialTheme.typography.bodySmall
                                )

                                Text(
                                    text = "Rp",
                                    modifier = Modifier.padding(start = 16.dp),
                                    style = MaterialTheme.typography.headlineSmall.copy(
                                        fontWeight = FontWeight.SemiBold
                                    )
                                )
                            }

                            TextFieldCustom(
                                value = tipField,
                                onValueChange = { input ->
                                    val digit = input.filter { it.isDigit() }
                                    val nominal = digit.toDoubleOrNull() ?: 0.0
                                    tipField = if (nominal in 1.0..99999999.0) {
                                        digit
                                    } else {
                                        ""
                                    }
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(80.dp)
                                    .onFocusChanged { state ->
                                        isFocusedTip = state.isFocused
                                    }
                                    .border(1.dp, borderColorTip, RoundedCornerShape(16.dp)),
                                textStyle = MaterialTheme.typography.headlineSmall.copy(
                                    fontWeight = FontWeight.SemiBold
                                ),
                                label = {
                                    Text(text = "")
                                },
                                trailingIcon = if (tipField.isNotEmpty()) {
                                    {
                                        Image(
                                            painter = painterResource(R.drawable.icon_close),
                                            contentDescription = null,
                                            modifier = Modifier
                                                .size(16.dp)
                                                .clickable {
                                                    tipField = ""
                                                },
                                            contentScale = ContentScale.Fit
                                        )
                                    }
                                } else {
                                    null
                                },
                                visualTransformation = ThousandSeparatorTransformation(),
                                keyboardOptions = KeyboardOptions(
                                    keyboardType = KeyboardType.Number
                                ),
                                singleLine = true,
                                shape = RoundedCornerShape(16.dp),
                                colors = textFieldColors(),
                                contentPadding = TextFieldDefaults.contentPaddingWithLabel(
                                    start = 48.dp,
                                    top = 16.dp
                                )
                            )
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState()),
                        ) {
                            state.qrisModel.qrisScan.tipOption?.forEachIndexed { index, tip ->
                                val selected = tipField == (tip.value ?: 0).toString()
                                val backgroundColor = if (selected) Color_E6EEFF else Color_F5F7FB
                                val textColor = if (selected) Color_0054F3 else Color.Black

                                Text(
                                    text = tip.text.orEmpty(),
                                    modifier = Modifier
                                        .background(backgroundColor, RoundedCornerShape(100))
                                        .clickable {
                                            tipField = (tip.value ?: 0).toString()
                                        }
                                        .padding(horizontal = 16.dp, vertical = 8.dp),
                                    color = textColor,
                                    style = MaterialTheme.typography.bodySmall
                                )

                                Spacer(modifier = Modifier.width(12.dp))
                            }
                        }

                        Spacer(modifier = Modifier.height(24.dp))
                    }

                    var isFocusedNote by rememberSaveable { mutableStateOf(false) }
                    val borderColorNote = if (isFocusedNote) {
                        Color_0054F3
                    } else {
                        Color.Transparent
                    }

                    TextFieldCustom(
                        value = noteField,
                        onValueChange = {
                            if (it.length <= 30) {
                                noteField = it
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(64.dp)
                            .onFocusChanged { state ->
                                isFocusedNote = state.isFocused
                            }
                            .border(1.dp, borderColorNote, RoundedCornerShape(16.dp)),
                        textStyle = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.SemiBold
                        ),
                        label = {
                            Text(text = "Catatan (Opsional)")
                        },
                        trailingIcon = if (noteField.isNotEmpty()) {
                            {
                                Image(
                                    painter = painterResource(R.drawable.icon_close),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(16.dp)
                                        .clickable {
                                            noteField = ""
                                        },
                                    contentScale = ContentScale.Fit
                                )
                            }
                        } else {
                            null
                        },
                        singleLine = true,
                        shape = RoundedCornerShape(16.dp),
                        colors = textFieldColors(),
                        contentPadding = TextFieldDefaults.contentPaddingWithLabel(top = 12.dp)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = counterNote,
                        modifier = Modifier.align(Alignment.End),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodySmall
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.systemBars))
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewQrisScanNominal() {
    MainTheme {
        QrisScanNominalContent(
            state = QrisNominalState(
                qrisNominalRoute = QrisNominalRoute(
                    data = "",
                    type = "",
                    fastMenu = false
                ),
                qrisModel = QrisModel(
                    qrisScan = QrisScanResponse(
                        accountList = listOf(),
                        billingDetail = listOf(),
                        billingDetailOpen = listOf(
                            BillingResponse(
                                listType = "",
                                iconName = "",
                                iconPath = "",
                                title = "Title",
                                subtitle = "Subtitle",
                                description = "Description"
                            )
                        ),
                        billingAmount = listOf(),
                        billingAmountDetail = listOf(),
                        inputDataFrom = InputDataFrom(
                            countryName = "",
                            countryCode = "",
                            currencyCode = "",
                            iconName = "",
                            iconPath = "",
                            inputName = "",
                            inputValue = 0
                        ),
                        information = DataViewResponse(
                            name = "",
                            value = "",
                            style = ""
                        ),
                        openPayment = true,
                        isBilling = false,
                        minimumPayment = false,
                        amountEditable = true,
                        rowDataShow = 0,
                        saved = "",
                        amount = "",
                        amountString = "",
                        minimumAmount = "",
                        minimumAmountString = "",
                        adminFee = "",
                        adminFeeString = "",
                        payAmount = "",
                        payAmountString = "",
                        minimumTransaction = "",
                        minimumTransactionString = "",
                        tipOption = listOf(
                            TipOption(
                                text = "Rp1.000",
                                value = 1000
                            ),
                            TipOption(
                                text = "Rp5.000",
                                value = 5000
                            ),
                            TipOption(
                                text = "Rp10.000",
                                value = 10000
                            ),
                            TipOption(
                                text = "Rp15.000",
                                value = 15000
                            ),
                            TipOption(
                                text = "Rp20.000",
                                value = 20000
                            ),
                            TipOption(
                                text = "Rp25.000",
                                value = 25000
                            )
                        ),
                        tipEditable = true,
                        tipType = "",
                        tipAmount = "",
                        tipAmountString = "",
                        typeQr = "qris_mpm",
                        merchantName = "",
                        merchantCountry = "",
                        nomorTiket = "",
                        referenceNumber = ""
                    )
                ),
                accountList = listOf(
                    AccountResponse(
                        account = "***************",
                        accountString = "1234 5678 9012 345",
                        name = "Name",
                        currency = "Rp",
                        cardNumber = "****************",
                        cardNumberString = "0987 XXXX XXXX 8765",
                        productType = "",
                        accountType = "",
                        scCode = "",
                        default = 0,
                        alias = "Alias",
                        minimumBalance = "",
                        limit = "",
                        limitString = "",
                        imageName = "",
                        imagePath = "",
                        onHold = false,
                        balance = "9000000",
                        balanceString = "9.000.000,00"
                    )
                )
            )
        )
    }
}
