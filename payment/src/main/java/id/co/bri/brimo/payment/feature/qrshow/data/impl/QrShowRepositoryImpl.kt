package id.co.bri.brimo.payment.feature.qrshow.data.impl

import id.co.bri.brimo.payment.core.network.processApi
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrshow.QrCpmGenerateRequest
import id.co.bri.brimo.payment.core.network.response.base.AccountListResponse
import id.co.bri.brimo.payment.core.network.response.base.SaldoNormalResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmCheckStatusResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmGenerateResponse
import id.co.bri.brimo.payment.dependency.PaymentDependency
import id.co.bri.brimo.payment.feature.qrshow.data.api.QrShowRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

internal class QrShowRepositoryImpl(
    private val ioDispatcher: CoroutineDispatcher
) : QrShowRepository {

    override suspend fun postAccountList(fastMenu: Boolean): AccountListResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "j1EEkMeQ0/HX28L5CI1tREbX2GgbvC2xNJ9KzPRllBs="
            } else {
                "6XcHuzXPT8Uh/Pggd+tfUdYwWu7OSbxYs/WHKsVq6qA="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = "",
                    fastMenu = fastMenu
                )
            }
        }

    override suspend fun postSaldoNormal(
        request: SaldoNormalRequest
    ): SaldoNormalResponse =
        withContext(ioDispatcher) {
            val url = "gnUBRK3Mc0ogiZdhmA6hBg=="
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }

    override suspend fun postQrCpmGenerate(
        request: QrCpmGenerateRequest,
        fastMenu: Boolean
    ): QrCpmGenerateResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "oQOpupCmuhxrSReW6H+ajAWcch2tnJ1uW3b/DFgsaWM="
            } else {
                "096RmcX35Bbcz/bEEog1SgtjJUPKYosFiheQimGrXN8="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = fastMenu
                )
            }
        }

    override suspend fun postQrCpmCheckStatus(fastMenu: Boolean): QrCpmCheckStatusResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "1JsieBMGkX5SljoDKde76dazUON+VHCCMK75F8gGpjY="
            } else {
                "3lSrHEx14xc2rvM9mhXpPAp3c71zeFvNwpTYAIEntNA="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = "",
                    fastMenu = fastMenu
                )
            }
        }
}
