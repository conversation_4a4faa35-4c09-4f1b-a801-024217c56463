package id.co.bri.brimo.payment.feature.qris.ui.nominal

import id.co.bri.brimo.payment.app.QrisNominalRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qris.QrisConfirmationResponse
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel

internal data class QrisNominalState(
    val qrisNominalRoute: QrisNominalRoute,
    val qrisModel: QrisModel = QrisModel(),
    val accountList: List<AccountResponse> = emptyList(),
    val qrisConfirmation: UiState<QrisConfirmationResponse> = UiState.Init
)

internal sealed class QrisNominalEvent {
    data class Confirmation(
        val accountNumber: String,
        val amount: String,
        val tipAmount: String,
        val note: String
    ) : QrisNominalEvent()

    object ResetConfirmation : QrisNominalEvent()
    data class RefreshSaldo(val account: String) : QrisNominalEvent()
}

internal sealed class QrisNominalNavigation {
    object Back : QrisNominalNavigation()
    data class Confirmation(val qrisData: String, val fastMenu: Boolean) : QrisNominalNavigation()
}
