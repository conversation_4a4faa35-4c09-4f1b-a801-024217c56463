package id.co.bri.brimo.payment.feature.qris.ui.park.confirmation

import id.co.bri.brimo.payment.app.QrisParkConfirmationRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qris.QrisParkConfirmationResponse
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel

internal data class QrisParkConfirmationState(
    val qrisParkConfirmationRoute: QrisParkConfirmationRoute,
    val qrisModel: QrisModel = QrisModel(),
    val accountList: List<AccountResponse> = emptyList(),
    val qrisConfirmation: UiState<QrisParkConfirmationResponse> = UiState.Init
)

internal sealed class QrisParkConfirmationEvent {
    data class Confirmation(
        val accountNumber: String,
        val pin: String
    ) : QrisParkConfirmationEvent()

    object ResetConfirmation : QrisParkConfirmationEvent()
    data class RefreshSaldo(val account: String) : QrisParkConfirmationEvent()
}

internal sealed class QrisParkConfirmationNavigation {
    data class Status(val qrisData: String) : QrisParkConfirmationNavigation()
}
