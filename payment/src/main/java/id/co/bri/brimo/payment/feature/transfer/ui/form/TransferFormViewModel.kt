package id.co.bri.brimo.payment.feature.transfer.ui.form

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.refresh
import id.co.bri.brimo.payment.core.network.request.AddFavoriteRequest
import id.co.bri.brimo.payment.core.network.request.FavoriteRequest
import id.co.bri.brimo.payment.core.network.request.InquiryAddFavoriteRequest
import id.co.bri.brimo.payment.core.network.request.UpdateFavoriteRequest
import id.co.bri.brimo.payment.core.network.response.TransferFormData
import id.co.bri.brimo.payment.core.network.response.base.FavoriteResponse
import id.co.bri.brimo.payment.feature.transfer.data.api.TransferRepository
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

internal class TransferFormViewModel(
    private val transferRepository: TransferRepository,
//    savedStateHandle: SavedStateHandle
) : ViewModel() {

//    val transferFormRoute = savedStateHandle.toRoute<TransferFormRoute>()

    private val _state = MutableStateFlow(
        TransferFormState(
            bankList = listOf()
        )
    )
    val state: StateFlow<TransferFormState> = _state.asStateFlow()

    private val _selectedFavorite = MutableStateFlow<FavoriteResponse?>(null)
    val selectedFavorite = _selectedFavorite.asStateFlow()

    private val _navigation = MutableSharedFlow<TransferFormNavigation>()
    val navigation = _navigation.asSharedFlow()

    private val _processPayment = MutableSharedFlow<UiState<Unit>>()
    val processPayment = _processPayment.asSharedFlow()

    private val refreshTransferForm = MutableStateFlow(false)
    private fun refreshTransferForm() = refreshTransferForm.refresh()

    private val _transferFormInitData = MutableStateFlow<UiState<TransferFormData>?>(null)
    val transferFormInitData = _transferFormInitData.asStateFlow()

    private val _transferFormData = MutableStateFlow<UiState<TransferFormData>?>(null)
    val transferFormData = _transferFormData.asStateFlow()

    private val _inquiryAddFavoriteData = MutableStateFlow<UiState<Boolean>?>(null)
    val inquiryAddFavoriteData = _inquiryAddFavoriteData.asStateFlow()

    private val _addFavoriteData = MutableStateFlow<UiState<Boolean>?>(null)
    val addFavoriteData = _addFavoriteData.asStateFlow()

    private val _updateFavoriteData = MutableStateFlow<UiState<Boolean>?>(null)
    val updateFavoriteData = _updateFavoriteData.asStateFlow()

    private val _pinFavoriteData = MutableStateFlow<UiState<Boolean>?>(null)
    val pinFavoriteData = _pinFavoriteData.asStateFlow()

    private val _unpinFavoriteData = MutableStateFlow<UiState<Boolean>?>(null)
    val unpinFavoriteData = _unpinFavoriteData.asStateFlow()

    private val _deleteFavoriteData = MutableStateFlow<UiState<Boolean>?>(null)
    val deleteFavoriteData = _deleteFavoriteData.asStateFlow()

//    val transferForm = refreshTransferForm.flatMapLatest {
//        asFlowUiState {
//            transferRepository.postTransferForm(transferFormRoute.fastMenu)
//        }
//    }.uiState(viewModelScope)

    fun handleEvent(event: TransferFormEvent) {
        when (event) {
            TransferFormEvent.GetTransferFormData -> {

            }

            is TransferFormEvent.RefreshTransferForm -> {
                postTransferFormInit(event.isFastMenu)
            }

            is TransferFormEvent.InquiryAddFavorite -> {
                postInquiryAddFavorite(request = event.request)
            }

            is TransferFormEvent.AddFavorite -> {
                postAddFavorite(request = event.request)
            }

            is TransferFormEvent.UpdateFavorite -> {
                postUpdateFavorite(request = event.request)
            }

            is TransferFormEvent.PinFavorite -> {
                postPinFavorite(request = event.request)
            }

            is TransferFormEvent.UnpinFavorite -> {
                postUnpinFavorite(request = event.request)
            }

            is TransferFormEvent.DeleteFavorite -> {
                postDeleteFavorite(request = event.request)
            }
        }
    }

    fun postTransferFormInit(isFastMenu: Boolean) {
        viewModelScope.launch {
            _transferFormInitData.emit(UiState.Loading)
            try {
                val response = transferRepository.postTransferForm(isFastMenu)
                _transferFormInitData.emit(UiState.Success(response))
            } catch (e: Throwable) {
                _transferFormInitData.emit(UiState.Error(e))
            }
        }
    }

    fun postTransferForm(isFastMenu: Boolean) {
        viewModelScope.launch {
            _transferFormData.emit(UiState.Loading)
            try {
                val response = transferRepository.postTransferForm(isFastMenu)
                _transferFormData.emit(UiState.Success(response))
            } catch (e: Throwable) {
                _transferFormData.emit(UiState.Error(e))
            }
        }
    }

    fun postInquiryAddFavorite(
        request: InquiryAddFavoriteRequest
    ) {
        viewModelScope.launch {
            _inquiryAddFavoriteData.emit(UiState.Loading)
            try {
                val response = transferRepository.postInquiryAddFavorite(
                    request = request
                )
                _inquiryAddFavoriteData.emit(UiState.Success(true))
            } catch (e: Throwable) {
                _inquiryAddFavoriteData.emit(UiState.Error(e))
            }
        }
    }

    fun postAddFavorite(
        request: AddFavoriteRequest
    ) {
        viewModelScope.launch {
            _addFavoriteData.emit(UiState.Loading)
            try {
                val response = transferRepository.postAddFavorite(
                    request = request
                )
                _addFavoriteData.emit(UiState.Success(true))
            } catch (e: Throwable) {
                _addFavoriteData.emit(UiState.Error(e))
            }
        }
    }

    fun postUpdateFavorite(
        request: UpdateFavoriteRequest
    ) {
        viewModelScope.launch {
            _updateFavoriteData.emit(UiState.Loading)
            try {
                val response = transferRepository.postUpdateFavorite(
                    request = request
                )
                _updateFavoriteData.emit(UiState.Success(true))
            } catch (e: Throwable) {
                _updateFavoriteData.emit(UiState.Error(e))
            }
        }
    }

    fun postPinFavorite(
        request: FavoriteRequest
    ) {
        viewModelScope.launch {
            _pinFavoriteData.emit(UiState.Loading)
            try {
                val response = transferRepository.postPinFavorite(
                    request = request
                )
                _pinFavoriteData.emit(UiState.Success(true))
            } catch (e: Throwable) {
                _pinFavoriteData.emit(UiState.Error(e))
            }
        }
    }

    fun postUnpinFavorite(
        request: FavoriteRequest
    ) {
        viewModelScope.launch {
            _unpinFavoriteData.emit(UiState.Loading)
            try {
                val response = transferRepository.postUnpinFavorite(
                    request = request
                )
                _unpinFavoriteData.emit(UiState.Success(true))
            } catch (e: Throwable) {
                _unpinFavoriteData.emit(UiState.Error(e))
            }
        }
    }

    fun postDeleteFavorite(
        request: FavoriteRequest
    ) {
        viewModelScope.launch {
            _deleteFavoriteData.emit(UiState.Loading)
            try {
                val response = transferRepository.postDeleteFavorite(
                    request = request
                )
                _deleteFavoriteData.emit(UiState.Success(true))
            } catch (e: Throwable) {
                _deleteFavoriteData.emit(UiState.Error(e))
            }
        }
    }

    fun navigateBack() {
        viewModelScope.launch {
            _navigation.emit(TransferFormNavigation.Back)
        }
    }

    fun clearTransferForm() {
        viewModelScope.launch {
            _transferFormData.emit(null)
            _transferFormInitData.emit(null)
        }
    }

    fun setSelectedFavorite(favorite: FavoriteResponse?) {
        viewModelScope.launch {
            _selectedFavorite.emit(favorite)
        }
    }

    fun clearSelectedFavorite() {
        viewModelScope.launch {
            _selectedFavorite.emit(null)
        }
    }

    fun successAddFavoriteState() {
        viewModelScope.launch {
            _addFavoriteData.emit(UiState.Success(true))
        }
    }

    fun successEditFavoriteState() {
        viewModelScope.launch {
            _updateFavoriteData.emit(UiState.Success(true))
        }
    }

    fun clearFavoriteState() {
        viewModelScope.launch {
            _addFavoriteData.emit(UiState.Init)
            _updateFavoriteData.emit(UiState.Init)
            _unpinFavoriteData.emit(UiState.Init)
            _pinFavoriteData.emit(UiState.Init)
            _deleteFavoriteData.emit(UiState.Init)
        }
    }
} 