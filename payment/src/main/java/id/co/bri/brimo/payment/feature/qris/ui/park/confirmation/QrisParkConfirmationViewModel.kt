package id.co.bri.brimo.payment.feature.qris.ui.park.confirmation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrisParkConfirmationRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qris.QrisParkConfirmationRequest
import id.co.bri.brimo.payment.core.network.response.qris.QrisParkConfirmationResponse
import id.co.bri.brimo.payment.feature.qris.data.api.QrisRepository
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrisParkConfirmationViewModel(
    private val qrisRepository: QrisRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrisParkConfirmationRoute = savedStateHandle.toRoute<QrisParkConfirmationRoute>()

    val qrisModel = runCatching {
        Gson().fromJson(qrisParkConfirmationRoute.data, QrisModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(qrisModel?.qrisScan?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrisParkConfirmationEvent) {
        when (event) {
            is QrisParkConfirmationEvent.Confirmation -> {
                postQrisParkConfirmation(
                    accountNumber = event.accountNumber,
                    pin = event.pin
                )
            }

            QrisParkConfirmationEvent.ResetConfirmation -> {
                resetConfirmation()
            }

            is QrisParkConfirmationEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _qrisParkConfirmation =
        MutableStateFlow<UiState<QrisParkConfirmationResponse>>(UiState.Init)
    val qrisParkConfirmation = _qrisParkConfirmation.asStateFlow()

    private fun resetConfirmation() {
        viewModelScope.launch {
            _qrisParkConfirmation.update { UiState.Init }
        }
    }

    private fun postQrisParkConfirmation(
        accountNumber: String,
        pin: String
    ) {
        viewModelScope.launch {
            _qrisParkConfirmation.asUiState {
                qrisRepository.postQrisParkConfirmation(
                    request = QrisParkConfirmationRequest(
                        pin = pin,
                        referenceNumber = qrisModel?.qrisScan?.referenceNumber.orEmpty(),
                        accountNumber = accountNumber
                    ),
                    fastMenu = qrisParkConfirmationRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrisRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
