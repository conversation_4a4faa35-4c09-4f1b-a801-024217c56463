package id.co.bri.brimo.payment.app

import kotlinx.serialization.Serializable

// <PERSON><PERSON><PERSON>
@Serializable
internal data class BrizziFormRoute(
    val fastMenu: Boolean
)

@Serializable
internal data class EditFavoriteRoute(
    val number: String,
    val name: String,
    val savedId: String
)

@Serializable
internal data class BrizziNominalRoute(
    val data: String,
    val fastMenu: Boolean,
    val fromScan: Boolean
)

@Serializable
internal data class ConfirmationRoute(
    val data: String,
    val fastMenu: Boolean,
    val fromScan: Boolean
)

@Serializable
internal data class ReceiptRoute(
    val data: String
)

// Briva
@Serializable
internal data class BrivaFormRoute(
    val fastMenu: Boolean
)

@Serializable
internal data class BrivaFavoriteRoute(
    val number: String,
    val name: String,
    val savedId: String,
    val subtitle: String
)

@Serializable
internal data class BrivaNominalRoute(
    val data: String,
    val fastMenu: Boolean
)

@Serializable
internal data class BrivaConfirmationRoute(
    val data: String,
    val fastMenu: Boolean
)

@Serializable
internal data class ReceiptBrivaRoute(
    val data: String,
    val type: String
)

// Qris
@Serializable
internal data class QrTransferRoute(
    val fastMenu: Boolean
)

@Serializable
internal data class QrShowRoute(
    val fastMenu: Boolean
)

@Serializable
internal data class QrTapRoute(
    val data: String,
    val fastMenu: Boolean
)

@Serializable
internal data class TransferFormRoute(
    val fastMenu: Boolean
)

@Serializable
internal data class TransferInputNominalRoute(
    val imageUrl: String,
    val favName: String = ""
)

@Serializable
internal data class TransferConfirmationRoute(
    val isRtgs: Boolean,
    val isFav: Boolean
)

@Serializable
internal data class InputAccountNumberRoute(
    val bankCode: String,
    val bankName: String,
    val imageUrl: String,
    val isAddFavorite: Boolean = false
)

@Serializable
internal data class EditFavoriteTransferRoute(
    val isAddFavorite: Boolean = false
)

// Qris
@Serializable
internal data class QrisScanRoute(
    val fastMenu: Boolean
)

@Serializable
internal data class QrisNominalRoute(
    val data: String,
    val type: String,
    val fastMenu: Boolean
)

@Serializable
internal data class QrisConfirmationRoute(
    val data: String,
    val fastMenu: Boolean
)

@Serializable
internal data class ReceiptQrisRoute(
    val data: String,
    val type: String
)

@Serializable
internal data class QrisParkConfirmationRoute(
    val data: String,
    val fastMenu: Boolean
)

@Serializable
internal data class QrisParkStatusRoute(
    val data: String
)

@Serializable
internal data class ReceiptDailyBankingRoute(
    val data: String
)
