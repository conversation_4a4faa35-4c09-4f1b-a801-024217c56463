package id.co.bri.brimo.payment.feature.qrshow.data.api

import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrshow.QrCpmGenerateRequest
import id.co.bri.brimo.payment.core.network.response.base.AccountListResponse
import id.co.bri.brimo.payment.core.network.response.base.SaldoNormalResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmCheckStatusResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmGenerateResponse

internal interface QrShowRepository {

    suspend fun postAccountList(fastMenu: Boolean): AccountListResponse

    suspend fun postSaldoNormal(request: SaldoNormalRequest): SaldoNormalResponse

    suspend fun postQrCpmGenerate(
        request: QrCpmGenerateRequest,
        fastMenu: Boolean
    ): QrCpmGenerateResponse

    suspend fun postQrCpmCheckStatus(fastMenu: Boolean): QrCpmCheckStatusResponse
}
