package id.co.bri.brimo.payment.feature.qrtap.ui

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrTapRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrtap.QrTapPayloadRequest
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapCheckStatusResponse
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapPayloadResponse
import id.co.bri.brimo.payment.feature.qrtap.data.api.QrTapRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrTapViewModel(
    private val qrTapRepository: QrTapRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrTapRoute = savedStateHandle.toRoute<QrTapRoute>()

    private val payload = runCatching {
        Gson().fromJson(qrTapRoute.data, QrTapPayloadResponse::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(payload?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrTapEvent) {
        when (event) {
            is QrTapEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }

            is QrTapEvent.Payload -> {
                postQrTapPayload(
                    pin = event.pin,
                    account = event.account,
                    cardToken = event.cardToken
                )
            }

            QrTapEvent.CheckStatus -> {
                postQrTapCheckStatus()
            }

            QrTapEvent.ResetCheckStatus -> {
                resetQrTapCheckStatus()
            }
        }
    }

    private val _qrTapPayload =
        MutableStateFlow<UiState<QrTapPayloadResponse?>>(UiState.Success(payload))
    val qrTapPayload = _qrTapPayload.asStateFlow()

    private fun postQrTapPayload(pin: String, account: String, cardToken: String) {
        viewModelScope.launch {
            _qrTapPayload.asUiState {
                val payload = qrTapRepository.postQrTapPayload(
                    request = QrTapPayloadRequest(
                        pin = pin,
                        account = account,
                        cardToken = cardToken,
                        nfcType = payload?.nfcType.orEmpty()
                    ),
                    fastMenu = qrTapRoute.fastMenu
                )

                val accountList = try {
                    qrTapRepository.postAccountList(fastMenu = qrTapRoute.fastMenu).account
                } catch (_: Throwable) {
                    payload.accountList
                }

                val accountListUpdated = if (!qrTapRoute.fastMenu) {
                    accountList?.map { account ->
                        async {
                            try {
                                val saldoNormal = qrTapRepository.postSaldoNormal(
                                    request = SaldoNormalRequest(
                                        account = account.account.orEmpty()
                                    )
                                )
                                account.copy(
                                    onHold = saldoNormal.onHold,
                                    balance = saldoNormal.balance,
                                    balanceString = saldoNormal.balanceString
                                )
                            } catch (_: Throwable) {
                                account
                            }
                        }
                    }?.awaitAll()
                } else {
                    accountList
                }

                _accountList.update { accountListUpdated }

                val payloadUpdated = payload.copy(accountList = accountListUpdated)

                payloadUpdated
            }
        }
    }

    private val _qrTapCheckStatus =
        MutableStateFlow<UiState<QrTapCheckStatusResponse>>(UiState.Init)
    val qrTapCheckStatus = _qrTapCheckStatus.asStateFlow()

    private fun resetQrTapCheckStatus() {
        viewModelScope.launch {
            _qrTapCheckStatus.update { UiState.Init }
        }
    }

    private fun postQrTapCheckStatus() {
        viewModelScope.launch {
            _qrTapCheckStatus.asUiState {
                qrTapRepository.postQrTapCheckStatus(fastMenu = qrTapRoute.fastMenu)
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrTapRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
