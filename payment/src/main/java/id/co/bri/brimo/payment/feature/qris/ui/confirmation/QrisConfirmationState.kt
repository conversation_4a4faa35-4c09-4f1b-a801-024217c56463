package id.co.bri.brimo.payment.feature.qris.ui.confirmation

import id.co.bri.brimo.payment.app.QrisConfirmationRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qris.QrisPaymentResponse
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel

internal data class QrisConfirmationState(
    val qrisConfirmationRoute: QrisConfirmationRoute,
    val qrisModel: QrisModel = QrisModel(),
    val accountList: List<AccountResponse> = emptyList(),
    val qrisPayment: UiState<QrisPaymentResponse> = UiState.Init,
)

internal sealed class QrisConfirmationEvent {
    data class Payment(
        val pin: String,
        val accountNumber: String,
        val note: String
    ) : QrisConfirmationEvent()

    object ResetPayment : QrisConfirmationEvent()
    data class RefreshSaldo(val account: String) : QrisConfirmationEvent()
}

internal sealed class QrisConfirmationNavigation {
    data class Payment(val qrisData: String, val type: String) : QrisConfirmationNavigation()
}
