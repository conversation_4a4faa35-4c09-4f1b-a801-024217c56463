package id.co.bri.brimo.payment.feature.briva.data.impl

import id.co.bri.brimo.payment.core.network.processApi
import id.co.bri.brimo.payment.core.network.processApiUnit
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaConfirmationRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaFavoriteRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaInquiryRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaPaymentRequest
import id.co.bri.brimo.payment.core.network.response.base.AccountListResponse
import id.co.bri.brimo.payment.core.network.response.base.SaldoNormalResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaConfirmationResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaFormResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaInquiryResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaPaymentResponse
import id.co.bri.brimo.payment.dependency.PaymentDependency
import id.co.bri.brimo.payment.feature.briva.data.api.BrivaRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

internal class BrivaRepositoryImpl(
    private val ioDispatcher: CoroutineDispatcher
) : BrivaRepository {

    override suspend fun postBrivaForm(fastMenu: Boolean): BrivaFormResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "7n2KY5arQKdEIwmxNVukJUflTboZSgOtd+pQ5kiHYaI="
            } else {
                "NLTwcRrqV1oG8XwCFWZF8w=="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = "",
                    fastMenu = fastMenu
                )
            }
        }

    override suspend fun postBrivaFavorite(request: BrivaFavoriteRequest) =
        withContext(ioDispatcher) {
            val url = "Yb5FrJBF3lw7YwEBAR9nRlsPVk89hZGVi/+Sp6RuO4k="
            processApiUnit {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }

    override suspend fun postBrivaRemoveFavorite(request: BrivaFavoriteRequest) =
        withContext(ioDispatcher) {
            val url = "L/mfs/o4FyQk7EsM+TrsVX95m81pST+yApUKxlFLyRxhBiud55kwpv3ga2YuCKy8"
            processApiUnit {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }

    override suspend fun postBrivaDelete(request: BrivaFavoriteRequest) =
        withContext(ioDispatcher) {
            val url = "23R5zTUuhiOlZTMUwzxsGNnmph7Tg1riok2cAQwerNU="
            processApiUnit {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }

    override suspend fun postBrivaUpdateNickname(request: BrivaFavoriteRequest) =
        withContext(ioDispatcher) {
            val url = "VExGAqhMxMVBpxQiOQNuha85Qjmfi63ub3A2PGkPuKo="
            processApiUnit {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }

    override suspend fun postBrivaInquiry(
        request: BrivaInquiryRequest,
        fastMenu: Boolean
    ): BrivaInquiryResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "CHIpkkrQbLHhzUgD1PZRzUmOIW0tcmcKJdd7BEwK2OY="
            } else {
                "SaR05fhwPLbj9TPLDF4fTr109gFuclx3IPoKO74Sv48="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = fastMenu
                )
            }
        }

    override suspend fun postAccountList(fastMenu: Boolean): AccountListResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "j1EEkMeQ0/HX28L5CI1tREbX2GgbvC2xNJ9KzPRllBs="
            } else {
                "6XcHuzXPT8Uh/Pggd+tfUdYwWu7OSbxYs/WHKsVq6qA="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = "",
                    fastMenu = fastMenu
                )
            }
        }

    override suspend fun postSaldoNormal(request: SaldoNormalRequest): SaldoNormalResponse =
        withContext(ioDispatcher) {
            val url = "gnUBRK3Mc0ogiZdhmA6hBg=="
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }

    override suspend fun postBrivaConfirmation(
        request: BrivaConfirmationRequest,
        fastMenu: Boolean
    ): BrivaConfirmationResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "3Ys49y9huSlBH1vf0mN16uHjsiNDpeq5tw2yqgBGYhY="
            } else {
                "JK/QXxO3iteqmzecb8micctWgzrjare2VN1xktahJ6I="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = fastMenu
                )
            }
        }

    override suspend fun postBrivaPay(
        request: BrivaPaymentRequest,
        fastMenu: Boolean
    ): BrivaPaymentResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "DJHiJCe2d9RZM9oqv9gUMQ=="
            } else {
                "BARY8EKOVtaCE+ubiMdIrQ=="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = fastMenu
                )
            }
        }
}
