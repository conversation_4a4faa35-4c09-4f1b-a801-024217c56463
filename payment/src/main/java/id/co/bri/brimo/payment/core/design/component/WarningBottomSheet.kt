package id.co.bri.brimo.payment.core.design.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme

@Composable
internal fun WarningBottomSheet(
    showBottomSheet: Boolean = true,
    onShowBottomSheet: (Boolean) -> Unit = {},
    title: String = "",
    description: String = "",
    primaryText: String = "",
    secondaryText: String = "",
    onPrimary: () -> Unit = {},
    onSecondary: () -> Unit = {},
    onClose: () -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    BottomSheet(
        showBottomSheet = showBottomSheet,
        onShowBottomSheet = onShowBottomSheet,
        onDismiss = onDismiss
    ) { dismiss ->
        WarningContent(
            title = title,
            description = description,
            primaryText = primaryText,
            secondaryText = secondaryText,
            onPrimary = {
                dismiss {
                    onPrimary()
                }
            },
            onSecondary = {
                dismiss {
                    onSecondary()
                }
            },
            onClose = {
                dismiss {
                    onClose()
                }
            }
        )
    }
}

@Composable
private fun WarningContent(
    title: String = "",
    description: String = "",
    primaryText: String = "",
    secondaryText: String = "",
    onPrimary: () -> Unit = {},
    onSecondary: () -> Unit = {},
    onClose: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Box(modifier = Modifier.fillMaxWidth()) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = null,
                modifier = Modifier
                    .background(Color_F5F7FB, CircleShape)
                    .clickable {
                        onClose()
                    }
                    .padding(8.dp)
                    .align(Alignment.TopEnd),
                tint = Color.Black
            )

            Image(
                painter = painterResource(R.drawable.image_error),
                contentDescription = null,
                modifier = Modifier
                    .size(200.dp)
                    .align(Alignment.TopCenter),
                contentScale = ContentScale.Fit
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = title,
            modifier = Modifier.fillMaxWidth(),
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyLarge
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = description,
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyMedium
        )

        Spacer(modifier = Modifier.height(32.dp))

        if (primaryText.isNotEmpty()) {
            PrimaryButton(
                label = primaryText,
                modifier = Modifier.fillMaxWidth(),
                onClick = onPrimary
            )
        }

        if (primaryText.isNotEmpty() && secondaryText.isNotEmpty()) {
            Spacer(modifier = Modifier.height(12.dp))
        }

        if (secondaryText.isNotEmpty()) {
            SecondaryButton(
                label = secondaryText,
                modifier = Modifier.fillMaxWidth(),
                onClick = onSecondary
            )
        }
    }
}

@Preview
@Composable
private fun PreviewWarningBottomSheet() {
    MainTheme {
        WarningContent(
            title = "Title",
            description = "Description",
            primaryText = "Primary",
            secondaryText = "Secondary"
        )
    }
}
