package id.co.bri.brimo.payment.feature.qrtransfer.ui

import android.Manifest
import android.os.Build
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.paint
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asAndroidBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.layer.drawLayer
import androidx.compose.ui.graphics.rememberGraphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.OutlinedTextFieldCustom
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SecondaryButton
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.WarningBottomSheet
import id.co.bri.brimo.payment.core.design.component.saveToDisk
import id.co.bri.brimo.payment.core.design.component.shareBitmap
import id.co.bri.brimo.payment.core.design.helper.ThousandSeparatorTransformation
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.errorSnackbar
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.base.AlertResponse
import id.co.bri.brimo.payment.core.network.response.base.PopupResponse
import id.co.bri.brimo.payment.core.network.response.qrtransfer.QrTransferGenerateResponse
import id.co.bri.brimo.payment.feature.brizzi.ui.receipt.generateQrCode
import id.co.bri.brimo.payment.feature.qris.ui.base.QrisAccountBottomSheet
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun QrTransferScreen(
    navigation: (QrTransferNavigation) -> Unit = {},
    qrTransferViewModel: QrTransferViewModel = koinViewModel()
) {
    val accountList by qrTransferViewModel.accountList.collectAsStateWithLifecycle()
    val qrAccountList by qrTransferViewModel.qrAccountList.collectAsStateWithLifecycle()
    val qrTransferGenerate by qrTransferViewModel.qrTransferGenerate.collectAsStateWithLifecycle()

    qrAccountList
        .onLoading {
            ProgressDialog()
        }
        .onSuccess { data ->
            QrTransferContent(
                state = QrTransferState(
                    accountList = accountList.orEmpty(),
                    qrTransferGenerate = qrTransferGenerate
                ),
                event = qrTransferViewModel::handleEvent,
                navigation = navigation
            )
        }
        .onError { error ->
            ErrorBottomSheet(
                error = error,
                onDismiss = {
                    navigation(QrTransferNavigation.Back)
                },
                onClose = {
                    navigation(QrTransferNavigation.Back)
                },
                onRetry = {
                    qrTransferViewModel.handleEvent(QrTransferEvent.RefreshAccountList)
                }
            )
        }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun QrTransferContent(
    state: QrTransferState,
    event: (QrTransferEvent) -> Unit = {},
    navigation: (QrTransferNavigation) -> Unit = {}
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val graphicsLayer = rememberGraphicsLayer()
    val density = LocalDensity.current
    val focusManager = LocalFocusManager.current

    // Global
    var nominalField by rememberSaveable { mutableStateOf("") }
    var selectedAccount: AccountResponse? by remember {
        mutableStateOf(state.accountList.find { it.default == 1 }
            ?: state.accountList.firstOrNull())
    }
    var changeAccount by rememberSaveable { mutableStateOf(false) }
    var hideDetail by rememberSaveable { mutableStateOf(false) }

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            event(
                QrTransferEvent.Generate(
                    accountNumber = selectedAccount?.account.orEmpty(),
                    amount = nominalField
                )
            )
        }
    )

    // Warning
    var warningBottomSheet by rememberSaveable { mutableStateOf(false) }
    var titleWarning by rememberSaveable { mutableStateOf("") }
    var descriptionWarning by rememberSaveable { mutableStateOf("") }

    WarningBottomSheet(
        showBottomSheet = warningBottomSheet,
        onShowBottomSheet = { warningBottomSheet = it },
        title = titleWarning,
        description = descriptionWarning,
        primaryText = "Mengerti"
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Account
    var accountBottomSheet by rememberSaveable { mutableStateOf(false) }

    if (state.accountList.isNotEmpty()) {
        BottomSheet(
            showBottomSheet = accountBottomSheet,
            onShowBottomSheet = { accountBottomSheet = it }
        ) { dismiss ->
            QrisAccountBottomSheet(
                selectedAccount = selectedAccount?.account.orEmpty(),
                nominal = nominalField,
                data = state.accountList,
                onSelect = { item ->
                    dismiss {
                        selectedAccount = item
                        changeAccount = true
                        nominalField = ""
                        event(
                            QrTransferEvent.Generate(
                                accountNumber = selectedAccount?.account.orEmpty(),
                                amount = nominalField
                            )
                        )
                    }
                },
                onClose = {
                    dismiss {}
                },
                fastMenu = true
            )
        }
    }

    LaunchedEffect(state.qrTransferGenerate) {
        state.qrTransferGenerate
            .onLoading {
                progressDialog = true
            }
            .onSuccess { data ->
                progressDialog = false
                if (changeAccount) {
                    snackbarType = SnackbarType.SUCCESS
                    snackbarHostState.showSnackbar("Rekening Tujuan berhasil diubah")
                    changeAccount = false
                }
            }
            .onError { e ->
                progressDialog = false
                if (e is MessageException && e.errorSnackbar()) {
                    snackbarType = SnackbarType.ERROR
                    snackbarHostState.showSnackbar(e.description)
                } else {
                    error = e
                    errorBottomSheet = true
                }
            }
    }

    val writeStorageAccessState = rememberMultiplePermissionsState(
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            emptyList()
        } else {
            listOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    )

    fun shareBitmapFromComposable(isShare: Boolean) {
        if (writeStorageAccessState.allPermissionsGranted) {
            coroutineScope.launch {
                try {
                    hideDetail = true
                    delay(500)
                    val bitmap = graphicsLayer.toImageBitmap()
                    val uri = bitmap.asAndroidBitmap().saveToDisk(context)
                    if (isShare) {
                        shareBitmap(context, uri)
                    } else {
                        coroutineScope.launch {
                            snackbarType = SnackbarType.SUCCESS
                            snackbarHostState.showSnackbar("Kode QRIS Transfer berhasil diunduh.")
                        }
                    }
                    hideDetail = false
                } catch (_: Throwable) {
                    if (!isShare) {
                        coroutineScope.launch {
                            snackbarType = SnackbarType.ERROR
                            snackbarHostState.showSnackbar("Kode QRIS Transfer gagal diunduh.")
                        }
                    }
                }
            }
        } else if (writeStorageAccessState.shouldShowRationale) {
            coroutineScope.launch {
                snackbarType = SnackbarType.INFO
                val result = snackbarHostState.showSnackbar(
                    message = "The storage permission is needed to save the image",
                    actionLabel = "Grant Access"
                )

                if (result == SnackbarResult.ActionPerformed) {
                    writeStorageAccessState.launchMultiplePermissionRequest()
                }
            }
        } else {
            writeStorageAccessState.launchMultiplePermissionRequest()
        }
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .pointerInput(Unit) {
                detectTapGestures {
                    focusManager.clearFocus()
                }
            },
        bottomBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.receipt_icon_share),
                        contentDescription = null,
                        modifier = Modifier
                            .size(48.dp)
                            .clickable {
                                shareBitmapFromComposable(true)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Image(
                        painter = painterResource(R.drawable.receipt_icon_download),
                        contentDescription = null,
                        modifier = Modifier
                            .size(48.dp)
                            .clickable {
                                shareBitmapFromComposable(false)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    SecondaryButton(
                        label = "Buat QRIS Baru",
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        if (state.qrTransferGenerate is UiState.Success) {
                            val data = state.qrTransferGenerate.data

                            if ((nominalField.toDoubleOrNull() ?: 0.0) <
                                (data.minNominal?.toDoubleOrNull() ?: 0.0) &&
                                nominalField.isNotEmpty()
                            ) {
                                titleWarning = data.popupMinTransaction?.title.orEmpty()
                                descriptionWarning = data.popupMinTransaction?.description.orEmpty()
                                warningBottomSheet = true
                            } else if ((nominalField.toDoubleOrNull() ?: 0.0) >
                                (data.maxNominal?.toDoubleOrNull() ?: 0.0) &&
                                nominalField.isNotEmpty()
                            ) {
                                titleWarning = data.popupMinTransaction?.title.orEmpty()
                                descriptionWarning = data.popupMinTransaction?.description.orEmpty()
                                warningBottomSheet = true
                            } else {
                                event(
                                    QrTransferEvent.Generate(
                                        accountNumber = selectedAccount?.account.orEmpty(),
                                        amount = nominalField
                                    )
                                )
                            }
                        } else {
                            event(
                                QrTransferEvent.Generate(
                                    accountNumber = selectedAccount?.account.orEmpty(),
                                    amount = nominalField
                                )
                            )
                        }
                    }
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(QrTransferNavigation.Back)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "QRIS Transfer",
                        modifier = Modifier
                            .weight(1f)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )

                    /*Image(
                        painter = painterResource(R.drawable.icon_history),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(QrTransferNavigation.History)
                            },
                        contentScale = ContentScale.Fit
                    )*/
                }

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .drawWithCache {
                                onDrawWithContent {
                                    graphicsLayer.record {
                                        <EMAIL>()
                                    }
                                    drawLayer(graphicsLayer)
                                }
                            }
                    ) {
                        Image(
                            painter = painterResource(R.drawable.qris_background),
                            contentDescription = null,
                            modifier = Modifier
                                .fillMaxWidth()
                                .wrapContentHeight()
                                .align(Alignment.TopCenter),
                            contentScale = ContentScale.Crop
                        )

                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .align(Alignment.TopCenter)
                                .padding(24.dp)
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 16.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painter = painterResource(R.drawable.image_logo),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(64.dp)
                                        .height(32.dp),
                                    contentScale = ContentScale.Fit
                                )

                                Spacer(modifier = Modifier.weight(1f))

                                Image(
                                    painter = painterResource(R.drawable.image_qris),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(64.dp)
                                        .height(32.dp),
                                    contentScale = ContentScale.Fit
                                )
                            }

                            Spacer(modifier = Modifier.height(24.dp))

                            Box(
                                modifier = Modifier
                                    .size(220.dp)
                                    .align(Alignment.CenterHorizontally),
                                contentAlignment = Alignment.Center
                            ) {
                                state.qrTransferGenerate.onSuccess { data ->
                                    if (!data.qrCode.isNullOrEmpty()) {
                                        val sizePx = with(density) { 220.dp.toPx().toInt() }
                                        val bitmap = remember(data.qrCode) {
                                            generateQrCode(data.qrCode, sizePx)
                                        }

                                        Image(
                                            bitmap = bitmap.asImageBitmap(),
                                            contentDescription = null,
                                            modifier = Modifier
                                                .size(220.dp)
                                                .clip(RoundedCornerShape(16.dp)),
                                            contentScale = ContentScale.Crop
                                        )

                                        Image(
                                            painter = painterResource(R.drawable.qr_logo),
                                            contentDescription = null,
                                            modifier = Modifier.size(32.dp),
                                            contentScale = ContentScale.Fit
                                        )
                                    }
                                }
                            }

                            Spacer(modifier = Modifier.height(24.dp))

                            Row(
                                modifier = Modifier
                                    .align(Alignment.CenterHorizontally)
                                    .clickable {
                                        accountBottomSheet = true
                                    },
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = selectedAccount?.alias.orEmpty()
                                        .ifEmpty { selectedAccount?.name.orEmpty() },
                                    color = Color.White,
                                    fontWeight = FontWeight.SemiBold,
                                    style = MaterialTheme.typography.bodyLarge
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                if (!hideDetail) {
                                    Icon(
                                        imageVector = Icons.Default.KeyboardArrowDown,
                                        contentDescription = null,
                                        modifier = Modifier.size(20.dp),
                                        tint = Color.White
                                    )
                                }
                            }

                            Text(
                                text = selectedAccount?.accountString.orEmpty(),
                                modifier = Modifier.align(Alignment.CenterHorizontally),
                                color = Color.White,
                                style = MaterialTheme.typography.bodySmall
                            )

                            Spacer(modifier = Modifier.height(24.dp))

                            if (!hideDetail) {
                                var isFocused by rememberSaveable { mutableStateOf(false) }

                                OutlinedTextFieldCustom(
                                    value = nominalField,
                                    onValueChange = { input ->
                                        val digit = input.filter { it.isDigit() }
                                        val nominal = digit.toDoubleOrNull() ?: 0.0
                                        nominalField = if (nominal in 1.0..********.0) {
                                            digit
                                        } else {
                                            ""
                                        }
                                    },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(32.dp)
                                        .padding(horizontal = 32.dp)
                                        .onFocusChanged { state ->
                                            isFocused = state.isFocused
                                        },
                                    textStyle = MaterialTheme.typography.bodySmall.copy(
                                        color = Color.White,
                                        fontWeight = FontWeight.SemiBold
                                    ),
                                    placeholder = if (!isFocused && nominalField.isEmpty()) {
                                        {
                                            Text(
                                                text = "Nominal Transfer (Opsional)",
                                                modifier = Modifier.fillMaxWidth(),
                                                color = Color.White,
                                                fontWeight = FontWeight.SemiBold,
                                                textAlign = TextAlign.Center,
                                                style = MaterialTheme.typography.bodySmall
                                            )
                                        }
                                    } else {
                                        null
                                    },
                                    prefix = if (isFocused) {
                                        {
                                            Text(
                                                text = "Rp",
                                                style = MaterialTheme.typography.bodySmall.copy(
                                                    color = Color.White,
                                                    fontWeight = FontWeight.SemiBold
                                                )
                                            )
                                        }
                                    } else {
                                        null
                                    },
                                    trailingIcon = if (nominalField.isNotEmpty()) {
                                        {
                                            Icon(
                                                imageVector = Icons.Default.Close,
                                                contentDescription = null,
                                                modifier = Modifier
                                                    .size(16.dp)
                                                    .clickable {
                                                        nominalField = ""
                                                    },
                                                tint = Color.White
                                            )
                                        }
                                    } else {
                                        null
                                    },
                                    visualTransformation = ThousandSeparatorTransformation(),
                                    keyboardOptions = KeyboardOptions(
                                        keyboardType = KeyboardType.Number
                                    ),
                                    singleLine = true,
                                    shape = RoundedCornerShape(100),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedBorderColor = Color.White,
                                        unfocusedBorderColor = Color.White,
                                        disabledBorderColor = Color.White
                                    ),
                                    contentPadding = PaddingValues(horizontal = 16.dp)
                                )
                            }

                            Spacer(modifier = Modifier.height(24.dp))

                            state.qrTransferGenerate.onSuccess { data ->
                                Text(
                                    text = data.mpan.orEmpty(),
                                    modifier = Modifier.align(Alignment.CenterHorizontally),
                                    color = Color.White,
                                    fontWeight = FontWeight.SemiBold,
                                    style = MaterialTheme.typography.bodySmall
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                Text(
                                    text = "Kode QR berlaku hingga ${data.expired}",
                                    modifier = Modifier.align(Alignment.CenterHorizontally),
                                    color = Color.White,
                                    style = MaterialTheme.typography.bodySmall
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                Text(
                                    text = "Hanya untuk 1 kali transaksi",
                                    modifier = Modifier.align(Alignment.CenterHorizontally),
                                    color = Color.White,
                                    style = MaterialTheme.typography.labelSmall
                                )
                            }
                        }
                    }

                    Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.systemBars))
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewQrisTransfer() {
    MainTheme {
        QrTransferContent(
            state = QrTransferState(
                qrTransferGenerate = UiState.Success(
                    data = QrTransferGenerateResponse(
                        amount = 100000,
                        expired = "86400",
                        qrCode = "00020101021240580013ID.CO.BRI.WWW0118936000021000007675021502300113711550752044829530336054061000005802ID5920ADIXXXXXXXXXXXXXXLTI6015KOTA ADM JAKART61051256062340804DMCT99220002000112323549795892630419C5",
                        mpan = "9360000210000076756",
                        title = "Tunjukkan kode QR ini ke pengirim dana.",
                        titleShare = "Scan QR ini untuk melakukan transfer.",
                        description = "",
                        maxNominal = "10000000",
                        minNominal = "10000",
                        footer = "",
                        popupMinTransaction = PopupResponse(
                            title = "Nominal Kurang dari Limit Transaksi",
                            imagePath = "",
                            imageName = "",
                            description = "Minimum nominal untuk menerima transfer adalah Rp10.000."
                        ),
                        popupMaxTransaction = PopupResponse(
                            title = "Nominal Melebihi Limit Transaksi",
                            imagePath = "",
                            imageName = "",
                            description = "Maksimum nominal untuk menerima transfer adalah Rp10.000.000."
                        ),
                        popupGeneralAlert = AlertResponse(
                            title = "Buat Kode QR Baru?",
                            description = "Setelah membuat kode QR baru, QR sebelumnya tidak dapat digunakan lagi. Pastikan transaksi QR ini sudah selesai, ya."
                        ),
                        snackbarMessage = "Nominal ditambahkan dan QR siap dibagikan.",
                        accountList = listOf(),
                        selectedAccount = null
                    )
                ),
                accountList = listOf(
                    AccountResponse(
                        account = "***************",
                        accountString = "1234 5678 9012 345",
                        name = "Name",
                        currency = "Rp",
                        cardNumber = "****************",
                        cardNumberString = "0987 XXXX XXXX 8765",
                        productType = "",
                        accountType = "",
                        scCode = "",
                        default = 0,
                        alias = "Alias",
                        minimumBalance = "",
                        limit = "",
                        limitString = "",
                        imageName = "",
                        imagePath = "",
                        onHold = false,
                        balance = "9000000",
                        balanceString = "9.000.000,00"
                    )
                )
            )
        )
    }
}
