package id.co.bri.brimo.payment.feature.briva.ui.form

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import id.co.bri.brimo.payment.app.BrivaFormRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asFlowUiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.common.refresh
import id.co.bri.brimo.payment.core.common.uiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaFavoriteRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaInquiryRequest
import id.co.bri.brimo.payment.core.network.response.base.FavoriteResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaInquiryResponse
import id.co.bri.brimo.payment.feature.briva.data.api.BrivaRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class BrivaFormViewModel(
    private val brivaRepository: BrivaRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val brivaFormRoute = savedStateHandle.toRoute<BrivaFormRoute>()

    fun handleEvent(event: BrivaFormEvent) {
        when (event) {
            BrivaFormEvent.RefreshBrivaForm -> {
                refreshBrivaForm()
            }

            is BrivaFormEvent.Inquiry -> {
                postBrivaInquiry(number = event.number)
            }

            BrivaFormEvent.ResetInquiry -> {
                resetBrivaInquiry()
            }

            is BrivaFormEvent.PinFavorite -> {
                pinFavorite(savedId = event.savedId, isPin = event.isPin)
            }

            BrivaFormEvent.ResetPinFavorite -> {
                resetPinFavorite()
            }

            is BrivaFormEvent.DeleteFavorite -> {
                deleteFavorite(savedId = event.savedId)
            }

            BrivaFormEvent.ResetDeleteFavorite -> {
                resetDeleteFavorite()
            }

            is BrivaFormEvent.EditFavorite -> {
                editFavorite(savedId = event.savedId, name = event.name)
            }
        }
    }

    private val refreshBrivaForm = MutableStateFlow(false)
    private fun refreshBrivaForm() = refreshBrivaForm.refresh()

    val brivaForm = refreshBrivaForm.flatMapLatest {
        asFlowUiState {
            val form = brivaRepository.postBrivaForm(fastMenu = brivaFormRoute.fastMenu)
            form.saved?.let { favoriteResponse ->
                _favorites.update { favoriteResponse }
            }
            form
        }
    }.uiState(viewModelScope)

    private val _brivaInquiry = MutableStateFlow<UiState<BrivaInquiryResponse>>(UiState.Init)
    val brivaInquiry = _brivaInquiry.asStateFlow()

    private fun resetBrivaInquiry() {
        viewModelScope.launch {
            _brivaInquiry.update { UiState.Init }
        }
    }

    private fun postBrivaInquiry(number: String) {
        viewModelScope.launch {
            _brivaInquiry.asUiState {
                val brivaInquiry = brivaRepository.postBrivaInquiry(
                    request = BrivaInquiryRequest(
                        brivaNumber = number
                    ),
                    fastMenu = brivaFormRoute.fastMenu
                )

                val accountList = try {
                    brivaRepository.postAccountList(fastMenu = brivaFormRoute.fastMenu).account
                } catch (_: Throwable) {
                    brivaInquiry.accountList
                }

                val accountListUpdated = if (!brivaFormRoute.fastMenu) {
                    accountList?.map { account ->
                        async {
                            try {
                                val saldoNormal = brivaRepository.postSaldoNormal(
                                    request = SaldoNormalRequest(
                                        account = account.account.orEmpty()
                                    )
                                )
                                account.copy(
                                    onHold = saldoNormal.onHold,
                                    balance = saldoNormal.balance,
                                    balanceString = saldoNormal.balanceString
                                )
                            } catch (_: Throwable) {
                                account
                            }
                        }
                    }?.awaitAll()
                } else {
                    accountList
                }

                val brivaInquiryUpdated = brivaInquiry.copy(accountList = accountListUpdated)

                brivaInquiryUpdated
            }
        }
    }

    private val _favorites = MutableStateFlow<List<FavoriteResponse>>(listOf())
    val favorites = _favorites.asStateFlow()

    private val _pinFavorite = MutableStateFlow<UiState<Unit>>(UiState.Init)
    val pinFavorite = _pinFavorite.asStateFlow()

    private fun resetPinFavorite() {
        viewModelScope.launch {
            _pinFavorite.update { UiState.Init }
        }
    }

    private fun pinFavorite(savedId: String, isPin: Boolean) {
        viewModelScope.launch {
            _pinFavorite.asUiState {
                if (isPin) {
                    brivaRepository.postBrivaRemoveFavorite(
                        request = BrivaFavoriteRequest(
                            productId = "",
                            savedId = savedId
                        )
                    )
                } else {
                    brivaRepository.postBrivaFavorite(
                        request = BrivaFavoriteRequest(
                            productId = "",
                            savedId = savedId
                        )
                    )
                }
                val updatedFavorite = favorites.value.map { item ->
                    if (item.value == savedId) {
                        item.copy(favorite = !isPin)
                    } else {
                        item
                    }
                }
                _favorites.update { updatedFavorite }
            }
        }
    }

    private val _deleteFavorite = MutableStateFlow<UiState<Unit>>(UiState.Init)
    val deleteFavorite = _deleteFavorite.asStateFlow()

    private fun resetDeleteFavorite() {
        viewModelScope.launch {
            _deleteFavorite.update { UiState.Init }
        }
    }

    private fun deleteFavorite(savedId: String) {
        viewModelScope.launch {
            _deleteFavorite.asUiState {
                brivaRepository.postBrivaDelete(
                    request = BrivaFavoriteRequest(
                        productId = "",
                        savedId = savedId
                    )
                )
                val updatedFavorite = favorites.value.filterNot { it.value == savedId }
                _favorites.update { updatedFavorite }
            }
        }
    }

    private fun editFavorite(savedId: String, name: String) {
        viewModelScope.launch {
            val updatedFavorite = favorites.value.map { item ->
                if (item.value == savedId) {
                    item.copy(title = name)
                } else {
                    item
                }
            }
            _favorites.update { updatedFavorite }
        }
    }
}
