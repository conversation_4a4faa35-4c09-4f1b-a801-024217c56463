package id.co.bri.brimo.payment.feature.qrshow.ui

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import id.co.bri.brimo.payment.app.QrShowRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.common.refresh
import id.co.bri.brimo.payment.core.common.uiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrshow.QrCpmGenerateRequest
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmCheckStatusResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmGenerateResponse
import id.co.bri.brimo.payment.feature.qrshow.data.api.QrShowRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrShowViewModel(
    private val qrShowRepository: QrShowRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrShowRoute = savedStateHandle.toRoute<QrShowRoute>()

    private val _accountList = MutableStateFlow<List<AccountResponse>?>(null)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrShowEvent) {
        when (event) {
            QrShowEvent.RefreshAccountList -> {
                refreshQrAccountList()
            }

            is QrShowEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }

            is QrShowEvent.Generate -> {
                generateQrCpm(accountNumber = event.accountNumber, pin = event.pin)
            }

            is QrShowEvent.CheckStatus -> {
                qrCpmCheckStatus()
            }

            QrShowEvent.ResetCheckStatus -> {
                resetQrCpmCheckStatus()
            }
        }
    }

    private val refreshQrAccountList = MutableStateFlow(false)
    private fun refreshQrAccountList() = refreshQrAccountList.refresh()

    val qrAccountList = refreshQrAccountList.flatMapLatest {
        callbackFlow {
            send(UiState.Loading)
            try {
                val qrAccountList = qrShowRepository.postAccountList(
                    fastMenu = qrShowRoute.fastMenu
                )

                val accountList = if (!qrShowRoute.fastMenu) {
                    qrAccountList.account?.map { account ->
                        async {
                            try {
                                val saldoNormal = qrShowRepository.postSaldoNormal(
                                    request = SaldoNormalRequest(
                                        account = account.account.orEmpty()
                                    )
                                )
                                account.copy(
                                    onHold = saldoNormal.onHold,
                                    balance = saldoNormal.balance,
                                    balanceString = saldoNormal.balanceString
                                )
                            } catch (_: Throwable) {
                                account
                            }
                        }
                    }?.awaitAll()
                } else {
                    qrAccountList.account
                }

                _accountList.update { accountList }

                send(UiState.Success(Unit))
            } catch (error: Throwable) {
                send(UiState.Error(error))
            } finally {
                close()
            }
        }
    }.uiState(viewModelScope)

    private val _qrCpmGenerate =
        MutableStateFlow<UiState<QrCpmGenerateResponse>>(UiState.Init)
    val qrCpmGenerate = _qrCpmGenerate.asStateFlow()

    private fun generateQrCpm(accountNumber: String, pin: String) {
        viewModelScope.launch {
            _qrCpmGenerate.asUiState {
                qrShowRepository.postQrCpmGenerate(
                    request = QrCpmGenerateRequest(
                        accountNumber = accountNumber,
                        pin = pin,
                        isNewQr = true
                    ),
                    fastMenu = qrShowRoute.fastMenu
                )
            }
        }
    }

    private val _qrCpmCheckStatus =
        MutableStateFlow<UiState<QrCpmCheckStatusResponse>>(UiState.Init)
    val qrCpmCheckStatus = _qrCpmCheckStatus.asStateFlow()

    private fun resetQrCpmCheckStatus() {
        viewModelScope.launch {
            _qrCpmCheckStatus.update { UiState.Init }
        }
    }

    private fun qrCpmCheckStatus() {
        viewModelScope.launch {
            _qrCpmCheckStatus.asUiState {
                qrShowRepository.postQrCpmCheckStatus(fastMenu = qrShowRoute.fastMenu)
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrShowRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
