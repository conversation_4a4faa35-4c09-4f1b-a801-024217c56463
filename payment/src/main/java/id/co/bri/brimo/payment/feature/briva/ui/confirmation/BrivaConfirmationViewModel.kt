package id.co.bri.brimo.payment.feature.briva.ui.confirmation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.BrivaConfirmationRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaPaymentRequest
import id.co.bri.brimo.payment.core.network.response.briva.BrivaPaymentResponse
import id.co.bri.brimo.payment.feature.briva.data.api.BrivaRepository
import id.co.bri.brimo.payment.feature.briva.data.model.BrivaModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class BrivaConfirmationViewModel(
    private val brivaRepository: BrivaRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val brivaConfirmationRoute = savedStateHandle.toRoute<BrivaConfirmationRoute>()

    val brivaModel = runCatching {
        Gson().fromJson(brivaConfirmationRoute.data, BrivaModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(brivaModel?.brivaInquiry?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: BrivaConfirmationEvent) {
        when (event) {
            is BrivaConfirmationEvent.Payment -> {
                postBrivaPay(
                    accountNumber = event.accountNumber,
                    note = event.note,
                    pin = event.pin,
                    saveAs = event.saveAs
                )
            }

            BrivaConfirmationEvent.ResetPayment -> {
                resetBrivaPay()
            }

            is BrivaConfirmationEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _brivaPayment = MutableStateFlow<UiState<BrivaPaymentResponse>>(UiState.Init)
    val brivaPayment = _brivaPayment.asStateFlow()

    private fun resetBrivaPay() {
        viewModelScope.launch {
            _brivaPayment.update { UiState.Init }
        }
    }

    private fun postBrivaPay(
        accountNumber: String,
        note: String,
        pin: String,
        saveAs: String
    ) {
        viewModelScope.launch {
            _brivaPayment.asUiState {
                val request = if (brivaModel?.brivaInquiry != null) {
                    BrivaPaymentRequest(
                        accountNumber = accountNumber,
                        amount = brivaModel.brivaInquiry.payAmount,
                        note = note,
                        pfmCategory = brivaModel.brivaInquiry.pfmCategory,
                        pin = pin,
                        referenceNumber = brivaModel.brivaInquiry.referenceNumber,
                        saveAs = saveAs
                    )
                } else {
                    BrivaPaymentRequest(
                        accountNumber = brivaModel?.accountNumber,
                        amount = brivaModel?.brivaConfirmation?.payAmount,
                        note = brivaModel?.brivaConfirmation?.note,
                        pfmCategory = brivaModel?.brivaConfirmation?.pfmCategory,
                        pin = pin,
                        referenceNumber = brivaModel?.brivaConfirmation?.referenceNumber,
                        saveAs = saveAs
                    )
                }
                brivaRepository.postBrivaPay(
                    request = request,
                    fastMenu = brivaConfirmationRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = brivaRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
