package id.co.bri.brimo.payment.app

import android.graphics.Color
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.SystemBarStyle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import id.co.bri.brimo.payment.BuildConfig
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.logger.Level

class PaymentActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        enableEdge()

        if (BuildConfig.DEBUG) {
            PaymentModule.koinApp.androidLogger(Level.DEBUG)
        }
        PaymentModule.koinApp.androidContext(this@PaymentActivity)
        val uri = intent.data
        val extras = intent.extras
        val activity = this@PaymentActivity

        setContent {
            PaymentApp(
                uri = uri,
                extras = extras,
                activity = activity
            )
        }
    }

    private fun enableEdge() {
        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.dark(
                Color.TRANSPARENT
            ),
            navigationBarStyle = SystemBarStyle.light(
                Color.WHITE,
                Color.WHITE
            )
        )
    }
}
