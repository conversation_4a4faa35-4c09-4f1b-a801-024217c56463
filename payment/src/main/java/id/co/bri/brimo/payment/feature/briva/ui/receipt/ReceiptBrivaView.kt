package id.co.bri.brimo.payment.feature.briva.ui.receipt

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaPaymentResponse

@OptIn(ExperimentalPermissionsApi::class)
@Composable
internal fun ReceiptBrivaView(
    data: BrivaPaymentResponse,
    type: String
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min)
            .paint(
                painter = painterResource(R.drawable.receipt_background),
                sizeToIntrinsics = true,
                contentScale = ContentScale.Crop
            )
            .padding(horizontal = 16.dp)
    ) {
        Spacer(modifier = Modifier.height(24.dp))

        Image(
            painter = painterResource(R.drawable.image_logo),
            contentDescription = null,
            modifier = Modifier
                .width(62.dp)
                .height(32.dp)
                .align(Alignment.CenterHorizontally),
            contentScale = ContentScale.Fit
        )

        Spacer(modifier = Modifier.height(24.dp))

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Min)
                .clip(
                    RoundedCornerShape(
                        topStart = 24.dp,
                        topEnd = 64.dp,
                        bottomStart = 24.dp,
                        bottomEnd = 24.dp
                    )
                )
                .background(Color.White)
                .paint(
                    painter = painterResource(R.drawable.image_watermark),
                    sizeToIntrinsics = true,
                    contentScale = ContentScale.Crop
                )
                .padding(16.dp)
        ) {
            val image = if (data.onProcess == true) {
                R.drawable.image_receipt_process
            } else {
                R.drawable.image_receipt_success
            }

            Image(
                painter = painterResource(image),
                contentDescription = null,
                modifier = Modifier
                    .size(120.dp)
                    .align(Alignment.CenterHorizontally),
                contentScale = ContentScale.Fit
            )

            Text(
                text = data.title.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.titleLarge
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = data.totalDataView?.firstOrNull()?.value.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.headlineLarge
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = data.dateTransaction.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(24.dp))

            Text(
                text = "Detail Transaksi",
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                    .padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(data.sourceAccountDataView?.iconPath.orEmpty())
                            .allowHardware(false)
                            .build(),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape),
                        placeholder = painterResource(id = R.drawable.icon_bri),
                        error = painterResource(id = R.drawable.icon_bri),
                        contentScale = ContentScale.Crop
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Column(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = data.sourceAccountDataView?.title.orEmpty(),
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(2.dp))

                        val subtitle = data.sourceAccountDataView?.subtitle.orEmpty()
                        val description = data.sourceAccountDataView?.description.orEmpty()

                        Text(
                            text = "$subtitle - $description",
                            modifier = Modifier.fillMaxWidth(),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.receipt_icon_arrow),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                            .size(16.dp),
                        contentScale = ContentScale.Fit
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    DividerHorizontal()
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    val icon = if (type == "briva") {
                        R.drawable.icon_briva
                    } else {
                        R.drawable.thumbnail
                    }

                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(data.billingDetail?.iconPath.orEmpty())
                            .allowHardware(false)
                            .decoderFactory(SvgDecoder.Factory())
                            .build(),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape),
                        placeholder = painterResource(id = icon),
                        error = painterResource(id = icon),
                        contentScale = ContentScale.Crop
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Column(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            text = data.billingDetail?.title.orEmpty(),
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(2.dp))

                        val subtitle = data.billingDetail?.subtitle.orEmpty()
                        val description = data.billingDetail?.description.orEmpty()

                        Text(
                            text = "$subtitle - $description",
                            modifier = Modifier.fillMaxWidth(),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            data.headerDataView?.forEach { item ->
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = item.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = item.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
            }

            data.dataViewTransaction?.forEach { item ->
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = item.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = item.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
            }

            if (!data.amountDataView.isNullOrEmpty()) {
                DividerHorizontal()

                Spacer(modifier = Modifier.height(12.dp))
            }

            data.amountDataView?.forEach { item ->
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = item.name.orEmpty(),
                        modifier = Modifier.weight(1f),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = item.value.orEmpty(),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
            }

            DividerHorizontal()

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Informasi Hubungi Call Center 1500017",
                modifier = Modifier.fillMaxWidth(),
                color = Color_7B90A6,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Biaya Termasuk PPN (Apabila Dikenakan/Apabila Ada)\n" +
                    "PT. Bank Rakyat Indonesia (Persero) Tbk.\n" +
                    "Kantor Pusat BRI - Jakarta Pusat\n" +
                    "NPWP : 01.001.608.7-093.000",
                modifier = Modifier.fillMaxWidth(),
                color = Color_7B90A6,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "© 2025 PT. Bank Rakyat Indonesia (Persero), Tbk.",
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelSmall
        )

        Text(
            text = "Terdaftar dan diawasi oleh Otoritas Jasa Keuangan",
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelSmall
        )

        Spacer(modifier = Modifier.height(24.dp))
    }
}

@Preview(heightDp = 1000)
@Composable
private fun PreviewReceiptBrivaView() {
    MainTheme {
        ReceiptBrivaView(
            data = BrivaPaymentResponse(
                amountDataView = listOf(
                    DataViewResponse(
                        name = "Nominal",
                        value = "Rp10.000",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Biaya Admin",
                        value = "Rp1.000",
                        style = ""
                    )
                ),
                billingDetail = BillingResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description"
                ),
                closeButtonString = "",
                dataViewTransaction = listOf(
                    DataViewResponse(
                        name = "Jenis Transaksi",
                        value = "Pembayaran BRIVA",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Keterangan",
                        value = "Test",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Catatan",
                        value = "Test",
                        style = ""
                    )
                ),
                dateTransaction = "25 Juni 2025, 12:34 WIB",
                footer = "",
                footerHtml = "",
                headerDataView = listOf(
                    DataViewResponse(
                        name = "No. Ref",
                        value = "**********",
                        style = ""
                    )
                ),
                helpFlag = false,
                immediatelyFlag = false,
                onProcess = false,
                referenceNumber = "",
                rowDataShow = 0,
                share = false,
                shareButtonString = "",
                sourceAccountDataView = BillingResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description"
                ),
                sourceAccountListType = "",
                title = "Transaksi Berhasil",
                titleImage = "",
                totalDataView = listOf(
                    DataViewResponse(
                        name = "Total Transaksi",
                        value = "Rp11.000",
                        style = ""
                    )
                ),
                voucherDataView = listOf()
            ),
            type = "briva"
        )
    }
}
