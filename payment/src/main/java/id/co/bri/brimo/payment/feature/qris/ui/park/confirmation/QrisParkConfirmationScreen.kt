package id.co.bri.brimo.payment.feature.qris.ui.park.confirmation

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.QrisParkConfirmationRoute
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.DashLine
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.PrimaryButton
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.WarningBottomSheet
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.errorSnackbar
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse
import id.co.bri.brimo.payment.core.network.response.qris.InputDataFrom
import id.co.bri.brimo.payment.core.network.response.qris.QrisScanResponse
import id.co.bri.brimo.payment.dependency.PaymentDependency
import id.co.bri.brimo.payment.feature.brizzi.ui.pin.PinDialog
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import id.co.bri.brimo.payment.feature.qris.ui.base.QrisAccountBottomSheet
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun QrisParkConfirmationScreen(
    navigation: (QrisParkConfirmationNavigation) -> Unit = {},
    qrisParkConfirmationViewModel: QrisParkConfirmationViewModel = koinViewModel()
) {
    val accountList by qrisParkConfirmationViewModel.accountList.collectAsStateWithLifecycle()
    val qrisParkConfirmation by qrisParkConfirmationViewModel.qrisParkConfirmation.collectAsStateWithLifecycle()

    if (qrisParkConfirmationViewModel.qrisModel != null) {
        QrisParkConfirmationContent(
            state = QrisParkConfirmationState(
                qrisParkConfirmationRoute = qrisParkConfirmationViewModel.qrisParkConfirmationRoute,
                qrisModel = qrisParkConfirmationViewModel.qrisModel,
                accountList = accountList.orEmpty(),
                qrisConfirmation = qrisParkConfirmation
            ),
            event = qrisParkConfirmationViewModel::handleEvent,
            navigation = navigation
        )
    }
}

@Composable
private fun QrisParkConfirmationContent(
    state: QrisParkConfirmationState,
    event: (QrisParkConfirmationEvent) -> Unit = {},
    navigation: (QrisParkConfirmationNavigation) -> Unit = {}
) {
    // Global
    var selectedPin by rememberSaveable { mutableStateOf("") }
    var selectedAccount: AccountResponse? by remember {
        mutableStateOf(state.accountList.find { it.default == 1 }
            ?: state.accountList.firstOrNull())
    }

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            event(
                QrisParkConfirmationEvent.Confirmation(
                    accountNumber = selectedAccount?.account.orEmpty(),
                    pin = selectedPin
                )
            )
        }
    )

    // Warning
    var warningBottomSheet by rememberSaveable { mutableStateOf(false) }

    BackHandler {
        warningBottomSheet = true
    }

    WarningBottomSheet(
        showBottomSheet = warningBottomSheet,
        onShowBottomSheet = { warningBottomSheet = it },
        title = "Yakin ingin kembali?",
        description = "Kamu akan keluar dari halaman konfirmasi. Transaksi belum diproses.",
        primaryText = "Lanjutkan Transaksi",
        secondaryText = "Kembali",
        onSecondary = {
            PaymentDependency.getFinish()
        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Pin
    var pinDialog by rememberSaveable { mutableStateOf(false) }
    var pinMessage by rememberSaveable { mutableStateOf("") }
    var deletePin by remember { mutableStateOf({}) }

    if (pinDialog) {
        Dialog(
            onDismissRequest = { pinDialog = false },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                PinDialog(
                    error = pinMessage,
                    onBack = {
                        pinDialog = false
                    },
                    onPin = { pin ->
                        selectedPin = pin
                        event(
                            QrisParkConfirmationEvent.Confirmation(
                                accountNumber = selectedAccount?.account.orEmpty(),
                                pin = selectedPin
                            )
                        )
                    },
                    deletePin = { action ->
                        deletePin = action
                    }
                )
            }
        }
    }

    // Account
    var accountBottomSheet by rememberSaveable { mutableStateOf(false) }

    if (state.accountList.isNotEmpty()) {
        BottomSheet(
            showBottomSheet = accountBottomSheet,
            onShowBottomSheet = { accountBottomSheet = it }
        ) { dismiss ->
            QrisAccountBottomSheet(
                selectedAccount = selectedAccount?.account.orEmpty(),
                nominal = "",
                data = state.accountList,
                onSelect = { item ->
                    dismiss {
                        selectedAccount = item
                    }
                },
                onRefresh = { item ->
                    event(QrisParkConfirmationEvent.RefreshSaldo(item.account.orEmpty()))
                },
                onClose = {
                    dismiss {}
                },
                fastMenu = state.qrisParkConfirmationRoute.fastMenu
            )
        }
    }

    // Qris Park Confirmation
    LaunchedEffect(state.qrisConfirmation) {
        state.qrisConfirmation
            .onLoading {
                progressDialog = true
            }
            .onSuccess { data ->
                progressDialog = false
                val qrisModel = QrisModel(
                    openPayment = state.qrisModel.openPayment,
                    typeQr = state.qrisModel.typeQr,
                    qrisScan = state.qrisModel.qrisScan,
                    qrisParkConfirmation = data,
                    accountSelected = selectedAccount
                )
                val qrisData = qrisModel.serialize().orEmpty()
                if (qrisData.isEmpty()) {
                    errorBottomSheet = true
                } else {
                    navigation(QrisParkConfirmationNavigation.Status(qrisData = qrisData))
                }
                event(QrisParkConfirmationEvent.ResetConfirmation)
            }
            .onError { e ->
                progressDialog = false
                deletePin()
                if (e is MessageException && e.errorSnackbar()) {
                    snackbarType = SnackbarType.ERROR
                    snackbarHostState.showSnackbar(e.description)
                } else {
                    error = e
                    errorBottomSheet = true
                }
                event(QrisParkConfirmationEvent.ResetConfirmation)
            }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, top = 16.dp, end = 16.dp)
                        .clickable {
                            accountBottomSheet = true
                        },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = selectedAccount?.imagePath.orEmpty(),
                        contentDescription = null,
                        modifier = Modifier
                            .width(58.dp)
                            .height(36.dp),
                        placeholder = painterResource(id = R.drawable.thumbnail),
                        error = painterResource(id = R.drawable.thumbnail),
                        contentScale = ContentScale.Inside
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    if (state.qrisParkConfirmationRoute.fastMenu) {
                        Text(
                            text = selectedAccount?.accountString.orEmpty(),
                            modifier = Modifier.weight(1f),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    } else {
                        var hide by rememberSaveable { mutableStateOf(false) }
                        val icon = if (hide) {
                            R.drawable.icon_hide_eye
                        } else {
                            R.drawable.icon_unhide_eye
                        }
                        val accountString = selectedAccount?.accountString.orEmpty()
                        val account = if (hide) {
                            "${accountString.take(4)} **** ****${accountString.takeLast(4)}"
                        } else {
                            accountString
                        }
                        val balance = if (hide) {
                            "••••••"
                        } else {
                            selectedAccount?.balanceString.orEmpty()
                        }
                        val balanceError = selectedAccount?.balance == null
                        val color = if (balanceError) Color_E84040 else Color.Black

                        Column(modifier = Modifier.weight(1f)) {
                            Row(
                                modifier = Modifier.clickable {
                                    hide = !hide
                                },
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = account,
                                    style = MaterialTheme.typography.labelSmall
                                )

                                if (!balanceError) {
                                    Spacer(modifier = Modifier.width(4.dp))

                                    Image(
                                        painter = painterResource(icon),
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp),
                                        contentScale = ContentScale.Fit
                                    )
                                }
                            }

                            Row(verticalAlignment = Alignment.CenterVertically) {
                                if (!balanceError) {
                                    Text(
                                        text = selectedAccount?.currency.orEmpty() + balance,
                                        color = color,
                                        fontWeight = FontWeight.SemiBold,
                                        style = MaterialTheme.typography.bodyMedium
                                    )

                                    Spacer(modifier = Modifier.width(8.dp))
                                } else {
                                    Text(
                                        text = "Gagal memuat saldo",
                                        color = Color_E84040,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.width(12.dp))

                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp)
                    )
                }

                PrimaryButton(
                    label = "Konfirmasi Tiket Parkir",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    pinDialog = true
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                warningBottomSheet = true
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "Konfirmasi Tiket Parkir",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Image(
                    painter = painterResource(R.drawable.image_parking),
                    contentDescription = null,
                    modifier = Modifier
                        .width(180.dp)
                        .height(120.dp)
                        .align(Alignment.CenterHorizontally),
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .background(
                            Color.White,
                            RoundedCornerShape(
                                topStart = 16.dp,
                                topEnd = 64.dp,
                                bottomStart = 16.dp,
                                bottomEnd = 16.dp
                            )
                        )
                        .padding(horizontal = 16.dp, vertical = 24.dp)
                ) {
                    Text(
                        text = "Tiket Parkir Kamu",
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    Text(
                        text = state.qrisModel.qrisScan?.nomorTiket.orEmpty(),
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    DividerHorizontal()

                    Spacer(modifier = Modifier.height(24.dp))

                    state.qrisModel.qrisScan?.billingDetail?.forEachIndexed { index, item ->
                        ItemBilling(
                            name = item.name.orEmpty(),
                            value = item.value.orEmpty(),
                            notLast = index < state.qrisModel.qrisScan.billingDetail.size - 1
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    DividerHorizontal()

                    Spacer(modifier = Modifier.height(24.dp))

                    val listStep = listOf(
                        R.drawable.image_search to "Scan tiket di Qitta",
                        R.drawable.image_receipt_success to "Konfirmasi tiket parkir",
                        R.drawable.image_ticket to "Scan tiket di Pintu Keluar",
                        R.drawable.image_smile to "Pembayaran berhasil"
                    )

                    Row(modifier = Modifier.fillMaxWidth()) {
                        listStep.forEachIndexed { index, item ->
                            Column(modifier = Modifier.weight(3f)) {
                                Image(
                                    painter = painterResource(item.first),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(48.dp)
                                        .align(Alignment.CenterHorizontally),
                                    contentScale = ContentScale.Fit
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                Text(
                                    text = item.second,
                                    modifier = Modifier.fillMaxWidth(),
                                    textAlign = TextAlign.Center,
                                    style = MaterialTheme.typography.labelSmall
                                )
                            }

                            if (index < listStep.size - 1) {
                                DashLine(
                                    modifier = Modifier
                                        .weight(1f)
                                        .padding(vertical = 24.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ItemBilling(
    name: String,
    value: String,
    notLast: Boolean
) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = name,
            modifier = Modifier.weight(1f),
            color = Color_7B90A6,
            style = MaterialTheme.typography.bodyMedium
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = value,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.End,
            style = MaterialTheme.typography.bodyMedium
        )
    }

    if (notLast) {
        Spacer(modifier = Modifier.height(12.dp))
    }
}

@Preview
@Composable
private fun PreviewQrisParkConfirmation() {
    MainTheme {
        QrisParkConfirmationContent(
            state = QrisParkConfirmationState(
                qrisParkConfirmationRoute = QrisParkConfirmationRoute(
                    data = "",
                    fastMenu = false
                ),
                qrisModel = QrisModel(
                    qrisScan = QrisScanResponse(
                        accountList = listOf(),
                        billingDetail = listOf(
                            DataViewResponse(
                                name = "Waktu Masuk",
                                value = "12:34 WIB",
                                style = ""
                            ),
                            DataViewResponse(
                                name = "Tanggal",
                                value = "25 Juni 2025",
                                style = ""
                            ),
                            DataViewResponse(
                                name = "Lokasi",
                                value = "Senayan City",
                                style = ""
                            )
                        ),
                        billingDetailOpen = listOf(
                            BillingResponse(
                                listType = "",
                                iconName = "",
                                iconPath = "",
                                title = "Title",
                                subtitle = "Subtitle",
                                description = "Description"
                            )
                        ),
                        billingAmount = listOf(),
                        billingAmountDetail = listOf(),
                        inputDataFrom = InputDataFrom(
                            countryName = "",
                            countryCode = "",
                            currencyCode = "",
                            iconName = "",
                            iconPath = "",
                            inputName = "",
                            inputValue = 0
                        ),
                        information = DataViewResponse(
                            name = "",
                            value = "",
                            style = ""
                        ),
                        openPayment = true,
                        isBilling = false,
                        minimumPayment = false,
                        amountEditable = true,
                        rowDataShow = 0,
                        saved = "",
                        amount = "",
                        amountString = "",
                        minimumAmount = "",
                        minimumAmountString = "",
                        adminFee = "",
                        adminFeeString = "",
                        payAmount = "",
                        payAmountString = "",
                        minimumTransaction = "",
                        minimumTransactionString = "",
                        tipOption = listOf(),
                        tipEditable = false,
                        tipType = "",
                        tipAmount = "",
                        tipAmountString = "",
                        typeQr = "parking_spi",
                        merchantName = "",
                        merchantCountry = "",
                        nomorTiket = "DJSH8127SD3298393",
                        referenceNumber = ""
                    )
                ),
                accountList = listOf(
                    AccountResponse(
                        account = "***************",
                        accountString = "1234 5678 9012 345",
                        name = "Name",
                        currency = "Rp",
                        cardNumber = "****************",
                        cardNumberString = "0987 XXXX XXXX 8765",
                        productType = "",
                        accountType = "",
                        scCode = "",
                        default = 0,
                        alias = "Alias",
                        minimumBalance = "",
                        limit = "",
                        limitString = "",
                        imageName = "",
                        imagePath = "",
                        onHold = false,
                        balance = "9000000",
                        balanceString = "9.000.000,00"
                    )
                )
            )
        )
    }
}
