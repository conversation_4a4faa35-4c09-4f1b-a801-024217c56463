package id.co.bri.brimo.payment.feature.qris.ui.park.status

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse
import id.co.bri.brimo.payment.core.network.response.qris.InputDataFrom
import id.co.bri.brimo.payment.core.network.response.qris.QrisScanResponse
import id.co.bri.brimo.payment.feature.brizzi.ui.receipt.generateBarCode

@Composable
internal fun QrisParkView(
    data: QrisScanResponse
) {
    val density = LocalDensity.current
    val configuration = LocalConfiguration.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .paint(
                painter = painterResource(R.drawable.receipt_background),
                sizeToIntrinsics = true,
                contentScale = ContentScale.Crop
            )
    ) {
        Spacer(modifier = Modifier.height(24.dp))

        Image(
            painter = painterResource(R.drawable.image_logo),
            contentDescription = null,
            modifier = Modifier
                .width(96.dp)
                .height(48.dp)
                .align(Alignment.CenterHorizontally),
            contentScale = ContentScale.Fit
        )

        Spacer(modifier = Modifier.height(24.dp))

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .background(
                    Color.White,
                    RoundedCornerShape(
                        topStart = 16.dp,
                        topEnd = 64.dp,
                        bottomStart = 16.dp,
                        bottomEnd = 16.dp
                    )
                )
                .padding(horizontal = 16.dp, vertical = 24.dp)
        ) {
            Text(
                text = "Tiket Parkir Aktif Kamu",
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyLarge
            )

            Spacer(modifier = Modifier.height(12.dp))

            if (data.nomorTiket?.isNotEmpty() == true) {
                val widthPx =
                    with(density) { configuration.screenWidthDp.dp.toPx().toInt() }
                val heightPx =
                    with(density) { 70.dp.toPx().toInt() }
                val bitmap = remember(data.nomorTiket) {
                    generateBarCode(data.nomorTiket, widthPx, heightPx)
                }

                Image(
                    bitmap = bitmap.asImageBitmap(),
                    contentDescription = null,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = data.nomorTiket.orEmpty(),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall
            )

            Spacer(modifier = Modifier.height(24.dp))

            DividerHorizontal()

            Spacer(modifier = Modifier.height(24.dp))

            data.billingDetail?.forEachIndexed { index, item ->
                ItemBilling(
                    name = item.name.orEmpty(),
                    value = item.value.orEmpty(),
                    notLast = index < data.billingDetail.size - 1
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            DividerHorizontal()

            Spacer(modifier = Modifier.height(24.dp))

            Text(
                text = "Pembayaran melalui Qitta\n" +
                    "Tarif parkir sudah termasuk pajak",
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "© 2025 PT. Bank Rakyat Indonesia (Persero), Tbk.",
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelSmall
        )

        Text(
            text = "Terdaftar dan diawasi oleh Otoritas Jasa Keuangan",
            modifier = Modifier.fillMaxWidth(),
            color = Color_7B90A6,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelSmall
        )

        Spacer(modifier = Modifier.height(24.dp))
    }
}

@Composable
private fun ItemBilling(
    name: String,
    value: String,
    notLast: Boolean
) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = name,
            modifier = Modifier.weight(1f),
            color = Color_7B90A6,
            style = MaterialTheme.typography.bodyMedium
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = value,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.End,
            style = MaterialTheme.typography.bodyMedium
        )
    }

    if (notLast) {
        Spacer(modifier = Modifier.height(12.dp))
    }
}

@Preview
@Composable
private fun PreviewQrisParkView() {
    MainTheme {
        QrisParkView(
            data = QrisScanResponse(
                accountList = listOf(),
                billingDetail = listOf(
                    DataViewResponse(
                        name = "Waktu Masuk",
                        value = "12:34 WIB",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Tanggal",
                        value = "25 Juni 2025",
                        style = ""
                    ),
                    DataViewResponse(
                        name = "Lokasi",
                        value = "Senayan City",
                        style = ""
                    )
                ),
                billingDetailOpen = listOf(
                    BillingResponse(
                        listType = "",
                        iconName = "",
                        iconPath = "",
                        title = "Title",
                        subtitle = "Subtitle",
                        description = "Description"
                    )
                ),
                billingAmount = listOf(),
                billingAmountDetail = listOf(),
                inputDataFrom = InputDataFrom(
                    countryName = "",
                    countryCode = "",
                    currencyCode = "",
                    iconName = "",
                    iconPath = "",
                    inputName = "",
                    inputValue = 0
                ),
                information = DataViewResponse(
                    name = "",
                    value = "",
                    style = ""
                ),
                openPayment = true,
                isBilling = false,
                minimumPayment = false,
                amountEditable = true,
                rowDataShow = 0,
                saved = "",
                amount = "",
                amountString = "",
                minimumAmount = "",
                minimumAmountString = "",
                adminFee = "",
                adminFeeString = "",
                payAmount = "",
                payAmountString = "",
                minimumTransaction = "",
                minimumTransactionString = "",
                tipOption = listOf(),
                tipEditable = false,
                tipType = "",
                tipAmount = "",
                tipAmountString = "",
                typeQr = "qris_transfer",
                merchantName = "",
                merchantCountry = "",
                nomorTiket = "DJSH8127SD3298393",
                referenceNumber = ""
            )
        )
    }
}
