package id.co.bri.brimo.payment.feature.qrtransfer.ui

import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qrtransfer.QrTransferGenerateResponse

internal data class QrTransferState(
    val accountList: List<AccountResponse> = emptyList(),
    val qrTransferGenerate: UiState<QrTransferGenerateResponse> = UiState.Init
)

internal sealed class QrTransferEvent {
    object RefreshAccountList : QrTransferEvent()
    data class Generate(val accountNumber: String, val amount: String) : QrTransferEvent()
}

internal sealed class QrTransferNavigation {
    object Back : QrTransferNavigation()
    object History : QrTransferNavigation()
}
