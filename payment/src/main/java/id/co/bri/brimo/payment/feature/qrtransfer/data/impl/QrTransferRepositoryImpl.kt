package id.co.bri.brimo.payment.feature.qrtransfer.data.impl

import id.co.bri.brimo.payment.core.network.processApi
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrtransfer.QrTransferGenerateRequest
import id.co.bri.brimo.payment.core.network.response.base.AccountListResponse
import id.co.bri.brimo.payment.core.network.response.base.SaldoNormalResponse
import id.co.bri.brimo.payment.core.network.response.qrtransfer.QrTransferGenerateResponse
import id.co.bri.brimo.payment.dependency.PaymentDependency
import id.co.bri.brimo.payment.feature.qrtransfer.data.api.QrTransferRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

internal class QrTransferRepositoryImpl(
    private val ioDispatcher: CoroutineDispatcher
) : QrTransferRepository {

    override suspend fun postAccountList(fastMenu: Boolean): AccountListResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "j1EEkMeQ0/HX28L5CI1tREbX2GgbvC2xNJ9KzPRllBs="
            } else {
                "6XcHuzXPT8Uh/Pggd+tfUdYwWu7OSbxYs/WHKsVq6qA="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = "",
                    fastMenu = fastMenu
                )
            }
        }

    override suspend fun postSaldoNormal(
        request: SaldoNormalRequest
    ): SaldoNormalResponse =
        withContext(ioDispatcher) {
            val url = "gnUBRK3Mc0ogiZdhmA6hBg=="
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }

    override suspend fun postQrTransferGenerate(
        request: QrTransferGenerateRequest,
        fastMenu: Boolean
    ): QrTransferGenerateResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "jhIqXpuQXonbCAJng5Oz/4lOzsiR9odCoEQgvmIu9hk="
            } else {
                "jhIqXpuQXonbCAJng5Oz/4lOzsiR9odCoEQgvmIu9hk="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }
}
