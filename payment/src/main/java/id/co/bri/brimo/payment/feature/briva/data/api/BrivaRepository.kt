package id.co.bri.brimo.payment.feature.briva.data.api

import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaConfirmationRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaFavoriteRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaInquiryRequest
import id.co.bri.brimo.payment.core.network.request.briva.BrivaPaymentRequest
import id.co.bri.brimo.payment.core.network.response.base.AccountListResponse
import id.co.bri.brimo.payment.core.network.response.base.SaldoNormalResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaConfirmationResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaFormResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaInquiryResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaPaymentResponse

internal interface BrivaRepository {

    suspend fun postBrivaForm(fastMenu: Boolean): BrivaFormResponse

    suspend fun postBrivaFavorite(request: BrivaFavoriteRequest)

    suspend fun postBrivaRemoveFavorite(request: BrivaFavoriteRequest)

    suspend fun postBrivaDelete(request: BrivaFavoriteRequest)

    suspend fun postBrivaUpdateNickname(request: BrivaFavoriteRequest)

    suspend fun postBrivaInquiry(
        request: BrivaInquiryRequest,
        fastMenu: Boolean
    ): BrivaInquiryResponse

    suspend fun postAccountList(fastMenu: Boolean): AccountListResponse

    suspend fun postSaldoNormal(request: SaldoNormalRequest): SaldoNormalResponse

    suspend fun postBrivaConfirmation(
        request: BrivaConfirmationRequest,
        fastMenu: Boolean
    ): BrivaConfirmationResponse

    suspend fun postBrivaPay(request: BrivaPaymentRequest, fastMenu: Boolean): BrivaPaymentResponse
}
