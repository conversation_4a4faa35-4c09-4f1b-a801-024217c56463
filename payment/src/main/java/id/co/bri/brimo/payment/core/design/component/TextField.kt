package id.co.bri.brimo.payment.core.design.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.LocalTextSelectionColors
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.takeOrElse
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import coil.compose.AsyncImage
import coil.request.ImageRequest
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.common.formatWithLocale
import id.co.bri.brimo.payment.core.design.helper.AccountNumberVisualTransformation
import id.co.bri.brimo.payment.core.design.helper.ThousandSeparatorTransformation
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_A3B1C1
import id.co.bri.brimo.payment.core.design.theme.Color_E0E5EB
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_E9EEF6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun TextFieldCustom(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = LocalTextStyle.current,
    label: @Composable (() -> Unit)? = null,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    prefix: @Composable (() -> Unit)? = null,
    suffix: @Composable (() -> Unit)? = null,
    supportingText: @Composable (() -> Unit)? = null,
    isError: Boolean = false,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    interactionSource: MutableInteractionSource? = null,
    shape: Shape = TextFieldDefaults.shape,
    colors: TextFieldColors = TextFieldDefaults.colors(),
    contentPadding: PaddingValues = TextFieldDefaults.contentPaddingWithLabel()
) {
    @Suppress("NAME_SHADOWING")
    val interactionSource = interactionSource ?: remember { MutableInteractionSource() }
    val textColor =
        textStyle.color.takeOrElse {
            val focused = interactionSource.collectIsFocusedAsState().value
            colors.textColor(enabled, isError, focused)
        }
    val mergedTextStyle = textStyle.merge(TextStyle(color = textColor))

    CompositionLocalProvider(LocalTextSelectionColors provides colors.textSelectionColors) {
        BasicTextField(
            value = value,
            modifier =
                modifier
                    .defaultMinSize(
                        minWidth = TextFieldDefaults.MinWidth,
                        minHeight = TextFieldDefaults.MinHeight
                    ),
            onValueChange = onValueChange,
            enabled = enabled,
            readOnly = readOnly,
            textStyle = mergedTextStyle,
            cursorBrush = SolidColor(colors.cursorColor(isError)),
            visualTransformation = visualTransformation,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            interactionSource = interactionSource,
            singleLine = singleLine,
            maxLines = maxLines,
            minLines = minLines,
            decorationBox =
                @Composable { innerTextField ->
                    TextFieldDefaults.DecorationBox(
                        value = value,
                        visualTransformation = visualTransformation,
                        innerTextField = innerTextField,
                        placeholder = placeholder,
                        label = label,
                        leadingIcon = leadingIcon,
                        trailingIcon = trailingIcon,
                        prefix = prefix,
                        suffix = suffix,
                        supportingText = supportingText,
                        shape = shape,
                        singleLine = singleLine,
                        enabled = enabled,
                        isError = isError,
                        interactionSource = interactionSource,
                        colors = colors,
                        contentPadding = contentPadding
                    )
                }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun OutlinedTextFieldCustom(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = LocalTextStyle.current,
    label: @Composable (() -> Unit)? = null,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    prefix: @Composable (() -> Unit)? = null,
    suffix: @Composable (() -> Unit)? = null,
    supportingText: @Composable (() -> Unit)? = null,
    isError: Boolean = false,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    interactionSource: MutableInteractionSource? = null,
    shape: Shape = OutlinedTextFieldDefaults.shape,
    colors: TextFieldColors = OutlinedTextFieldDefaults.colors(),
    contentPadding: PaddingValues = OutlinedTextFieldDefaults.contentPadding()
) {
    @Suppress("NAME_SHADOWING")
    val interactionSource = interactionSource ?: remember { MutableInteractionSource() }
    val textColor =
        textStyle.color.takeOrElse {
            val focused = interactionSource.collectIsFocusedAsState().value
            colors.textColor(enabled, isError, focused)
        }
    val mergedTextStyle = textStyle.merge(TextStyle(color = textColor))

    val density = LocalDensity.current

    CompositionLocalProvider(LocalTextSelectionColors provides colors.textSelectionColors) {
        BasicTextField(
            value = value,
            modifier =
                modifier
                    .then(
                        if (label != null) {
                            Modifier
                                .semantics(mergeDescendants = true) {}
                                .padding(top = with(density) { 8.toDp() })
                        } else {
                            Modifier
                        }
                    )
                    .defaultMinSize(
                        minWidth = OutlinedTextFieldDefaults.MinWidth,
                        minHeight = OutlinedTextFieldDefaults.MinHeight
                    ),
            onValueChange = onValueChange,
            enabled = enabled,
            readOnly = readOnly,
            textStyle = mergedTextStyle,
            cursorBrush = SolidColor(colors.cursorColor(isError)),
            visualTransformation = visualTransformation,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            interactionSource = interactionSource,
            singleLine = singleLine,
            maxLines = maxLines,
            minLines = minLines,
            decorationBox =
                @Composable { innerTextField ->
                    OutlinedTextFieldDefaults.DecorationBox(
                        value = value,
                        visualTransformation = visualTransformation,
                        innerTextField = innerTextField,
                        placeholder = placeholder,
                        label = label,
                        leadingIcon = leadingIcon,
                        trailingIcon = trailingIcon,
                        prefix = prefix,
                        suffix = suffix,
                        supportingText = supportingText,
                        singleLine = singleLine,
                        enabled = enabled,
                        isError = isError,
                        interactionSource = interactionSource,
                        colors = colors,
                        contentPadding = contentPadding,
                        container = {
                            OutlinedTextFieldDefaults.Container(
                                enabled = enabled,
                                isError = isError,
                                interactionSource = interactionSource,
                                colors = colors,
                                shape = shape,
                            )
                        }
                    )
                }
        )
    }
}

@Stable
private fun TextFieldColors.textColor(
    enabled: Boolean,
    isError: Boolean,
    focused: Boolean
): Color =
    when {
        !enabled -> disabledTextColor
        isError -> errorTextColor
        focused -> focusedTextColor
        else -> unfocusedTextColor
    }

@Stable
private fun TextFieldColors.cursorColor(isError: Boolean): Color =
    if (isError) errorCursorColor else cursorColor

@Stable
@Composable
internal fun textFieldColors(): TextFieldColors {
    return TextFieldDefaults.colors(
        focusedContainerColor = Color_F5F7FB,
        unfocusedContainerColor = Color_F5F7FB,
        disabledContainerColor = Color_E9EEF6,
        errorContainerColor = Color_F5F7FB,
        focusedIndicatorColor = Color.Transparent,
        unfocusedIndicatorColor = Color.Transparent,
        disabledIndicatorColor = Color.Transparent,
        errorIndicatorColor = Color.Transparent,
        focusedLabelColor = Color.Black,
        disabledLabelColor = Color_7B90A6
    )
}

@Stable
@Composable
internal fun textFieldColorsDisable(): TextFieldColors {
    return TextFieldDefaults.colors(
        focusedContainerColor = Color_F5F7FB,
        unfocusedContainerColor = Color_F5F7FB,
        disabledContainerColor = Color_E9EEF6,
        errorContainerColor = Color_F5F7FB,
        focusedIndicatorColor = Color.Transparent,
        unfocusedIndicatorColor = Color.Transparent,
        disabledIndicatorColor = Color.Transparent,
        errorIndicatorColor = Color.Transparent,
        focusedLabelColor = Color.Black,
        disabledLabelColor = Color_7B90A6
    )
}

@Stable
@Composable
internal fun nominalFieldColors(): TextFieldColors {
    return TextFieldDefaults.colors(
        focusedContainerColor = Color_F5F7FB,
        unfocusedContainerColor = Color_F5F7FB,
        disabledContainerColor = Color_F5F7FB,
        errorContainerColor = Color_F5F7FB,
        focusedIndicatorColor = Color.Transparent,
        unfocusedIndicatorColor = Color.Transparent,
        disabledIndicatorColor = Color.Transparent,
        errorIndicatorColor = Color.Transparent,
        focusedLabelColor = Color.Black
    )
}

@Stable
@Composable
internal fun outlinedTextFieldColors(): TextFieldColors {
    return OutlinedTextFieldDefaults.colors(
        focusedContainerColor = Color_F5F7FB,
        unfocusedContainerColor = Color_F5F7FB,
        disabledContainerColor = Color_F5F7FB,
        errorContainerColor = Color_F5F7FB,
        focusedBorderColor = Color_0054F3,
        unfocusedBorderColor = Color_F5F7FB,
        disabledBorderColor = Color_F5F7FB,
    )
}

@Composable
internal fun NominalTextField(
    value: String,
    placeholder: String,
    currencyPrefix: String? = "Rp",
    maxValue: Int,
    minValue: Int,
    isInsufficientBalance: Boolean = false,
    showMinimalText: Boolean = false,
    currencyImageUrl: String? = null,
    validateInput: (Boolean) -> Unit = { false },
    onValueChange: (String) -> Unit
) {
    var isMin by remember { mutableStateOf(false) }
    var isMax by remember { mutableStateOf(false) }
    val numericValue = value.toLongOrNull()
    val interactionSource = remember { MutableInteractionSource() }
    val isFocused by interactionSource.collectIsFocusedAsState()

    isMin = numericValue != null && numericValue < minValue
    isMax = numericValue != null && numericValue > maxValue

    val borderColor = when {
//        isMin -> Color_E84040
//        isMax -> Color_E84040
        isInsufficientBalance -> Color_E84040
        isFocused -> Color_0054F3
        else -> Color.Transparent
    }

    Box(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.zIndex(1f)) {
            Text(
                text = "Nominal",
                modifier = Modifier.padding(start = 16.dp, top = 16.dp),
                style = MaterialTheme.typography.bodySmall
            )

            Text(
                text = currencyPrefix.toString(),
                modifier = Modifier.padding(start = 16.dp),
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.SemiBold
                )
            )
        }

        TextFieldCustom(
            value = value,
            onValueChange = onValueChange,
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp)
                .border(1.dp, borderColor, RoundedCornerShape(16.dp)),
            textStyle = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.SemiBold
            ),
            label = {
                Text(text = "")
            },
            visualTransformation = ThousandSeparatorTransformation(),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number
            ),
            singleLine = true,
            shape = RoundedCornerShape(16.dp),
            colors = textFieldColors(),
            contentPadding = TextFieldDefaults.contentPaddingWithLabel(
                top = 16.dp,
                start = 44.dp
            ),
            trailingIcon = if (value.isNotEmpty()) {
                {
                    Icon(
                        painter = painterResource(R.drawable.icon_close_circle),
                        contentDescription = null,
                        modifier = Modifier.clickable {
                            onValueChange("")
                        }
                    )
                }
            } else {
                null
            }
        )
    }

    val errorText = if (isInsufficientBalance) {
        "Saldo tidak cukup"
    } else if (isMin) {
        "Batas minimal nominal adalah Rp${minValue.formatWithLocale()}"
    } else { }

    if (minValue > 1) {
        if (isMin || isInsufficientBalance) {
            validateInput(false)
            if (isInsufficientBalance) {
                Text(
                    text = errorText.toString(),
                    color = Color_E84040,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp)
                )
            } else {
                Text(
                    text = "Minimal Rp${minValue.formatWithLocale()}",
                    color = Color_7B90A6,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp)
                )
            }
        } else {
            validateInput(true)
            if (showMinimalText) {
                Text(
                    text = "Minimal Rp${minValue.formatWithLocale()}",
                    color = Color_7B90A6,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp)
                )
            }
        }
    } else {
        if (isInsufficientBalance) {
            validateInput(false)
            Text(
                text = errorText.toString(),
                color = Color_E84040,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)
            )
        } else {
            validateInput(true)
        }
    }
}

@Composable
fun BasicTextField(
    label: String,
    value: String,
    maxLength: Int? = 50,
    enableCounter: Boolean = false,
    multiLine: Boolean? = false,
    note: String? = "",
    prefix: String? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardType: KeyboardType = KeyboardType.Text,
    onValueChange: (String) -> Unit,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isFocused by interactionSource.collectIsFocusedAsState()

    val borderColor = if (isFocused == true) Color_0054F3 else Color.Transparent

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        TextFieldCustom(
            value = value,
            onValueChange = { newValue ->
                val filteredValue = if (!multiLine!!) newValue.replace("\n", "") else newValue
                if (maxLength == null || filteredValue.length <= maxLength) {
                    onValueChange(filteredValue)
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(if (!multiLine!!) 64.dp else 104.dp)
                .border(1.dp, borderColor, RoundedCornerShape(16.dp)),
            textStyle = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold),
            label = {
                Text(text = label)
            },
            trailingIcon = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (value.isNotEmpty()) {
                        Icon(
                            painter = painterResource(R.drawable.icon_close_circle),
                            contentDescription = null,
                            modifier = Modifier.clickable {
                                onValueChange("")
                            }
                        )

                        if (trailingIcon != null) Spacer(modifier = Modifier.width(4.dp))
                    }

                    if (trailingIcon != null) {
                        trailingIcon()
                    }
                }
            },
            prefix = {
                if (!prefix.isNullOrEmpty()) {
                    Text(
                        text = prefix.toString()
                    )
                }
            },
            singleLine = !multiLine,
            shape = RoundedCornerShape(16.dp),
            colors = textFieldColors(),
            contentPadding = TextFieldDefaults.contentPaddingWithLabel(
                top = 12.dp,
                bottom = 12.dp
            ),
            interactionSource = interactionSource,
            visualTransformation = visualTransformation,
            keyboardOptions = KeyboardOptions(keyboardType = keyboardType)
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = note.toString(),
                color = Color_7B90A6,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(top = 8.dp)
            )

            if (isFocused && enableCounter) {
                Text(
                    text = "${value.length}/$maxLength",
                    color = Color_7B90A6,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}

@Composable
internal fun NominalTextFieldWithFlag(
    value: String,
    placeholder: String,
    currencyPrefix: String? = "",
    maxValue: Int,
    minValue: Int,
    flagImage: String?,
    flagName: String? = "",
    enabled: Boolean = true,
    showMinimalText: Boolean = false,
    currencyImageUrl: String? = null,
    validateInput: (Boolean) -> Unit = { false },
    onValueChange: (String) -> Unit
) {
    var isMin by remember { mutableStateOf(false) }
    var isMax by remember { mutableStateOf(false) }
    val numericValue = value.toLongOrNull()
    val interactionSource = remember { MutableInteractionSource() }
    val isFocused by interactionSource.collectIsFocusedAsState()

    isMin = numericValue != null && numericValue < minValue
    isMax = numericValue != null && numericValue > maxValue

    val borderColor = when {
        isMin -> Color_E84040
        isMax -> Color_E84040
        isFocused -> Color_0054F3
        else -> Color.Transparent
    }

    Box {
        if (!currencyImageUrl.isNullOrEmpty()) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalAlignment = Alignment.End
            ) {
                Row {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(currencyImageUrl)
                            .crossfade(true)
                            .build(),
                        contentDescription = null,
                        modifier = Modifier
                            .width(20.dp)
                            .height(20.dp)
                            .clip(RoundedCornerShape(10.dp)),
                        contentScale = ContentScale.Crop
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = currencyPrefix.toString(),
                        style = MaterialTheme.typography.titleMedium
                    )
                }
            }
        }

        TextFieldCustom(
            value = value,
            onValueChange = onValueChange,
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp)
                .border(1.dp, borderColor, RoundedCornerShape(16.dp)),
            textStyle = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.SemiBold
            ),
            label = {
                Text(
                    text = placeholder,
                    modifier = Modifier.padding(bottom = 4.dp)
                )
            },
            prefix = {
                Text(
                    text = currencyPrefix.toString(),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.SemiBold
                    )
                )
            },
            visualTransformation = ThousandSeparatorTransformation(),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number
            ),
            singleLine = true,
            shape = RoundedCornerShape(16.dp),
            colors = if (enabled) textFieldColors() else textFieldColorsDisable(),
            contentPadding = TextFieldDefaults.contentPaddingWithLabel(
                top = 16.dp
            ),
            enabled = enabled,
            trailingIcon = if (value.isNotEmpty() && enabled) {
                {
                    Icon(
                        painter = painterResource(R.drawable.icon_close_circle),
                        contentDescription = "remove",
                        modifier = Modifier
                            .padding(top = 20.dp)
                            .clickable {
                                onValueChange("")
                            }
                    )
                }
            } else {
                null
            }
        )

        if (!flagName.isNullOrEmpty() && !flagImage.isNullOrEmpty()) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.End,
                modifier = Modifier
                    .padding(end = 16.dp)
                    .padding(top = 12.dp)
                    .fillMaxWidth()
            ) {

                AsyncImage(
                    model = flagImage,
                    contentDescription = "Circle Flag",
                    modifier = Modifier
                        .size(20.dp)
                        .clip(CircleShape),
                    placeholder = painterResource(id = R.drawable.thumbnail),
                    error = if (flagName == "IDR" || flagImage == "id") painterResource(R.drawable.flag_indonesia) else painterResource(
                        id = R.drawable.thumbnail
                    ),
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.width(4.dp))

                Text(
                    text = flagName ?: "",
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.titleSmall
                )
            }
        }

    }

    val errorText = if (isMin) {
        "Batas minimal nominal adalah Rp${minValue.formatWithLocale()}"
    } else if (isMax) {
        "Batas maksimal nominal adalah Rp${maxValue.formatWithLocale()}"
    } else {
        ""
    }

    if (minValue > 0) {
        if (isMin || isMax) {
            validateInput(false)
            Text(
                text = errorText,
                color = Color_E84040,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)
            )
        } else {
            validateInput(true)
            if (showMinimalText) {
                Text(
                    text = "Minimal Rp${minValue.formatWithLocale()}",
                    color = Color_7B90A6,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp)
                )
            }
        }
    }
}

@Composable
internal fun CurrencyCentFieldWithFlag(
    value: String,
    value2: String? = "",
    maxTransferableAmount: Long? = null,
    placeholder: String,
    currencyPrefix: String? = "",
    maxValue: Long? = null,
    minValue: Long? = null,
    flagImage: String? = "",
    flagName: String? = "",
    enabled: Boolean = true,
    showMinimalText: Boolean = false,
    currencyImageUrl: String? = null,
    validateInput: (Boolean) -> Unit = { false },
    onValueChange: (String) -> Unit
) {
    val parsedBigDecimal = try {
        value2
            ?.replace("Rp", "", ignoreCase = true)
            ?.replace(" ", "")
            ?.replace(".", "")
            ?.replace(",", ".")
            ?.trim()
            ?.let { BigDecimal(it) }
    } catch (e: Exception) {
        null
    }
    val interactionSource = remember { MutableInteractionSource() }
    val isFocused by interactionSource.collectIsFocusedAsState()

    val maxToBigDecimal = BigDecimal(maxValue ?: 0)
    val minToBigDecimal = BigDecimal(minValue ?: 0)
    val isInitialInput = parsedBigDecimal?.compareTo(BigDecimal.ZERO) == 0

    val isBelowMin =
        parsedBigDecimal != null && minValue != null && parsedBigDecimal < minToBigDecimal && !isInitialInput
    val isAboveMax =
        parsedBigDecimal != null && maxValue != null && parsedBigDecimal > maxToBigDecimal

    val isAboveBalance = parsedBigDecimal != null && maxTransferableAmount != null &&
        parsedBigDecimal > BigDecimal(maxTransferableAmount)

    val isError = isBelowMin || isAboveMax || isAboveBalance

    val borderColor = when {
        isBelowMin -> Color_E84040
        isAboveMax -> Color_E84040
        isAboveBalance -> Color_E84040
        isFocused -> Color_0054F3
        else -> Color.Transparent
    }

    Box {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp)
                .border(1.dp, borderColor, RoundedCornerShape(16.dp))
                .background(
                    if (enabled) Color_F5F7FB else Color_E9EEF6,
                    shape = RoundedCornerShape(16.dp)
                )
            ,
        ){
            if (!flagName.isNullOrEmpty() && !flagImage.isNullOrEmpty()){
                Column(
                    modifier = Modifier
                        .padding(vertical = 16.dp)
                        .width(64.dp),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                )
                {
                    AsyncImage(
                        model = flagImage,
                        contentDescription = "Circle Flag",
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape),
                        placeholder = painterResource(id = R.drawable.thumbnail),
                        error = if (flagName == "IDR" || flagImage == "id") painterResource(R.drawable.flag_indonesia) else painterResource(
                            id = R.drawable.thumbnail
                        ),
                        contentScale = ContentScale.Fit
                    )

                    Spacer(modifier = Modifier.height(2.dp))

                    Text(
                        text = flagName,
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.titleSmall
                    )
                }
            }

            Box(
                modifier = Modifier
                    .width(1.dp)
                    .height(44.dp)
                    .align(Alignment.CenterVertically)
                    .background(if (enabled) Color_E0E5EB else Color_A3B1C1)
            )

            TextFieldCustom(
            value = value,
            onValueChange = onValueChange,
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp),
            textStyle = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.SemiBold,
                color = if (enabled) Color.Black else Color_A3B1C1
            ),
            interactionSource = interactionSource,
            label = {
                Text(
                    text = placeholder,
                    modifier = Modifier.padding(bottom = 4.dp), color = if(enabled) Color.Black else Color_A3B1C1
                )
            },
            prefix = {
                Text(
                    text = currencyPrefix.toString(),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.SemiBold
                    )
                )
            },
            visualTransformation = VisualTransformation.None,
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number
            ),
            singleLine = true,
            shape = RoundedCornerShape(16.dp),
            colors = if (enabled) textFieldColors() else textFieldColorsDisable(),
            contentPadding = TextFieldDefaults.contentPaddingWithLabel(
                top = 16.dp
            ),
            enabled = enabled,
            trailingIcon = if (value.isNotEmpty() && enabled) {
                {
                    Icon(
                        painter = painterResource(R.drawable.icon_close_circle),
                        contentDescription = "remove",
                        modifier = Modifier
                            .clickable {
                                onValueChange("")
                            }
                    )
                }
            } else {
                null
            }
        )
        }
        if (!currencyImageUrl.isNullOrEmpty()) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalAlignment = Alignment.End
            ) {
                Row {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(currencyImageUrl)
                            .crossfade(true)
                            .build(),
                        contentDescription = null,
                        modifier = Modifier
                            .width(20.dp)
                            .height(20.dp)
                            .clip(RoundedCornerShape(10.dp)),
                        contentScale = ContentScale.Crop
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = currencyPrefix.toString(),
                        style = MaterialTheme.typography.titleMedium
                    )
                }
            }
        }

    }
    fun Long.formatWithLocale(): String {
        return NumberFormat.getNumberInstance(Locale("id", "ID")).format(this)
    }

    val errorText = when {
        isBelowMin -> "Nominal minimal untuk transaksi adalah Rp${minValue?.formatWithLocale()}"
        isAboveBalance -> "Saldo tidak cukup"
        isAboveMax -> "Transaksi melebihi batas maksimum Rp${maxValue?.formatWithLocale()}"
        else -> ""
    }


    if (minValue != null) {
        if (minValue > 0) {
            if (isError) {
                validateInput(false)
                Text(
                    text = errorText,
                    color = Color_E84040,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp)
                )
            } else {
                validateInput(true)
                if (showMinimalText) {
                    Text(
                        text = "Minimal Rp${minValue}",
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp)
                    )
                }
            }
        }
    }
}


@Preview(showBackground = true)
@Composable
private fun NominalTextFieldPreview() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
//        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Normal state
        NominalTextField(
            value = "1000000",
            placeholder = "Masukkan nominal",
            maxValue = ********,
            minValue = 10000,
            onValueChange = {}
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Normal state with Flag
        NominalTextFieldWithFlag(
            value = "1000000",
            placeholder = "Masukkan nominal",
            maxValue = ********,
            minValue = 10000,
            onValueChange = {},
            flagImage = "IDR",
            flagName = "IDR"
        )

        Spacer(modifier = Modifier.height(16.dp))

        // CurrencyCentField
        CurrencyCentFieldWithFlag(
            value = "value",
            placeholder = "Masukkan nominal",
            onValueChange = {},
            flagImage = "IDR",
            flagName = "IDR",
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Error state - below minimum
        NominalTextField(
            value = "0",
            placeholder = "Masukkan nominal",
            maxValue = ********,
            minValue = 10000,
            onValueChange = {}
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Error state - above maximum
        NominalTextField(
            value = "********",
            placeholder = "Masukkan nominal",
            maxValue = ********,
            minValue = 10000,
            currencyImageUrl = "",
            onValueChange = {}
        )

        Spacer(modifier = Modifier.height(16.dp))

        BasicTextField(
            label = "Catatan",
            value = "22",
            onValueChange = {},
            visualTransformation = AccountNumberVisualTransformation()
        )

        Spacer(modifier = Modifier.height(16.dp))

        BasicTextField(
            label = "Catatan",
            value = "22",
            maxLength = 30,
            enableCounter = true,
            multiLine = true,
            note = "Contoh: Nama Jalan, No. Rumah, Kecamatan, dll",
            onValueChange = {},
            visualTransformation = AccountNumberVisualTransformation()
        )
    }
}


