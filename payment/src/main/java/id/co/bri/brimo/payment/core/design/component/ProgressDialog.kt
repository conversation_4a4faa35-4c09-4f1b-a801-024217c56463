package id.co.bri.brimo.payment.core.design.component

import android.graphics.RenderEffect
import android.graphics.Shader
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.theme.MainTheme

@Composable
internal fun ProgressDialog() {
    val dialogProperties =
        DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    val loadingComposition by rememberLottieComposition(
        spec = LottieCompositionSpec.RawRes(R.raw.loading_animation)
    )
    val context = LocalContext.current

    DisposableEffect(Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val blurEffect = RenderEffect.createBlurEffect(
                20f, 20f, Shader.TileMode.CLAMP
            )
            (context as? ComponentActivity)?.window?.decorView?.setRenderEffect(blurEffect)
        }

        onDispose {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                (context as? ComponentActivity)?.window?.decorView?.setRenderEffect(null)
            }
        }
    }

    Dialog(
        onDismissRequest = {},
        properties = dialogProperties
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            LottieAnimation(
                composition = loadingComposition,
                modifier = Modifier.size(200.dp),
                iterations = LottieConstants.IterateForever
            )
        }
    }
}

@Preview
@Composable
private fun PreviewProgressDialog() {
    MainTheme {
        ProgressDialog()
    }
}
