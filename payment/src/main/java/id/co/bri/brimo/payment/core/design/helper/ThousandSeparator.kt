package id.co.bri.brimo.payment.core.design.helper

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.text.NumberFormat

internal class ThousandSeparatorTransformation : VisualTransformation {

    override fun filter(text: AnnotatedString): TransformedText {
        val numberFormat = NumberFormat.getInstance() as DecimalFormat
        val decimalFormatSymbols = DecimalFormatSymbols().apply {
            groupingSeparator = '.'
            decimalSeparator = ','
        }
        numberFormat.decimalFormatSymbols = decimalFormatSymbols
        numberFormat.maximumFractionDigits = 2
        val decimalSeparator = decimalFormatSymbols.decimalSeparator

        var outputText = ""

        if (text.text.isNotEmpty()) {
            val number = text.text.toDoubleOrNull() ?: 0.0
            outputText += numberFormat.format(number)

            if (text.text.contains(decimalSeparator)) {
                val dotPart = text.text.substring(text.text.indexOf(decimalSeparator))
                if (dotPart.isNotEmpty()) {
                    outputText += dotPart
                }
            }
        }

        val numberOffsetTranslator = object : OffsetMapping {

            override fun originalToTransformed(offset: Int): Int {
                return outputText.length
            }

            override fun transformedToOriginal(offset: Int): Int {
                return text.length
            }
        }

        return TransformedText(
            text = AnnotatedString(outputText),
            offsetMapping = numberOffsetTranslator
        )
    }
}

internal class ThousandSeparatorFractionTransformation : VisualTransformation {

    override fun filter(text: AnnotatedString): TransformedText {
        val numberFormat = NumberFormat.getInstance() as DecimalFormat
        val decimalFormatSymbols = DecimalFormatSymbols().apply {
            groupingSeparator = '.'
            decimalSeparator = ','
        }
        numberFormat.decimalFormatSymbols = decimalFormatSymbols
        numberFormat.maximumFractionDigits = 2

        var outputText: String

        if (text.text.isNotEmpty()) {
            val number = text.text
            val padded = number.padStart(3, '0')
            val dollar = padded.dropLast(2).toDoubleOrNull() ?: 0.0
            val cents = padded.takeLast(2)
            val formattedDollar = numberFormat.format(dollar)
            outputText = "$formattedDollar,$cents"
        } else {
            outputText = "0,00"
        }

        val numberOffsetTranslator = object : OffsetMapping {

            override fun originalToTransformed(offset: Int): Int {
                return outputText.length
            }

            override fun transformedToOriginal(offset: Int): Int {
                return text.length
            }
        }

        return TransformedText(
            text = AnnotatedString(outputText),
            offsetMapping = numberOffsetTranslator
        )
    }
}
