package id.co.bri.brimo.payment.feature.briva.ui.form

import id.co.bri.brimo.payment.app.BrivaFormRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.FavoriteResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaFormResponse
import id.co.bri.brimo.payment.core.network.response.briva.BrivaInquiryResponse

internal data class BrivaFormState(
    val brivaFormRoute: BrivaFormRoute,
    val brivaForm: UiState<BrivaFormResponse> = UiState.Init,
    val brivaInquiry: UiState<BrivaInquiryResponse> = UiState.Init,
    val pinFavorite: UiState<Unit> = UiState.Init,
    val deleteFavorite: UiState<Unit> = UiState.Init,
    val favorites: List<FavoriteResponse> = listOf()
)

internal sealed class BrivaFormEvent {
    object RefreshBrivaForm : BrivaFormEvent()
    data class Inquiry(val number: String) : BrivaFormEvent()
    object ResetInquiry : BrivaFormEvent()
    data class PinFavorite(val savedId: String, val isPin: Boolean) : BrivaFormEvent()
    object ResetPinFavorite : BrivaFormEvent()
    data class DeleteFavorite(val savedId: String) : BrivaFormEvent()
    object ResetDeleteFavorite : BrivaFormEvent()
    data class EditFavorite(val savedId: String, val name: String) : BrivaFormEvent()
}

internal sealed class BrivaFormNavigation {
    object Back : BrivaFormNavigation()
    data class EditFavorite(
        val number: String,
        val name: String,
        val savedId: String,
        val subtitle: String
    ) : BrivaFormNavigation()

    data class Nominal(val brivaData: String, val fastMenu: Boolean) : BrivaFormNavigation()
    data class Confirmation(val brivaData: String, val fastMenu: Boolean) : BrivaFormNavigation()
}
