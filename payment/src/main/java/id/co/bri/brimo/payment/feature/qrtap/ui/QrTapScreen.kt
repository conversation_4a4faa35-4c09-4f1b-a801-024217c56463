package id.co.bri.brimo.payment.feature.qrtap.ui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.activity.ComponentActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.QrTapRoute
import id.co.bri.brimo.payment.core.common.data
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.WarningBottomSheet
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.errorSnackbar
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.dependency.PaymentDependency
import id.co.bri.brimo.payment.feature.brizzi.ui.pin.PinDialog
import id.co.bri.brimo.payment.feature.qris.ui.base.QrisAccountBottomSheet
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import java.util.Locale

@Composable
internal fun QrTapScreen(
    navigation: (QrTapNavigation) -> Unit = {},
    qrTapViewModel: QrTapViewModel = koinViewModel()
) {
    val qrTapPayload by qrTapViewModel.qrTapPayload.collectAsStateWithLifecycle()
    val accountList by qrTapViewModel.accountList.collectAsStateWithLifecycle()
    val qrTapCheckStatus by qrTapViewModel.qrTapCheckStatus.collectAsStateWithLifecycle()

    QrTapContent(
        state = QrTapState(
            qrTapRoute = qrTapViewModel.qrTapRoute,
            qrTapPayload = qrTapPayload,
            accountList = accountList.orEmpty(),
            qrTapCheckStatus = qrTapCheckStatus
        ),
        event = qrTapViewModel::handleEvent,
        navigation = navigation
    )
}

@Composable
private fun QrTapContent(
    state: QrTapState,
    event: (QrTapEvent) -> Unit = {},
    navigation: (QrTapNavigation) -> Unit = {}
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // Global
    val arrowComposition by rememberLottieComposition(
        spec = LottieCompositionSpec.RawRes(R.raw.arrows_animation)
    )
    val qrTapPayload = state.qrTapPayload.data()
    var selectedAccount: AccountResponse? by remember {
        mutableStateOf(state.accountList.find { it.default == 1 }
            ?: state.accountList.firstOrNull())
    }
    var selectedPin by rememberSaveable { mutableStateOf("") }
    var expireTime by rememberSaveable { mutableIntStateOf(qrTapPayload?.activeFor ?: 0) }
    val expireText by remember {
        derivedStateOf {
            String.format(
                Locale.getDefault(),
                "%02d:%02d",
                expireTime / 60,
                expireTime % 60
            )
        }
    }
    val expired by remember {
        derivedStateOf {
            qrTapPayload?.activeFor != null && expireTime == 0
        }
    }

    // Broadcast Receiver
    val nfcServiceReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            event(QrTapEvent.CheckStatus)
        }
    }

    DisposableEffect(Unit) {
        PaymentDependency.getPaymentApi()?.onInitPayment(
            context = context,
            activity = context as ComponentActivity,
            nfcServiceReceiver = nfcServiceReceiver
        )

        onDispose {
            PaymentDependency.getPaymentApi()?.onDestroyPayment(
                context = context,
                nfcServiceReceiver = nfcServiceReceiver
            )
        }
    }

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }
    var retry: QrTapEvent by remember { mutableStateOf(QrTapEvent.CheckStatus) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            event(retry)
        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Account
    var accountBottomSheet by rememberSaveable { mutableStateOf(false) }

    if (state.accountList.isNotEmpty()) {
        BottomSheet(
            showBottomSheet = accountBottomSheet,
            onShowBottomSheet = { accountBottomSheet = it }
        ) { dismiss ->
            QrisAccountBottomSheet(
                selectedAccount = selectedAccount?.account.orEmpty(),
                nominal = "",
                data = state.accountList,
                onSelect = { item ->
                    dismiss {
                        selectedAccount = item
                    }
                },
                onRefresh = { item ->
                    event(QrTapEvent.RefreshSaldo(account = item.account.orEmpty()))
                },
                onClose = {
                    dismiss {}
                },
                fastMenu = state.qrTapRoute.fastMenu
            )
        }
    }

    // Pin
    var pinDialog by rememberSaveable { mutableStateOf(false) }
    var pinMessage by rememberSaveable { mutableStateOf("") }
    var deletePin by remember { mutableStateOf({}) }

    if (pinDialog) {
        Dialog(
            onDismissRequest = { pinDialog = false },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                PinDialog(
                    error = pinMessage,
                    onBack = {
                        pinDialog = false
                    },
                    onPin = { pin ->
                        selectedPin = pin
                        val account =
                            if (selectedAccount != null && selectedAccount?.accountType != "CC") {
                                selectedAccount?.account.orEmpty()
                            } else {
                                ""
                            }
                        val cardToken =
                            if (selectedAccount != null && selectedAccount?.accountType == "CC") {
                                selectedAccount?.cardNumber.orEmpty()
                            } else {
                                ""
                            }
                        event(
                            QrTapEvent.Payload(
                                pin = selectedPin,
                                account = account,
                                cardToken = cardToken
                            )
                        )
                    },
                    deletePin = { action ->
                        deletePin = action
                    }
                )
            }
        }
    }

    // Warning
    var warningBottomSheet by rememberSaveable { mutableStateOf(false) }

    WarningBottomSheet(
        showBottomSheet = warningBottomSheet,
        onShowBottomSheet = { warningBottomSheet = it },
        title = "Waktu Tunggu Pembayaran Habis",
        description = "Sesi pembayaran berakhir karena tidak ada aktivitas. Silakan coba lagi.",
        primaryText = "Perbarui",
        secondaryText = "Batalkan Transaksi",
        onPrimary = {
            pinDialog = true
        },
        onSecondary = {
            PaymentDependency.getFinish()
        }
    )

    LaunchedEffect(expireTime) {
        if (expireTime > 0) {
            delay(1000)
            expireTime--
        } else {
            warningBottomSheet = true
        }
    }

    // Payload
    LaunchedEffect(state.qrTapPayload) {
        state.qrTapPayload
            .onLoading {
                progressDialog = true
            }
            .onSuccess { data ->
                progressDialog = false
                PaymentDependency.getPaymentApi()?.onStartPayment(
                    activity = context as ComponentActivity,
                    payload = data?.payload.orEmpty()
                )
                pinMessage = ""
                pinDialog = false
            }
            .onError { e ->
                progressDialog = false
                deletePin()
                if (e is MessageException && e.errorSnackbar()) {
                    if (e.description.contains("pin", true)) {
                        pinMessage = e.description
                    } else {
                        pinDialog = false
                        snackbarType = SnackbarType.ERROR
                        snackbarHostState.showSnackbar(e.description)
                    }
                } else {
                    error = e
                    val account =
                        if (selectedAccount != null && selectedAccount?.accountType != "CC") {
                            selectedAccount?.account.orEmpty()
                        } else {
                            ""
                        }
                    val cardToken =
                        if (selectedAccount != null && selectedAccount?.accountType == "CC") {
                            selectedAccount?.cardNumber.orEmpty()
                        } else {
                            ""
                        }
                    retry = QrTapEvent.Payload(
                        pin = selectedPin,
                        account = account,
                        cardToken = cardToken
                    )
                    errorBottomSheet = true
                }
            }
    }

    // CheckStatus
    LaunchedEffect(state.qrTapCheckStatus) {
        state.qrTapCheckStatus
            .onSuccess { data ->
                if (data.referenceNumber.isNullOrEmpty()) {
                    scope.launch {
                        delay(5000)
                        if (!expired) {
                            event(QrTapEvent.CheckStatus)
                        }
                    }
                } else {
                    val qrTapData = data.serialize().orEmpty()
                    if (qrTapData.isEmpty()) {
                        retry = QrTapEvent.CheckStatus
                        errorBottomSheet = true
                    } else {
                        navigation(QrTapNavigation.Payment(qrTapData))
                    }
                }
                event(QrTapEvent.ResetCheckStatus)
            }
            .onError { e ->
                PaymentDependency.getPaymentApi()?.onStartPayment(
                    activity = context as ComponentActivity,
                    payload = ""
                )
                if (e is MessageException && e.code == "EX") {
                    warningBottomSheet = true
                } else if (e is MessageException && e.errorSnackbar()) {
                    if (e.code != "01") {
                        snackbarType = SnackbarType.ERROR
                        snackbarHostState.showSnackbar(e.description)
                    }
                    delay(5000)
                    event(QrTapEvent.CheckStatus)
                } else {
                    error = e
                    retry = QrTapEvent.CheckStatus
                    errorBottomSheet = true
                }
                event(QrTapEvent.ResetCheckStatus)
            }
    }

    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.park_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(QrTapNavigation.Back)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "QRIS Tap",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                Image(
                    painter = painterResource(R.drawable.image_tap),
                    contentDescription = null,
                    modifier = Modifier
                        .width(270.dp)
                        .height(240.dp)
                        .align(Alignment.CenterHorizontally),
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.height(24.dp))

                Text(
                    text = "Bayar dengan QRIS Tap",
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.titleLarge
                )

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = qrTapPayload?.tapGuidance.orEmpty()
                        .ifEmpty { "Dekatkan atau tempel HP ke mesin pembayaran" },
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    val name =
                        selectedAccount?.name ?: qrTapPayload?.cardHolder.orEmpty()

                    Text(
                        text = name,
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyLarge
                    )

                    val account =
                        selectedAccount?.accountString ?: qrTapPayload?.accountNo.orEmpty()

                    Text(
                        text = account,
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodySmall
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    if (!state.qrTapRoute.fastMenu) {
                        var hide by rememberSaveable { mutableStateOf(false) }
                        val balance = if (hide) {
                            "••••••"
                        } else {
                            selectedAccount?.balanceString ?: "-"
                        }

                        Row(
                            modifier = Modifier.align(Alignment.CenterHorizontally),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                painter = painterResource(R.drawable.flag_indonesia),
                                contentDescription = null,
                                modifier = Modifier.size(24.dp),
                                contentScale = ContentScale.Fit
                            )

                            Spacer(modifier = Modifier.width(12.dp))

                            Text(
                                text = selectedAccount?.currency.orEmpty() + balance,
                                fontWeight = FontWeight.SemiBold,
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.headlineSmall
                            )

                            Spacer(modifier = Modifier.width(12.dp))

                            Image(
                                painter = painterResource(R.drawable.icon_unhide_eye),
                                contentDescription = null,
                                modifier = Modifier
                                    .size(24.dp)
                                    .clickable {
                                        hide = !hide
                                    },
                                contentScale = ContentScale.Fit
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    if (!expired) {
                        Row(
                            modifier = Modifier.align(Alignment.CenterHorizontally),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Diproses transaksi",
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Image(
                                painter = painterResource(R.drawable.icon_clock),
                                contentDescription = null,
                                modifier = Modifier.size(16.dp),
                                contentScale = ContentScale.Fit
                            )

                            Spacer(modifier = Modifier.width(4.dp))

                            Text(
                                text = expireText,
                                color = Color_0054F3,
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }

                    Spacer(modifier = Modifier.weight(1f))

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .pointerInput(Unit) {
                                detectVerticalDragGestures(
                                    onVerticalDrag = { change, dragAmount ->
                                        if (dragAmount <= -27) {
                                            accountBottomSheet = true
                                        }
                                    }
                                )
                            }
                    ) {
                        Spacer(modifier = Modifier.height(24.dp))

                        LottieAnimation(
                            composition = arrowComposition,
                            modifier = Modifier
                                .width(24.dp)
                                .height(32.dp)
                                .align(Alignment.CenterHorizontally),
                            iterations = LottieConstants.IterateForever
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "Geser untuk ganti sumber dana",
                            modifier = Modifier.fillMaxWidth(),
                            color = Color_0054F3,
                            fontWeight = FontWeight.SemiBold,
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(24.dp))
                    }

                    Spacer(modifier = Modifier.height(24.dp))
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewQrTap() {
    MainTheme {
        QrTapContent(
            state = QrTapState(
                qrTapRoute = QrTapRoute(
                    data = "",
                    fastMenu = false
                ),
                accountList = listOf(
                    AccountResponse(
                        account = "***************",
                        accountString = "1234 5678 9012 345",
                        name = "Name",
                        currency = "Rp",
                        cardNumber = "****************",
                        cardNumberString = "0987 XXXX XXXX 8765",
                        productType = "",
                        accountType = "",
                        scCode = "",
                        default = 0,
                        alias = "Alias",
                        minimumBalance = "",
                        limit = "",
                        limitString = "",
                        imageName = "",
                        imagePath = "",
                        onHold = false,
                        balance = "9000000",
                        balanceString = "9.000.000,00"
                    )
                )
            )
        )
    }
}
