package id.co.bri.brimo.payment.feature.qrtap.ui

import id.co.bri.brimo.payment.app.QrTapRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapCheckStatusResponse
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapPayloadResponse

internal data class QrTapState(
    val qrTapRoute: QrTapRoute,
    val qrTapPayload: UiState<QrTapPayloadResponse?> = UiState.Init,
    val accountList: List<AccountResponse> = emptyList(),
    val qrTapCheckStatus: UiState<QrTapCheckStatusResponse> = UiState.Init
)

internal sealed class QrTapEvent {
    data class RefreshSaldo(val account: String) : QrTapEvent()
    data class Payload(
        val pin: String,
        val account: String,
        val cardToken: String
    ) : QrTapEvent()

    object CheckStatus : QrTapEvent()
    object ResetCheckStatus : QrTapEvent()
}

internal sealed class QrTapNavigation {
    object Back : QrTapNavigation()
    data class Payment(val data: String) : QrTapNavigation()
}
