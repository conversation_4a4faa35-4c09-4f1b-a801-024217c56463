package id.co.bri.brimo.payment.app

import android.net.Uri
import android.os.Bundle

private const val DESTINATION = "destination"
private const val FAST_MENU = "fromfastmenu"

private const val BRIZZI_FORM = "brizzi_form"
private const val BRIVA_FORM = "briva_form"
private const val TRANSFER_FORM = "transfer_form"
private const val QRIS_SCAN = "qris_scan"
private const val RECEIPT = "receipt"
private const val PENDING_DATA = "pending_data"

internal fun handleDeeplink(
    uri: Uri?,
    extras: Bundle?
): Any {
    val destination = extras?.getString(DESTINATION, "").orEmpty()
    val fastMenu = extras?.getBoolean(FAST_MENU, false) ?: false

    return when (destination) {
        BRIZZI_FORM -> BrizziFormRoute(fastMenu)
        BRIVA_FORM -> BrivaFormRoute(fastMenu)
        TRANSFER_FORM -> TransferFormRoute(fastMenu)
        QRIS_SCAN -> QrisScanRoute(fastMenu)
        RECEIPT -> {
            val data = extras?.getString(PENDING_DATA, "").orEmpty()
            ReceiptDailyBankingRoute(data)
        }

        else -> ""
    }
}