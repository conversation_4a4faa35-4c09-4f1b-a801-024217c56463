package id.co.bri.brimo.payment.feature.qris.ui.park.status

import android.Manifest
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.Notice
import id.co.bri.brimo.payment.core.design.component.NoticeType
import id.co.bri.brimo.payment.core.design.component.PrimaryButton
import id.co.bri.brimo.payment.core.design.component.SecondaryButtonSmall
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.saveToDisk
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse
import id.co.bri.brimo.payment.core.network.response.qris.InputDataFrom
import id.co.bri.brimo.payment.core.network.response.qris.QrisParkConfirmationResponse
import id.co.bri.brimo.payment.core.network.response.qris.QrisScanResponse
import id.co.bri.brimo.payment.feature.brizzi.ui.receipt.composableToBitmap
import id.co.bri.brimo.payment.feature.brizzi.ui.receipt.generateBarCode
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun QrisParkStatusScreen(
    onFinish: () -> Unit = {},
    qrisParkStatusViewModel: QrisParkStatusViewModel = koinViewModel()
) {
    if (qrisParkStatusViewModel.qrisModel != null)
        QrisParkStatusContent(
            data = qrisParkStatusViewModel.qrisModel,
            onFinish = onFinish
        )
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun QrisParkStatusContent(
    data: QrisModel,
    onFinish: () -> Unit = {}
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    val writeStorageAccessState = rememberMultiplePermissionsState(
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            emptyList()
        } else {
            listOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    )

    fun shareBitmapFromComposable() {
        if (writeStorageAccessState.allPermissionsGranted) {
            coroutineScope.launch {
                try {
                    val bitmap = composableToBitmap(
                        mainScope = coroutineScope,
                        activity = context as ComponentActivity,
                        width = configuration.screenWidthDp.dp,
                        screenDensity = density
                    ) {
                        MainTheme {
                            QrisParkView(data.qrisScan!!)
                        }
                    }
                    bitmap.saveToDisk(context)
                    coroutineScope.launch {
                        snackbarType = SnackbarType.SUCCESS
                        snackbarHostState.showSnackbar("Kode Parkir berhasil disimpan.")
                    }
                } catch (_: Throwable) {
                    coroutineScope.launch {
                        snackbarType = SnackbarType.ERROR
                        snackbarHostState.showSnackbar("Kode Parkir gagal disimpan.")
                    }
                }
            }
        } else if (writeStorageAccessState.shouldShowRationale) {
            coroutineScope.launch {
                snackbarType = SnackbarType.INFO
                val result = snackbarHostState.showSnackbar(
                    message = "The storage permission is needed to save the image",
                    actionLabel = "Grant Access"
                )

                if (result == SnackbarResult.ActionPerformed) {
                    writeStorageAccessState.launchMultiplePermissionRequest()
                }
            }
        } else {
            writeStorageAccessState.launchMultiplePermissionRequest()
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                PrimaryButton(
                    label = "OK",
                    modifier = Modifier.fillMaxWidth()
                ) {
                    onFinish()
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .verticalScroll(rememberScrollState())
                    .padding(innerPadding)
            ) {
                Spacer(modifier = Modifier.height(24.dp))

                Image(
                    painter = painterResource(R.drawable.image_receipt_success),
                    contentDescription = null,
                    modifier = Modifier
                        .size(120.dp)
                        .align(Alignment.CenterHorizontally),
                    contentScale = ContentScale.Fit
                )

                Text(
                    text = data.qrisParkConfirmation?.title.orEmpty(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.titleLarge
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = data.qrisParkConfirmation?.desc.orEmpty(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .background(
                            Color.White,
                            RoundedCornerShape(
                                topStart = 16.dp,
                                topEnd = 64.dp,
                                bottomStart = 16.dp,
                                bottomEnd = 16.dp
                            )
                        )
                        .padding(horizontal = 16.dp, vertical = 24.dp)
                ) {
                    Text(
                        text = "Tiket Parkir Aktif Kamu",
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    if (data.qrisScan?.nomorTiket?.isNotEmpty() == true) {
                        val widthPx =
                            with(density) { configuration.screenWidthDp.dp.toPx().toInt() }
                        val heightPx =
                            with(density) { 72.dp.toPx().toInt() }
                        val bitmap = remember(data.qrisScan.nomorTiket) {
                            generateBarCode(data.qrisScan.nomorTiket, widthPx, heightPx)
                        }

                        Image(
                            bitmap = bitmap.asImageBitmap(),
                            contentDescription = null,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = data.qrisScan?.nomorTiket.orEmpty(),
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodySmall
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    SecondaryButtonSmall(
                        label = "Simpan Kode Parkir",
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    ) {
                        shareBitmapFromComposable()
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    DividerHorizontal()

                    Spacer(modifier = Modifier.height(24.dp))

                    data.qrisScan?.billingDetail?.forEachIndexed { index, item ->
                        ItemBilling(
                            name = item.name.orEmpty(),
                            value = item.value.orEmpty(),
                            notLast = index < data.qrisScan.billingDetail.size - 1
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Sumber Dana",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                            .padding(16.dp)
                    ) {
                        AsyncImage(
                            model = data.accountSelected?.imagePath,
                            contentDescription = null,
                            modifier = Modifier
                                .width(58.dp)
                                .height(36.dp),
                            placeholder = painterResource(id = R.drawable.thumbnail),
                            error = painterResource(id = R.drawable.thumbnail),
                            contentScale = ContentScale.Inside
                        )

                        Spacer(modifier = Modifier.width(16.dp))

                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = data.accountSelected?.alias.orEmpty()
                                    .ifEmpty { data.accountSelected?.name.orEmpty() },
                                modifier = Modifier.fillMaxWidth(),
                                style = MaterialTheme.typography.bodySmall
                            )

                            Text(
                                text = data.accountSelected?.accountString.orEmpty(),
                                modifier = Modifier.fillMaxWidth(),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    Notice(
                        description = "Kamu bisa menutup Qitta dan melanjutkan aktivitas.",
                        type = NoticeType.INFO
                    )

                    Spacer(modifier = Modifier.height(24.dp))
                }
            }
        }
    }
}

@Composable
private fun ItemBilling(
    name: String,
    value: String,
    notLast: Boolean
) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = name,
            modifier = Modifier.weight(1f),
            color = Color_7B90A6,
            style = MaterialTheme.typography.bodyMedium
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = value,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.End,
            style = MaterialTheme.typography.bodyMedium
        )
    }

    if (notLast) {
        Spacer(modifier = Modifier.height(12.dp))
    }
}

@Preview(heightDp = 960)
@Composable
private fun PreviewQrisParkStatus() {
    MainTheme {
        QrisParkStatusContent(
            data = QrisModel(
                qrisScan = QrisScanResponse(
                    accountList = listOf(),
                    billingDetail = listOf(
                        DataViewResponse(
                            name = "Waktu Masuk",
                            value = "12:34 WIB",
                            style = ""
                        ),
                        DataViewResponse(
                            name = "Tanggal",
                            value = "25 Juni 2025",
                            style = ""
                        ),
                        DataViewResponse(
                            name = "Lokasi",
                            value = "Senayan City",
                            style = ""
                        )
                    ),
                    billingDetailOpen = listOf(
                        BillingResponse(
                            listType = "",
                            iconName = "",
                            iconPath = "",
                            title = "Title",
                            subtitle = "Subtitle",
                            description = "Description"
                        )
                    ),
                    billingAmount = listOf(),
                    billingAmountDetail = listOf(),
                    inputDataFrom = InputDataFrom(
                        countryName = "",
                        countryCode = "",
                        currencyCode = "",
                        iconName = "",
                        iconPath = "",
                        inputName = "",
                        inputValue = 0
                    ),
                    information = DataViewResponse(
                        name = "",
                        value = "",
                        style = ""
                    ),
                    openPayment = true,
                    isBilling = false,
                    minimumPayment = false,
                    amountEditable = true,
                    rowDataShow = 0,
                    saved = "",
                    amount = "",
                    amountString = "",
                    minimumAmount = "",
                    minimumAmountString = "",
                    adminFee = "",
                    adminFeeString = "",
                    payAmount = "",
                    payAmountString = "",
                    minimumTransaction = "",
                    minimumTransactionString = "",
                    tipOption = listOf(),
                    tipEditable = false,
                    tipType = "",
                    tipAmount = "",
                    tipAmountString = "",
                    typeQr = "parking_spi",
                    merchantName = "",
                    merchantCountry = "",
                    nomorTiket = "DJSH8127SD3298393",
                    referenceNumber = ""
                ),
                qrisParkConfirmation = QrisParkConfirmationResponse(
                    title = "Tiket Parkir Terkonfirmasi",
                    desc = "Saldo rekeningmu akan otomatis terpotong saat tiket berhasil di-scan di pintu keluar. Pastikan saldomu cukup untuk transaksi ya!"
                ),
                accountSelected = AccountResponse(
                    account = "***************",
                    accountString = "1234 5678 9012 345",
                    name = "Name",
                    currency = "Rp",
                    cardNumber = "****************",
                    cardNumberString = "0987 XXXX XXXX 8765",
                    productType = "",
                    accountType = "",
                    scCode = "",
                    default = 0,
                    alias = "Alias",
                    minimumBalance = "",
                    limit = "",
                    limitString = "",
                    imageName = "",
                    imagePath = "",
                    onHold = false,
                    balance = "9000000",
                    balanceString = "9.000.000,00"
                )
            ),
            onFinish = {}
        )
    }
}
