package id.co.bri.brimo.payment.feature.brizzi.ui.pin

import androidx.activity.ComponentActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.text.isDigitsOnly
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_E9EEF6
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.dependency.PaymentDependency

@OptIn(ExperimentalLayoutApi::class)
@Composable
internal fun PinDialog(
    error: String = "",
    onBack: () -> Unit = {},
    onPin: (String) -> Unit = {},
    deletePin: (() -> Unit) -> Unit = {}
) {
    val context = LocalContext.current

    Column(modifier = Modifier.fillMaxSize()) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(R.drawable.icon_back_filled),
                contentDescription = null,
                modifier = Modifier
                    .size(32.dp)
                    .clickable {
                        onBack()
                    },
                contentScale = ContentScale.Fit
            )

            Text(
                text = "PIN",
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = 32.dp),
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.titleLarge
            )
        }

        Spacer(modifier = Modifier.height(48.dp))

        Text(
            text = "Masukkan PIN",
            modifier = Modifier.align(Alignment.CenterHorizontally),
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyLarge
        )

        Spacer(modifier = Modifier.height(24.dp))

        var pin by rememberSaveable { mutableStateOf("") }
        deletePin { pin = "" }

        Row(
            modifier = Modifier.align(Alignment.CenterHorizontally),
            horizontalArrangement = Arrangement.spacedBy(32.dp)
        ) {
            repeat(6) { index ->
                val filled = pin.getOrNull(index) != null
                val color = if (filled) Color_0054F3 else Color_E9EEF6
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .background(color, CircleShape)
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        if (error.isNotEmpty()) {
            Text(
                text = error,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp),
                color = Color_E84040,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall
            )

            Spacer(modifier = Modifier.height(24.dp))
        }

        Text(
            text = "Lupa PIN?",
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .clickable {
                    PaymentDependency.getPaymentApi()?.onPin(context as ComponentActivity)
                },
            color = Color_0054F3,
            fontWeight = FontWeight.SemiBold,
            style = MaterialTheme.typography.bodyMedium
        )

        Spacer(modifier = Modifier.weight(1f))

        val listNumber = listOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "", "0", "←")

        FlowRow(
            modifier = Modifier.padding(horizontal = 14.dp, vertical = 48.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            maxItemsInEachRow = 3
        ) {
            listNumber.forEach { num ->
                val isDigit = num.isNotEmpty() && num.isDigitsOnly()
                Box(
                    modifier = Modifier
                        .padding(10.dp)
                        .height(84.dp)
                        .weight(1f)
                        .clip(RoundedCornerShape(16.dp))
                        .background(if (isDigit) Color_F5F7FB else Color.Transparent)
                        .clickable(enabled = num.isNotEmpty()) {
                            if (isDigit) {
                                if (pin.length < 6) pin += num
                            } else {
                                if (pin.isNotEmpty()) pin = pin.dropLast(1)
                            }

                            if (pin.length == 6) {
                                onPin(pin)
                            }
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = num,
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.displaySmall
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewPin() {
    MainTheme {
        PinDialog()
    }
}
