package id.co.bri.brimo.payment.feature.qris.ui.confirmation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrisConfirmationRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qris.QrisPaymentRequest
import id.co.bri.brimo.payment.core.network.response.qris.QrisPaymentResponse
import id.co.bri.brimo.payment.feature.qris.data.api.QrisRepository
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrisConfirmationViewModel(
    private val qrisRepository: QrisRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrisConfirmationRoute = savedStateHandle.toRoute<QrisConfirmationRoute>()

    val qrisModel = runCatching {
        Gson().fromJson(qrisConfirmationRoute.data, QrisModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(qrisModel?.qrisScan?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrisConfirmationEvent) {
        when (event) {
            is QrisConfirmationEvent.Payment -> {
                postQrisPay(
                    pin = event.pin,
                    accountNumber = event.accountNumber,
                    note = event.note
                )
            }

            QrisConfirmationEvent.ResetPayment -> {
                resetPayment()
            }

            is QrisConfirmationEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _qrisPayment = MutableStateFlow<UiState<QrisPaymentResponse>>(UiState.Init)
    val qrisPayment = _qrisPayment.asStateFlow()

    private fun resetPayment() {
        viewModelScope.launch {
            _qrisPayment.update { UiState.Init }
        }
    }

    private fun postQrisPay(
        pin: String,
        accountNumber: String,
        note: String
    ) {
        viewModelScope.launch {
            _qrisPayment.asUiState {
                val request = if (qrisModel?.qrisScan != null) {
                    val accountNumber =
                        if (qrisModel.typeQr == "qris_cb") null else accountNumber
                    val pfmCategory =
                        if (qrisModel.typeQr == "qris_transfer") 17 else qrisModel.qrisConfirmation?.pfmCategory
                    val referenceNumber =
                        if (qrisModel.typeQr == "qris_transfer") {
                            qrisModel.qrisScan.referenceNumber
                        } else {
                            qrisModel.qrisConfirmation?.referenceNumber
                        }
                    QrisPaymentRequest(
                        accountNumber = accountNumber,
                        note = note,
                        pfmCategory = pfmCategory,
                        pin = pin,
                        referenceNumber = referenceNumber,
                    )
                } else {
                    val accountNumber =
                        if (qrisModel?.typeQr == "qris_cb") null else qrisModel?.accountNumber
                    QrisPaymentRequest(
                        accountNumber = accountNumber,
                        note = qrisModel?.qrisConfirmation?.note.orEmpty(),
                        pfmCategory = qrisModel?.qrisConfirmation?.pfmCategory,
                        pin = pin,
                        referenceNumber = qrisModel?.qrisConfirmation?.referenceNumber,
                    )
                }
                qrisRepository.postQrisPay(
                    type = qrisModel?.typeQr.orEmpty(),
                    request = request,
                    fastMenu = qrisConfirmationRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrisRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
