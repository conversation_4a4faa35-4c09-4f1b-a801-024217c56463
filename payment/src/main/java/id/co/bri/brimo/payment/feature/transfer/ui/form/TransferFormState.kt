package id.co.bri.brimo.payment.feature.transfer.ui.form

import id.co.bri.brimo.payment.core.network.request.AddFavoriteRequest
import id.co.bri.brimo.payment.core.network.request.FavoriteRequest
import id.co.bri.brimo.payment.core.network.request.InquiryAddFavoriteRequest
import id.co.bri.brimo.payment.core.network.request.UpdateFavoriteRequest
import id.co.bri.brimo.payment.feature.brizzi.ui.form.BrizziFormEvent
import id.co.bri.brimo.payment.feature.transfer.data.model.BankData

internal data class TransferFormState(
    val bankList: List<BankData>
)

internal sealed class TransferFormEvent {
    object GetTransferFormData : TransferFormEvent()
    data class RefreshTransferForm(val isFastMenu: Boolean) : TransferFormEvent()
    data class InquiryAddFavorite(val request: InquiryAddFavoriteRequest) : TransferFormEvent()
    data class AddFavorite(val request: AddFavoriteRequest) : TransferFormEvent()
    data class UpdateFavorite(val request: UpdateFavoriteRequest) : TransferFormEvent()
    data class PinFavorite(val request: FavoriteRequest) : TransferFormEvent()
    data class UnpinFavorite(val request: FavoriteRequest) : TransferFormEvent()
    data class DeleteFavorite(val request: FavoriteRequest) : TransferFormEvent()
}

internal sealed class TransferFormNavigation {
    object Back : TransferFormNavigation()
    data class EditFavorite(
        val isAddFavorite: Boolean
    ) : TransferFormNavigation()
    data class AddFavorite(
        val bankCode: String,
        val bankName: String,
        val imageUrl: String,
        val isAddFavorite: Boolean
    ) : TransferFormNavigation()
    data class ToInputNominal(
        val imageUrl: String,
        val favName: String
    ) : TransferFormNavigation()
    object BackToTransferForm : TransferFormNavigation()
} 