package id.co.bri.brimo.payment.feature.qrshow.ui

import id.co.bri.brimo.payment.app.QrShowRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmCheckStatusResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmGenerateResponse

internal data class QrShowState(
    val qrShowRoute: QrShowRoute,
    val accountList: List<AccountResponse> = emptyList(),
    val qrCpmGenerate: UiState<QrCpmGenerateResponse> = UiState.Init,
    val qrCpmCheckStatus: UiState<QrCpmCheckStatusResponse> = UiState.Init
)

internal sealed class QrShowEvent {
    object RefreshAccountList : QrShowEvent()
    data class RefreshSaldo(val account: String) : QrShowEvent()
    data class Generate(val accountNumber: String, val pin: String) : QrShowEvent()
    object CheckStatus : QrShowEvent()
    object ResetCheckStatus : QrShowEvent()
}

internal sealed class QrShowNavigation {
    object Back : QrShowNavigation()
    data class Payment(val data: String) : QrShowNavigation()
}
