package id.co.bri.brimo.payment.feature.qris.ui.scan

import id.co.bri.brimo.payment.app.QrisScanRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapPayloadResponse
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel

internal data class QrisScanState(
    val qrisScanRoute: QrisScanRoute,
    val qrScan: UiState<QrisModel> = UiState.Init,
    val qrTapPayload: UiState<QrTapPayloadResponse> = UiState.Init
)

internal sealed class QrisScanEvent {
    data class Scan(val stringQr: String) : QrisScanEvent()
    object ResetScan : QrisScanEvent()
    data class Payload(val pin: String) : QrisScanEvent()
    object ResetPayload : QrisScanEvent()
}

internal sealed class QrisScanNavigation {
    object Back : QrisScanNavigation()
    object QrTransfer : QrisScanNavigation()
    object QrShow : QrisScanNavigation()
    data class QrTap(val qrisData: String) : QrisScanNavigation()
    data class Nominal(
        val qrisData: String,
        val type: String,
        val fastMenu: Boolean
    ) : QrisScanNavigation()

    data class Confirmation(
        val qrisData: String,
        val fastMenu: Boolean
    ) : QrisScanNavigation()

    data class Park(
        val qrisData: String,
        val fastMenu: Boolean
    ) : QrisScanNavigation()
}
