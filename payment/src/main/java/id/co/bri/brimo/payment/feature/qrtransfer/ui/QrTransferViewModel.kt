package id.co.bri.brimo.payment.feature.qrtransfer.ui

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import id.co.bri.brimo.payment.app.QrTransferRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.common.getDateTimeSecond
import id.co.bri.brimo.payment.core.common.refresh
import id.co.bri.brimo.payment.core.common.uiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrtransfer.QrTransferGenerateRequest
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qrtransfer.QrTransferGenerateResponse
import id.co.bri.brimo.payment.feature.qrtransfer.data.api.QrTransferRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrTransferViewModel(
    private val qrTransferRepository: QrTransferRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrTransferRoute = savedStateHandle.toRoute<QrTransferRoute>()

    private val _accountList = MutableStateFlow<List<AccountResponse>?>(null)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrTransferEvent) {
        when (event) {
            QrTransferEvent.RefreshAccountList -> {
                refreshQrAccountList()
            }

            is QrTransferEvent.Generate -> {
                generateQrTransfer(accountNumber = event.accountNumber, amount = event.amount)
            }
        }
    }

    private val refreshQrAccountList = MutableStateFlow(false)
    private fun refreshQrAccountList() = refreshQrAccountList.refresh()

    val qrAccountList = refreshQrAccountList.flatMapLatest {
        callbackFlow {
            send(UiState.Loading)
            try {
                val qrAccountList = qrTransferRepository.postAccountList(
                    fastMenu = qrTransferRoute.fastMenu
                )

                val accountList = if (!qrTransferRoute.fastMenu) {
                    qrAccountList.account?.map { account ->
                        async {
                            try {
                                val saldoNormal = qrTransferRepository.postSaldoNormal(
                                    request = SaldoNormalRequest(
                                        account = account.account.orEmpty()
                                    )
                                )
                                account.copy(
                                    onHold = saldoNormal.onHold,
                                    balance = saldoNormal.balance,
                                    balanceString = saldoNormal.balanceString
                                )
                            } catch (_: Throwable) {
                                account
                            }
                        }
                    }?.awaitAll()
                } else {
                    qrAccountList.account
                }

                _accountList.update { accountList }

                val mainAccount = accountList?.find { it.default == 1 }
                    ?: accountList?.firstOrNull()

                generateQrTransfer(accountNumber = mainAccount?.account.orEmpty(), amount = "")

                send(UiState.Success(Unit))
            } catch (error: Throwable) {
                send(UiState.Error(error))
            } finally {
                close()
            }
        }
    }.uiState(viewModelScope)

    private val _qrTransferGenerate =
        MutableStateFlow<UiState<QrTransferGenerateResponse>>(UiState.Init)
    val qrTransferGenerate = _qrTransferGenerate.asStateFlow()

    private fun generateQrTransfer(accountNumber: String, amount: String) {
        viewModelScope.launch {
            _qrTransferGenerate.asUiState {
                val qrTransferGenerate = qrTransferRepository.postQrTransferGenerate(
                    request = QrTransferGenerateRequest(
                        accountNumber = accountNumber,
                        amount = amount.ifEmpty { "0" },
                        isNewQr = true
                    ),
                    fastMenu = qrTransferRoute.fastMenu
                )
                val qrTransferGenerateUpdated = qrTransferGenerate.copy(
                    expired = getDateTimeSecond(qrTransferGenerate.expired?.toIntOrNull() ?: 0)
                )
                qrTransferGenerateUpdated
            }
        }
    }
}
