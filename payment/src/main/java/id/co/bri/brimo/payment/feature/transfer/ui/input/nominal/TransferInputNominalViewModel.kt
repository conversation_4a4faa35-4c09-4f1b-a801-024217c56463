package id.co.bri.brimo.payment.feature.transfer.ui.input.nominal

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.BrizziPaymentRequest
import id.co.bri.brimo.payment.core.network.request.ListCityRequest
import id.co.bri.brimo.payment.core.network.request.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.TransferConfirmationRequest
import id.co.bri.brimo.payment.core.network.request.TransferConfirmationRtgsRequest
import id.co.bri.brimo.payment.core.network.response.BrizziPaymentResponse
import id.co.bri.brimo.payment.core.network.response.ListCityResponse
import id.co.bri.brimo.payment.core.network.response.SaldoNormalData
import id.co.bri.brimo.payment.core.network.response.TransferConfirmationData
import id.co.bri.brimo.payment.feature.transfer.data.api.TransferRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

internal class TransferInputNominalViewModel(
    private val transferRepository: TransferRepository
) : ViewModel() {

    private val _state = MutableStateFlow(TransferInputNominalState())
    val state: StateFlow<TransferInputNominalState> = _state.asStateFlow()

    private val _navigation = MutableSharedFlow<TransferInputNominalNavigation>()
    val navigation = _navigation.asSharedFlow()

    private val _saldoNormalData = MutableStateFlow<UiState<List<SaldoNormalData>>?>(null)
    val saldoNormalData = _saldoNormalData.asStateFlow()

    private val _confirmationData = MutableStateFlow<UiState<TransferConfirmationData>?>(null)
    val confirmationData = _confirmationData.asStateFlow()

    private val _listCityData = MutableStateFlow<UiState<ListCityResponse>?>(null)
    val listCityData = _listCityData.asStateFlow()

    private val _confirmationRtgsData = MutableStateFlow<UiState<TransferConfirmationData>?>(null)
    val confirmationRtgsData = _confirmationRtgsData.asStateFlow()

    var lastAccountNumberList: List<String>? = null

    fun handleEvent(event: TransferInputNominalEvent) {
        when (event) {
            is TransferInputNominalEvent.PostSaldoNormal -> {
                postSaldoNormal(
                    accountNumberList = event.accountNumberList
                )
            }

            is TransferInputNominalEvent.Confirmation -> {
                postConfirmation(
                    isFastMenu = event.isFastMenu,
                    request = event.request
                )
            }

            is TransferInputNominalEvent.PostListCity -> {
                postListCity(
                    request = ListCityRequest(event.bicCode)
                )
            }

            is TransferInputNominalEvent.PostConfirmationRtgs -> {
                postConfirmationRtgs(
                    isFastMenu = event.isFastMenu,
                    request = event.request
                )
            }
        }
    }

    fun refreshSaldoNormal() {
        lastAccountNumberList?.let { accounts ->
            postSaldoNormal(accounts)
        }
    }

    fun postSaldoNormal(
        accountNumberList: List<String>
    ) {
        lastAccountNumberList = accountNumberList
        viewModelScope.launch {
            _saldoNormalData.emit(UiState.Loading)
            try {
                val responses = mutableListOf<SaldoNormalData>()
                accountNumberList.map { accountNumber ->
                    async {
                        val response = transferRepository.postSaldoNormal(
                            request = SaldoNormalRequest(
                                account = accountNumber
                            )
                        )
                        responses.add(response)
                    }
                }.awaitAll()
                _saldoNormalData.emit(UiState.Success(responses))
            } catch (e: Throwable) {
                _saldoNormalData.emit(UiState.Error(e))
            }
        }
    }

    fun postConfirmation(isFastMenu: Boolean, request: TransferConfirmationRequest) {
        viewModelScope.launch {
            _confirmationData.emit(UiState.Loading)
            try {
                val response = transferRepository.postTransferConfirmation(
                    isFastMenu = isFastMenu,
                    request = request
                )
                _confirmationData.emit(UiState.Success(response))
            } catch (e: Throwable) {
                _confirmationData.emit(UiState.Error(e))
            }
        }
    }

    fun postListCity(request: ListCityRequest) {
        viewModelScope.launch {
            _listCityData.emit(UiState.Loading)
            try {
                val response = transferRepository.postListCity(
                    request = request
                )
                _listCityData.emit(UiState.Success(response))
            } catch (e: Throwable) {
                _listCityData.emit(UiState.Error(e))
            }
        }
    }

    private fun postConfirmationRtgs(isFastMenu: Boolean, request: TransferConfirmationRtgsRequest) {
        viewModelScope.launch {
            _confirmationRtgsData.emit(UiState.Loading)
            try {
                val response = transferRepository.postTransferConfirmationRtgs(
                    isFastMenu = isFastMenu,
                    request = request
                )
                _confirmationRtgsData.emit(UiState.Success(response))
            } catch (e: Throwable) {
                _confirmationRtgsData.emit(UiState.Error(e))
            }
        }
    }

    fun clearConfirmationData() {
        _saldoNormalData.value = UiState.Init
        _confirmationData.value = UiState.Init
        _confirmationRtgsData.value = UiState.Init
    }

    fun navigateBack() {
        viewModelScope.launch {
            _navigation.emit(TransferInputNominalNavigation.Back)
        }
    }
} 