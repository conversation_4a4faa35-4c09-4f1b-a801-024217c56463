package id.co.bri.brimo.payment.feature.qris.ui.confirmation

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.QrisConfirmationRoute
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.ImageAsync
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.TextFieldCustom
import id.co.bri.brimo.payment.core.design.component.WarningBottomSheet
import id.co.bri.brimo.payment.core.design.component.textFieldColors
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.errorSnackbar
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse
import id.co.bri.brimo.payment.core.network.response.qris.InputDataFrom
import id.co.bri.brimo.payment.core.network.response.qris.QrisScanResponse
import id.co.bri.brimo.payment.dependency.PaymentDependency
import id.co.bri.brimo.payment.feature.brizzi.ui.pin.PinDialog
import id.co.bri.brimo.payment.feature.brizzi.ui.process.ProcessDialog
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import id.co.bri.brimo.payment.feature.qris.ui.base.QrisAccountBottomSheet
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun QrisConfirmationScreen(
    navigation: (QrisConfirmationNavigation) -> Unit = {},
    qrisConfirmationViewModel: QrisConfirmationViewModel = koinViewModel()
) {
    val accountList by qrisConfirmationViewModel.accountList.collectAsStateWithLifecycle()
    val qrisPayment by qrisConfirmationViewModel.qrisPayment.collectAsStateWithLifecycle()

    if (qrisConfirmationViewModel.qrisModel != null) {
        QrisConfirmationContent(
            state = QrisConfirmationState(
                qrisConfirmationRoute = qrisConfirmationViewModel.qrisConfirmationRoute,
                qrisModel = qrisConfirmationViewModel.qrisModel,
                accountList = accountList.orEmpty(),
                qrisPayment = qrisPayment
            ),
            event = qrisConfirmationViewModel::handleEvent,
            navigation = navigation
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun QrisConfirmationContent(
    state: QrisConfirmationState,
    event: (QrisConfirmationEvent) -> Unit = {},
    navigation: (QrisConfirmationNavigation) -> Unit = {}
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current

    // Global
    var noteField by rememberSaveable { mutableStateOf("") }
    val counterNote by remember {
        derivedStateOf {
            "${noteField.length}/30"
        }
    }
    var selectedPin by rememberSaveable { mutableStateOf("") }
    var selectedAccount: AccountResponse? by remember {
        mutableStateOf(state.accountList.find { it.default == 1 }
            ?: state.accountList.firstOrNull())
    }
    val amount =
        if (state.qrisModel.qrisScan != null && state.qrisModel.typeQr == "qris_transfer") {
            state.qrisModel.qrisScan.payAmount
        } else if (state.qrisModel.typeQr == "qris_cb") {
            state.qrisModel.qrisConfirmation?.amount
        } else {
            state.qrisModel.qrisConfirmation?.payAmount
        }
    val notEnough = (selectedAccount?.balance?.toDoubleOrNull() ?: 0.0) <
        (amount?.toDoubleOrNull() ?: 0.0) &&
        !state.qrisConfirmationRoute.fastMenu &&
        state.qrisModel.qrisScan != null

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Process
    var processDialog by rememberSaveable { mutableStateOf(false) }
    if (processDialog) {
        Dialog(
            onDismissRequest = { processDialog = false },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                ProcessDialog()
            }
        }
    }

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            event(
                QrisConfirmationEvent.Payment(
                    pin = selectedPin,
                    accountNumber = selectedAccount?.account.orEmpty(),
                    note = noteField
                )
            )
        }
    )

    // Warning
    var warningBottomSheet by rememberSaveable { mutableStateOf(false) }

    BackHandler {
        warningBottomSheet = true
    }

    WarningBottomSheet(
        showBottomSheet = warningBottomSheet,
        onShowBottomSheet = { warningBottomSheet = it },
        title = "Yakin ingin kembali?",
        description = "Kamu akan keluar dari halaman konfirmasi. Transaksi belum diproses.",
        primaryText = "Lanjutkan Transaksi",
        secondaryText = "Kembali",
        onSecondary = {
            PaymentDependency.getFinish()
        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Pin
    var pinDialog by rememberSaveable { mutableStateOf(false) }
    var pinMessage by rememberSaveable { mutableStateOf("") }
    var deletePin by remember { mutableStateOf({}) }

    if (pinDialog) {
        Dialog(
            onDismissRequest = { pinDialog = false },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                PinDialog(
                    error = pinMessage,
                    onBack = {
                        pinDialog = false
                    },
                    onPin = { pin ->
                        selectedPin = pin
                        event(
                            QrisConfirmationEvent.Payment(
                                pin = selectedPin,
                                accountNumber = selectedAccount?.account.orEmpty(),
                                note = noteField
                            )
                        )
                    },
                    deletePin = { action ->
                        deletePin = action
                    }
                )
            }
        }
    }

    // Account
    var accountBottomSheet by rememberSaveable { mutableStateOf(false) }

    if (state.accountList.isNotEmpty()) {
        BottomSheet(
            showBottomSheet = accountBottomSheet,
            onShowBottomSheet = { accountBottomSheet = it }
        ) { dismiss ->
            QrisAccountBottomSheet(
                selectedAccount = selectedAccount?.account.orEmpty(),
                nominal = amount.orEmpty(),
                data = state.accountList,
                onSelect = { item ->
                    dismiss {
                        selectedAccount = item
                    }
                },
                onRefresh = { item ->
                    event(QrisConfirmationEvent.RefreshSaldo(item.account.orEmpty()))
                },
                onClose = {
                    dismiss {}
                },
                fastMenu = state.qrisConfirmationRoute.fastMenu
            )
        }
    }

    // Qris Payment
    LaunchedEffect(state.qrisPayment) {
        state.qrisPayment
            .onLoading {
                progressDialog = true
            }
            .onSuccess { data ->
                progressDialog = false
                var updatedData = data
                updatedData = if (updatedData.sourceAccountDataView == null) {
                    updatedData.copy(sourceAccountDataView = state.qrisModel.qrisConfirmation?.sourceAccountDataView)
                } else {
                    updatedData
                }
                updatedData = if (updatedData.billingDetail == null) {
                    updatedData.copy(billingDetail = state.qrisModel.qrisConfirmation?.billingDetail)
                } else {
                    updatedData
                }
                val qrisData = updatedData.serialize().orEmpty()
                if (qrisData.isEmpty()) {
                    errorBottomSheet = true
                } else {
                    processDialog = true
                    delay(2500)
                    navigation(
                        QrisConfirmationNavigation.Payment(
                            qrisData = qrisData,
                            type = state.qrisModel.typeQr.orEmpty()
                        )
                    )
                }
                event(QrisConfirmationEvent.ResetPayment)
            }
            .onError { e ->
                progressDialog = false
                deletePin()
                if (e is MessageException && e.errorSnackbar()) {
                    if (e.description.contains("pin", true)) {
                        pinMessage = e.description
                    } else {
                        pinDialog = false
                        snackbarType = SnackbarType.ERROR
                        snackbarHostState.showSnackbar(e.description)
                    }
                } else {
                    error = e
                    errorBottomSheet = true
                }
                event(QrisConfirmationEvent.ResetPayment)
            }
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .pointerInput(Unit) {
                detectTapGestures {
                    focusManager.clearFocus()
                }
            },
        bottomBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                if (state.qrisModel.qrisScan != null) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 16.dp, top = 16.dp, end = 16.dp)
                            .clickable {
                                accountBottomSheet = true
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        AsyncImage(
                            model = selectedAccount?.imagePath.orEmpty(),
                            contentDescription = null,
                            modifier = Modifier
                                .width(58.dp)
                                .height(36.dp),
                            placeholder = painterResource(id = R.drawable.thumbnail),
                            error = painterResource(id = R.drawable.thumbnail),
                            contentScale = ContentScale.Inside
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        if (state.qrisConfirmationRoute.fastMenu) {
                            Text(
                                text = selectedAccount?.accountString.orEmpty(),
                                modifier = Modifier.weight(1f),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        } else {
                            var hide by rememberSaveable { mutableStateOf(false) }
                            val icon = if (hide) {
                                R.drawable.icon_hide_eye
                            } else {
                                R.drawable.icon_unhide_eye
                            }
                            val accountString = selectedAccount?.accountString.orEmpty()
                            val account = if (hide) {
                                "${accountString.take(4)} **** ****${accountString.takeLast(4)}"
                            } else {
                                accountString
                            }
                            val balance = if (hide) {
                                "••••••"
                            } else {
                                selectedAccount?.balanceString.orEmpty()
                            }
                            val balanceError = selectedAccount?.balance == null
                            val color = if (balanceError || notEnough) Color_E84040 else Color.Black

                            Column(modifier = Modifier.weight(1f)) {
                                Row(
                                    modifier = Modifier.clickable {
                                        hide = !hide
                                    },
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = account,
                                        style = MaterialTheme.typography.labelSmall
                                    )

                                    if (!balanceError) {
                                        Spacer(modifier = Modifier.width(4.dp))

                                        Image(
                                            painter = painterResource(icon),
                                            contentDescription = null,
                                            modifier = Modifier.size(16.dp),
                                            contentScale = ContentScale.Fit
                                        )
                                    }
                                }

                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    if (!balanceError) {
                                        Text(
                                            text = selectedAccount?.currency.orEmpty() + balance,
                                            color = color,
                                            fontWeight = FontWeight.SemiBold,
                                            style = MaterialTheme.typography.bodyMedium
                                        )

                                        Spacer(modifier = Modifier.width(8.dp))
                                    } else {
                                        Text(
                                            text = "Gagal memuat saldo",
                                            color = Color_E84040,
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                    }
                                }
                            }
                        }

                        Spacer(modifier = Modifier.width(12.dp))

                        Icon(
                            imageVector = Icons.Default.KeyboardArrowDown,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                Button(
                    onClick = {
                        pinDialog = true
                    },
                    modifier = Modifier
                        .padding(16.dp)
                        .fillMaxWidth()
                        .height(56.dp),
                    enabled = !notEnough,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color_0054F3,
                        contentColor = Color.White
                    )
                ) {
                    Text(
                        text = "Bayar Sekarang",
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    val amount =
                        if (state.qrisModel.qrisScan != null && state.qrisModel.typeQr == "qris_transfer") {
                            state.qrisModel.qrisScan.payAmountString
                        } else if (state.qrisModel.typeQr == "qris_cb") {
                            state.qrisModel.qrisConfirmation?.totalDataView?.firstOrNull()?.value
                        } else {
                            state.qrisModel.qrisConfirmation?.payAmountString
                        }

                    Text(
                        text = amount.orEmpty(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                warningBottomSheet = true
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "Konfirmasi",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))

                val title = when (state.qrisModel.typeQr) {
                    "qris_transfer" -> "Total QRIS Transfer"
                    else -> "Total QRIS Pembayaran"
                }

                Text(
                    text = title,
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyLarge
                )

                val amount =
                    if (state.qrisModel.qrisScan != null && state.qrisModel.typeQr == "qris_transfer") {
                        state.qrisModel.qrisScan.payAmountString
                    } else if (state.qrisModel.typeQr == "qris_cb") {
                        state.qrisModel.qrisConfirmation?.totalDataView?.firstOrNull()?.value
                    } else {
                        state.qrisModel.qrisConfirmation?.payAmountString
                    }

                Text(
                    text = amount.orEmpty(),
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.headlineLarge
                )

                if (state.qrisModel.typeQr == "qris_cb") {
                    val convertAmount =
                        state.qrisModel.qrisConfirmation?.amountDataView?.firstOrNull()?.value

                    Text(
                        text = "Senilai dengan ${convertAmount.orEmpty()}",
                        modifier = Modifier.fillMaxWidth(),
                        color = Color.White,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Penerima",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val image =
                            if (state.qrisModel.qrisScan != null && state.qrisModel.typeQr == "qris_transfer") {
                                state.qrisModel.qrisScan.billingDetailOpen?.firstOrNull()?.iconPath.orEmpty()
                            } else {
                                state.qrisModel.qrisConfirmation?.billingDetail?.iconPath.orEmpty()
                            }
                        val title =
                            if (state.qrisModel.qrisScan != null && state.qrisModel.typeQr == "qris_transfer") {
                                state.qrisModel.qrisScan.billingDetailOpen?.firstOrNull()?.title.orEmpty()
                            } else {
                                state.qrisModel.qrisConfirmation?.billingDetail?.title.orEmpty()
                            }

                        ImageAsync(
                            context = context,
                            url = image,
                            initial = title,
                            size = 32
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Column(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = title,
                                modifier = Modifier.fillMaxWidth(),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(2.dp))

                            val subtitle =
                                if (state.qrisModel.qrisScan != null && state.qrisModel.typeQr == "qris_transfer") {
                                    state.qrisModel.qrisScan.billingDetailOpen?.firstOrNull()?.subtitle.orEmpty()
                                } else {
                                    state.qrisModel.qrisConfirmation?.billingDetail?.subtitle.orEmpty()
                                }
                            val description =
                                if (state.qrisModel.qrisScan != null && state.qrisModel.typeQr == "qris_transfer") {
                                    state.qrisModel.qrisScan.billingDetailOpen?.firstOrNull()?.description.orEmpty()
                                } else {
                                    state.qrisModel.qrisConfirmation?.billingDetail?.description.orEmpty()
                                }
                            val source =
                                subtitle + if (description.isNotEmpty()) " - $description" else ""

                            Text(
                                text = source,
                                modifier = Modifier.fillMaxWidth(),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Detail Tagihan",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                            .padding(horizontal = 16.dp, vertical = 12.dp)
                    ) {
                        if (state.qrisModel.qrisScan != null && state.qrisModel.typeQr == "qris_transfer") {
                            state.qrisModel.qrisScan.billingAmountDetail?.forEachIndexed { index, item ->
                                ItemBilling(
                                    name = item.name.orEmpty(),
                                    value = item.value.orEmpty(),
                                    notLast = index < state.qrisModel.qrisScan.billingAmountDetail.size - 1
                                )
                            }
                        } else {
                            state.qrisModel.qrisConfirmation?.amountDataView?.forEachIndexed { index, item ->
                                ItemBilling(
                                    name = item.name.orEmpty(),
                                    value = item.value.orEmpty(),
                                    notLast = index < state.qrisModel.qrisConfirmation.amountDataView.size - 1
                                )
                            }
                        }
                    }

                    if (state.qrisModel.qrisScan == null) {
                        Spacer(modifier = Modifier.height(24.dp))

                        Text(
                            text = "Sumber Dana",
                            modifier = Modifier.fillMaxWidth(),
                            fontWeight = FontWeight.SemiBold,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                                .padding(16.dp)
                        ) {
                            AsyncImage(
                                model = state.qrisModel.accountImage,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(58.dp)
                                    .height(36.dp),
                                placeholder = painterResource(id = R.drawable.thumbnail),
                                error = painterResource(id = R.drawable.thumbnail),
                                contentScale = ContentScale.Inside
                            )

                            Spacer(modifier = Modifier.width(16.dp))

                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = state.qrisModel.qrisConfirmation?.sourceAccountDataView?.title.orEmpty(),
                                    modifier = Modifier.fillMaxWidth(),
                                    style = MaterialTheme.typography.bodySmall
                                )

                                val description =
                                    state.qrisModel.qrisConfirmation?.sourceAccountDataView?.description
                                        ?: state.qrisModel.qrisConfirmation?.sourceAccountDataView?.subtitle
                                Text(
                                    text = description.orEmpty(),
                                    modifier = Modifier.fillMaxWidth(),
                                    color = Color_7B90A6,
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                        }
                    }

                    if (state.qrisModel.qrisScan != null) {
                        Spacer(modifier = Modifier.height(24.dp))

                        var isFocusedNote by rememberSaveable { mutableStateOf(false) }
                        val borderColorNote = if (isFocusedNote) {
                            Color_0054F3
                        } else {
                            Color.Transparent
                        }

                        TextFieldCustom(
                            value = noteField,
                            onValueChange = {
                                if (it.length <= 30) {
                                    noteField = it
                                }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp)
                                .onFocusChanged { state ->
                                    isFocusedNote = state.isFocused
                                }
                                .border(1.dp, borderColorNote, RoundedCornerShape(16.dp)),
                            textStyle = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            label = {
                                Text(text = "Catatan (Opsional)")
                            },
                            trailingIcon = if (noteField.isNotEmpty()) {
                                {
                                    Image(
                                        painter = painterResource(R.drawable.icon_close),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .size(16.dp)
                                            .clickable {
                                                noteField = ""
                                            },
                                        contentScale = ContentScale.Fit
                                    )
                                }
                            } else {
                                null
                            },
                            singleLine = true,
                            shape = RoundedCornerShape(16.dp),
                            colors = textFieldColors(),
                            contentPadding = TextFieldDefaults.contentPaddingWithLabel(top = 12.dp)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = counterNote,
                            modifier = Modifier.align(Alignment.End),
                            color = Color_7B90A6,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.systemBars))
                }
            }
        }
    }
}

@Composable
private fun ItemBilling(
    name: String,
    value: String,
    notLast: Boolean
) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = name,
            modifier = Modifier.weight(1f),
            color = Color_7B90A6,
            style = MaterialTheme.typography.bodyMedium
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = value,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.End,
            style = MaterialTheme.typography.bodyMedium
        )
    }

    if (notLast) {
        Spacer(modifier = Modifier.height(12.dp))
    }
}

@Preview
@Composable
fun PreviewQrisConfirmation() {
    MainTheme {
        QrisConfirmationContent(
            state = QrisConfirmationState(
                qrisConfirmationRoute = QrisConfirmationRoute(
                    data = "",
                    fastMenu = false
                ),
                qrisModel = QrisModel(
                    typeQr = "qris_transfer",
                    qrisScan = QrisScanResponse(
                        accountList = listOf(),
                        billingDetail = listOf(),
                        billingDetailOpen = listOf(
                            BillingResponse(
                                listType = "",
                                iconName = "",
                                iconPath = "",
                                title = "Title",
                                subtitle = "Subtitle",
                                description = "Description"
                            )
                        ),
                        billingAmount = listOf(
                            DataViewResponse(
                                name = "Total Tagihan",
                                value = "Rp11.000",
                                style = ""
                            )
                        ),
                        billingAmountDetail = listOf(
                            DataViewResponse(
                                name = "Nominal",
                                value = "Rp10.000",
                                style = ""
                            ),
                            DataViewResponse(
                                name = "Biaya Admin",
                                value = "Rp1.000",
                                style = ""
                            )
                        ),
                        inputDataFrom = InputDataFrom(
                            countryName = "",
                            countryCode = "",
                            currencyCode = "",
                            iconName = "",
                            iconPath = "",
                            inputName = "",
                            inputValue = 0
                        ),
                        information = DataViewResponse(
                            name = "",
                            value = "",
                            style = ""
                        ),
                        openPayment = true,
                        isBilling = false,
                        minimumPayment = false,
                        amountEditable = true,
                        rowDataShow = 0,
                        saved = "",
                        amount = "10000",
                        amountString = "Rp10.000",
                        minimumAmount = "",
                        minimumAmountString = "",
                        adminFee = "1000",
                        adminFeeString = "Rp1.000",
                        payAmount = "11000",
                        payAmountString = "Rp11.000",
                        minimumTransaction = "",
                        minimumTransactionString = "",
                        tipOption = listOf(),
                        tipEditable = false,
                        tipType = "",
                        tipAmount = "",
                        tipAmountString = "",
                        typeQr = "qris_transfer",
                        merchantName = "",
                        merchantCountry = "",
                        nomorTiket = "",
                        referenceNumber = ""
                    ),
                    qrisConfirmation = null,
                    /*QrisConfirmationResponse(
                        sourceAccountDataView = BillingResponse(
                            listType = "",
                            iconName = "",
                            iconPath = "",
                            title = "Title",
                            subtitle = "Subtitle",
                            description = "Description"
                        ),
                        billingDetail = BillingResponse(
                            listType = "",
                            iconName = "",
                            iconPath = "",
                            title = "Title",
                            subtitle = "Subtitle",
                            description = "Description"
                        ),
                        amountDataView = listOf(
                            DataViewResponse(
                                name = "Nominal",
                                value = "Rp10.000",
                                style = ""
                            ),
                            DataViewResponse(
                                name = "Biaya Admin",
                                value = "Rp1.000",
                                style = ""
                            )
                        ),
                        totalDataView = listOf(
                            DataViewResponse(
                                name = "Total Transaksi",
                                value = "Rp11.000",
                                style = ""
                            )
                        ),
                        saveAs = "",
                        amount = "10000",
                        amountString = "Rp10.000",
                        adminFee = "1000",
                        adminFeeString = "Rp1.000",
                        payAmount = "11000",
                        payAmountString = "Rp11.000",
                        pfmCategory = 0,
                        pfmDescription = "",
                        note = "",
                        referenceNumber = "",
                        rateCurrency = "",
                        nominalCurrency = ""
                    )*/
                ),
                accountList = listOf(
                    AccountResponse(
                        account = "***************",
                        accountString = "1234 5678 9012 345",
                        name = "Name",
                        currency = "Rp",
                        cardNumber = "****************",
                        cardNumberString = "0987 XXXX XXXX 8765",
                        productType = "",
                        accountType = "",
                        scCode = "",
                        default = 0,
                        alias = "Alias",
                        minimumBalance = "",
                        limit = "",
                        limitString = "",
                        imageName = "",
                        imagePath = "",
                        onHold = false,
                        balance = "9000000",
                        balanceString = "9.000.000,00"
                    )
                )
            )
        )
    }
}
