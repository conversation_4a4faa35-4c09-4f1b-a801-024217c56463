package id.co.bri.brimo.payment.feature.qris.ui.scan

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import id.co.bri.brimo.payment.app.QrisScanRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qris.QrisConfirmationRequest
import id.co.bri.brimo.payment.core.network.request.qris.QrisScanRequest
import id.co.bri.brimo.payment.core.network.request.qrtap.QrTapPayloadRequest
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapPayloadResponse
import id.co.bri.brimo.payment.feature.qris.data.api.QrisRepository
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import id.co.bri.brimo.payment.feature.qrtap.data.api.QrTapRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrisScanViewModel(
    private val qrisRepository: QrisRepository,
    private val qrTapRepository: QrTapRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrisScanRoute = savedStateHandle.toRoute<QrisScanRoute>()

    fun handleEvent(event: QrisScanEvent) {
        when (event) {
            is QrisScanEvent.Scan -> {
                postScanQris(stringQr = event.stringQr)
            }

            QrisScanEvent.ResetScan -> {
                resetScan()
            }

            is QrisScanEvent.Payload -> {
                postQrTapPayload(pin = event.pin)
            }

            QrisScanEvent.ResetPayload -> {
                resetPayload()
            }
        }
    }

    private val _qrScan = MutableStateFlow<UiState<QrisModel>>(UiState.Init)
    val qrScan = _qrScan.asStateFlow()

    private fun resetScan() {
        viewModelScope.launch {
            _qrScan.update { UiState.Init }
        }
    }

    private fun postScanQris(stringQr: String) {
        viewModelScope.launch {
            _qrScan.asUiState {
                val scanQris = qrisRepository.postScanQris(
                    request = QrisScanRequest(stringQr = stringQr),
                    fastMenu = qrisScanRoute.fastMenu
                )

                val accountList = try {
                    qrisRepository.postAccountList(fastMenu = qrisScanRoute.fastMenu).account
                } catch (_: Throwable) {
                    scanQris.accountList
                }

                val accountListUpdated = if (!qrisScanRoute.fastMenu) {
                    accountList?.map { account ->
                        async {
                            try {
                                val saldoNormal = qrisRepository.postSaldoNormal(
                                    request = SaldoNormalRequest(
                                        account = account.account.orEmpty()
                                    )
                                )
                                account.copy(
                                    onHold = saldoNormal.onHold,
                                    balance = saldoNormal.balance,
                                    balanceString = saldoNormal.balanceString
                                )
                            } catch (_: Throwable) {
                                account
                            }
                        }
                    }?.awaitAll()
                } else {
                    accountList
                }

                val scanQrisUpdated = scanQris.copy(accountList = accountListUpdated)

                when (scanQrisUpdated.typeQr) {
                    "qris_mpm", "qris_cb" -> {
                        if (scanQrisUpdated.amountEditable == true) {
                            QrisModel(
                                openPayment = true,
                                typeQr = scanQrisUpdated.typeQr,
                                qrisScan = scanQrisUpdated
                            )
                        } else {
                            val mainAccount = scanQrisUpdated.accountList?.find { it.default == 1 }
                                ?: scanQrisUpdated.accountList?.firstOrNull()

                            val qrisConfirmation =
                                qrisRepository.postQrisConfirmation(
                                    type = scanQrisUpdated.typeQr,
                                    request = QrisConfirmationRequest(
                                        accountNumber = mainAccount?.account.orEmpty(),
                                        amount = scanQrisUpdated.payAmount,
                                        note = "",
                                        referenceNumber = scanQrisUpdated.referenceNumber,
                                        saveAs = "",
                                        inputTipAmount = null
                                    ),
                                    fastMenu = qrisScanRoute.fastMenu
                                )

                            QrisModel(
                                openPayment = false,
                                typeQr = scanQrisUpdated.typeQr,
                                qrisScan = scanQrisUpdated,
                                qrisConfirmation = qrisConfirmation
                            )
                        }
                    }

                    else -> {
                        QrisModel(
                            openPayment = scanQrisUpdated.openPayment,
                            typeQr = scanQrisUpdated.typeQr,
                            qrisScan = scanQrisUpdated
                        )
                    }
                }
            }
        }
    }

    private val _qrTapPayload = MutableStateFlow<UiState<QrTapPayloadResponse>>(UiState.Init)
    val qrTapPayload = _qrTapPayload.asStateFlow()

    private fun resetPayload() {
        viewModelScope.launch {
            _qrTapPayload.update { UiState.Init }
        }
    }

    private fun postQrTapPayload(pin: String) {
        viewModelScope.launch {
            _qrTapPayload.asUiState {
                val payload = qrTapRepository.postQrTapPayload(
                    request = QrTapPayloadRequest(
                        pin = pin,
                        account = "",
                        cardToken = "",
                        nfcType = ""
                    ),
                    fastMenu = qrisScanRoute.fastMenu
                )

                val accountList = try {
                    qrTapRepository.postAccountList(fastMenu = qrisScanRoute.fastMenu).account
                } catch (_: Throwable) {
                    payload.accountList
                }

                val accountListUpdated = if (!qrisScanRoute.fastMenu) {
                    accountList?.map { account ->
                        async {
                            try {
                                val saldoNormal = qrTapRepository.postSaldoNormal(
                                    request = SaldoNormalRequest(
                                        account = account.account.orEmpty()
                                    )
                                )
                                account.copy(
                                    onHold = saldoNormal.onHold,
                                    balance = saldoNormal.balance,
                                    balanceString = saldoNormal.balanceString
                                )
                            } catch (_: Throwable) {
                                account
                            }
                        }
                    }?.awaitAll()
                } else {
                    accountList
                }

                val payloadUpdated = payload.copy(accountList = accountListUpdated)

                payloadUpdated
            }
        }
    }
}
