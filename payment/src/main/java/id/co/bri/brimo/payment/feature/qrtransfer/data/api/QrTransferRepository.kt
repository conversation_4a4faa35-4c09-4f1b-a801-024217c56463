package id.co.bri.brimo.payment.feature.qrtransfer.data.api

import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrtransfer.QrTransferGenerateRequest
import id.co.bri.brimo.payment.core.network.response.base.AccountListResponse
import id.co.bri.brimo.payment.core.network.response.base.SaldoNormalResponse
import id.co.bri.brimo.payment.core.network.response.qrtransfer.QrTransferGenerateResponse

internal interface QrTransferRepository {

    suspend fun postAccountList(fastMenu: Boolean): AccountListResponse

    suspend fun postSaldoNormal(
        request: SaldoNormalRequest
    ): SaldoNormalResponse

    suspend fun postQrTransferGenerate(
        request: QrTransferGenerateRequest,
        fastMenu: Boolean
    ): QrTransferGenerateResponse
}
