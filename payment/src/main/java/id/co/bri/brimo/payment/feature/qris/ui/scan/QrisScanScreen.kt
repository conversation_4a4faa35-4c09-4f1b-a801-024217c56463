package id.co.bri.brimo.payment.feature.qris.ui.scan

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.nfc.NfcAdapter
import android.nfc.NfcManager
import android.nfc.cardemulation.CardEmulation
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.QrisScanRoute
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.WarningBottomSheet
import id.co.bri.brimo.payment.core.design.theme.Color_E9EEF6
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.errorSnackbar
import id.co.bri.brimo.payment.feature.brizzi.ui.pin.PinDialog
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@Composable
internal fun QrisScanScreen(
    navigation: (QrisScanNavigation) -> Unit = {},
    qrisScanViewModel: QrisScanViewModel = koinViewModel()
) {
    // NFC
    val context = LocalContext.current
    val nfcManager = context.applicationContext.getSystemService(Context.NFC_SERVICE) as NfcManager
    var nfcAdapter = nfcManager.defaultAdapter
    if (nfcAdapter == null) {
        nfcAdapter = NfcAdapter.getDefaultAdapter(context)
    }
    val cardEmulation = nfcAdapter?.let {
        CardEmulation.getInstance(nfcAdapter)
    }
    val isNfcAvailable = nfcAdapter != null
    val isNfcEnabled = nfcAdapter != null && nfcAdapter.isEnabled
    val isDefaultService = cardEmulation?.isDefaultServiceForCategory(
        ComponentName("id.co.bri.brimo", "id.co.bri.brimo.nfcpayment.NFCPaymentService"),
        CardEmulation.CATEGORY_PAYMENT
    ) == true

    // State
    val qrScan by qrisScanViewModel.qrScan.collectAsStateWithLifecycle()
    val qrTapPayload by qrisScanViewModel.qrTapPayload.collectAsStateWithLifecycle()

    QrisScanContent(
        isNfcAvailable = isNfcAvailable,
        isNfcEnabled = isNfcEnabled,
        isDefaultService = isDefaultService,
        state = QrisScanState(
            qrisScanRoute = qrisScanViewModel.qrisScanRoute,
            qrScan = qrScan,
            qrTapPayload = qrTapPayload
        ),
        event = qrisScanViewModel::handleEvent,
        navigation = navigation
    )
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun QrisScanContent(
    isNfcAvailable: Boolean,
    isNfcEnabled: Boolean,
    isDefaultService: Boolean,
    state: QrisScanState,
    event: (QrisScanEvent) -> Unit = {},
    navigation: (QrisScanNavigation) -> Unit = {}
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // Global
    var qrCode by rememberSaveable { mutableStateOf("") }
    var onQrCode by remember { mutableStateOf({}) }
    var flash by rememberSaveable { mutableStateOf(false) }
    var onFlash: (Boolean) -> Unit by remember { mutableStateOf({}) }
    val qrisScanComposition by rememberLottieComposition(
        spec = LottieCompositionSpec.RawRes(R.raw.qris_scan_animation)
    )
    var selectedPin by rememberSaveable { mutableStateOf("") }

    // Permission
    val cameraPermissionState = rememberPermissionState(
        Manifest.permission.CAMERA
    )

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onDismiss = { onQrCode() },
        onClose = { onQrCode() },
        onRetry = {
            event(QrisScanEvent.Scan(stringQr = qrCode))
        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Image
    val imagePicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { selectedUri ->
            try {
                val inputImage = InputImage.fromFilePath(context, selectedUri)

                val options = BarcodeScannerOptions.Builder()
                    .setBarcodeFormats(Barcode.FORMAT_QR_CODE)
                    .build()
                val scanner = BarcodeScanning.getClient(options)

                scanner.process(inputImage)
                    .addOnSuccessListener { barcodes ->
                        val barcode = barcodes.firstOrNull()?.rawValue
                        if (barcode != null) {
                            qrCode = barcode
                            event(QrisScanEvent.Scan(stringQr = qrCode))
                        } else {
                            scope.launch {
                                snackbarType = SnackbarType.ERROR
                                snackbarHostState.showSnackbar("Gagal membaca QR. Silakan coba lagi.")
                            }
                        }
                    }
                    .addOnFailureListener {
                        scope.launch {
                            snackbarType = SnackbarType.ERROR
                            snackbarHostState.showSnackbar("Gagal membaca QR. Silakan coba lagi.")
                        }
                    }
            } catch (_: Throwable) {
                scope.launch {
                    snackbarType = SnackbarType.ERROR
                    snackbarHostState.showSnackbar("Gagal membaca QR. Silakan coba lagi.")
                }
            }
        }
    }

    // Camera Bottom Sheet
    var cameraBottomSheet by rememberSaveable { mutableStateOf(false) }
    var cameraAction by remember { mutableStateOf({}) }

    WarningBottomSheet(
        showBottomSheet = cameraBottomSheet,
        onShowBottomSheet = { cameraBottomSheet = it },
        title = "Aktifkan Akses Kamera",
        description = "Untuk scan QR, kamu perlu izinkan akses kamera terlebih dahulu.",
        primaryText = "Izinkan Akses",
        onPrimary = {
            cameraAction()
        },
        onClose = {
            navigation(QrisScanNavigation.Back)
        },
        onDismiss = {
            navigation(QrisScanNavigation.Back)
        }
    )

    // NFC Bottom Sheet
    var nfcBottomSheet by rememberSaveable { mutableStateOf(false) }

    WarningBottomSheet(
        showBottomSheet = nfcBottomSheet,
        onShowBottomSheet = { nfcBottomSheet = it },
        title = "Aktifkan NFC di HP Kamu, Yuk!",
        description = "Fitur di NFC harus aktif untuk bisa menikmati NFC Payment.",
        primaryText = "Aktifkan di Pengaturan",
        onPrimary = {
            val intent = Intent(Settings.ACTION_NFC_SETTINGS)
            context.startActivity(intent)
        }
    )

    // Pin
    var pinDialog by rememberSaveable { mutableStateOf(false) }
    var pinMessage by rememberSaveable { mutableStateOf("") }
    var deletePin by remember { mutableStateOf({}) }

    if (pinDialog) {
        Dialog(
            onDismissRequest = { pinDialog = false },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                PinDialog(
                    error = pinMessage,
                    onBack = {
                        pinDialog = false
                    },
                    onPin = { pin ->
                        selectedPin = pin
                        event(QrisScanEvent.Payload(pin = selectedPin))
                    },
                    deletePin = { action ->
                        deletePin = action
                    }
                )
            }
        }
    }

    // Qr Scan
    LaunchedEffect(state.qrScan) {
        state.qrScan
            .onLoading {
                progressDialog = true
            }
            .onSuccess { data ->
                progressDialog = false
                onQrCode()
                val qrisData = data.serialize().orEmpty()
                if (qrisData.isEmpty()) {
                    errorBottomSheet = true
                } else {
                    if (data.typeQr == "parking_spi") {
                        navigation(
                            QrisScanNavigation.Park(
                                qrisData = qrisData,
                                fastMenu = state.qrisScanRoute.fastMenu
                            )
                        )
                    } else if (data.openPayment == true) {
                        navigation(
                            QrisScanNavigation.Nominal(
                                qrisData = qrisData,
                                type = data.typeQr.orEmpty(),
                                fastMenu = state.qrisScanRoute.fastMenu
                            )
                        )
                    } else {
                        navigation(
                            QrisScanNavigation.Confirmation(
                                qrisData = qrisData,
                                fastMenu = state.qrisScanRoute.fastMenu
                            )
                        )
                    }
                }
                event(QrisScanEvent.ResetScan)
            }
            .onError { e ->
                progressDialog = false
                if (e is MessageException && e.errorSnackbar()) {
                    onQrCode()
                    snackbarType = SnackbarType.ERROR
                    snackbarHostState.showSnackbar(e.description)
                } else {
                    error = e
                    errorBottomSheet = true
                }
                event(QrisScanEvent.ResetScan)
            }
    }

    // Qr Tap Payload
    LaunchedEffect(state.qrTapPayload) {
        state.qrTapPayload
            .onLoading {
                progressDialog = true
            }
            .onSuccess { data ->
                progressDialog = false
                val qrisData = data.serialize().orEmpty()
                if (qrisData.isEmpty()) {
                    errorBottomSheet = true
                } else {
                    navigation(QrisScanNavigation.QrTap(qrisData = qrisData))
                }
                event(QrisScanEvent.ResetPayload)
            }
            .onError { e ->
                progressDialog = false
                deletePin()
                if (e is MessageException && e.errorSnackbar()) {
                    if (e.description.contains("pin", true)) {
                        pinMessage = e.description
                    } else {
                        pinDialog = false
                        snackbarType = SnackbarType.ERROR
                        snackbarHostState.showSnackbar(e.description)
                    }
                } else {
                    error = e
                    errorBottomSheet = true
                }
                event(QrisScanEvent.ResetPayload)
            }
    }

    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
        ) {
            // Add ! to show the preview
            if (cameraPermissionState.status.isGranted) {
                if (cameraBottomSheet) cameraBottomSheet = false

                CameraScreen(
                    onFlash = { action ->
                        onFlash = action
                    },
                    onQrScanned = { code ->
                        qrCode = code
                        event(QrisScanEvent.Scan(stringQr = qrCode))
                    },
                    onQrCode = { action ->
                        onQrCode = action
                    },
                    onQrError = {
                        scope.launch {
                            snackbarType = SnackbarType.ERROR
                            snackbarHostState.showSnackbar("Gagal membaca QR. Silakan coba lagi.")
                        }
                    },
                    onCameraError = {
                        scope.launch {
                            snackbarType = SnackbarType.ERROR
                            snackbarHostState.showSnackbar("Gagal mengaktifkan kamera. Silakan coba lagi.")
                        }
                    }
                )

                LottieAnimation(
                    composition = qrisScanComposition,
                    modifier = Modifier.fillMaxSize(),
                    iterations = LottieConstants.IterateForever,
                    contentScale = ContentScale.FillBounds
                )
            } else {
                if (cameraPermissionState.status.shouldShowRationale) {
                    cameraAction = {
                        val intent =
                            Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                                data = Uri.fromParts("package", context.packageName, null)
                                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                            }
                        context.startActivity(intent)
                    }
                    cameraBottomSheet = true
                } else {
                    cameraAction = {
                        cameraPermissionState.launchPermissionRequest()
                    }
                    cameraBottomSheet = true
                }
            }

            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(innerPadding)
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.drawable.icon_back),
                    contentDescription = null,
                    modifier = Modifier
                        .size(32.dp)
                        .clickable {
                            navigation(QrisScanNavigation.Back)
                        },
                    contentScale = ContentScale.Fit
                )

                Image(
                    painter = painterResource(R.drawable.icon_qris),
                    contentDescription = null,
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 32.dp)
                        .padding(horizontal = 16.dp),
                    contentScale = ContentScale.Fit
                )
            }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 24.dp)
                ) {
                    val iconFlash = if (flash) {
                        R.drawable.icon_flash_on
                    } else {
                        R.drawable.icon_flash_off
                    }

                    IconButton(
                        onClick = {
                            flash = !flash
                            onFlash(flash)
                        },
                        modifier = Modifier
                            .clip(CircleShape)
                            .background(Color.White.copy(alpha = 0.3f))
                    ) {
                        Icon(
                            painter = painterResource(iconFlash),
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                            tint = Color.White
                        )
                    }

                    Spacer(modifier = Modifier.weight(1f))

                    IconButton(
                        onClick = {
                            imagePicker.launch("image/*")
                        },
                        modifier = Modifier
                            .clip(CircleShape)
                            .background(Color.White.copy(alpha = 0.3f))
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.icon_gallery_add),
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                            tint = Color.White
                        )
                    }
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp, vertical = 24.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    NavigationItem(
                        icon = R.drawable.icon_transfer,
                        label = "Transfer",
                        onClick = { navigation(QrisScanNavigation.QrTransfer) },
                        modifier = Modifier.weight(1f)
                    )

                    VerticalDivider(
                        modifier = Modifier.height(64.dp),
                        color = Color_E9EEF6
                    )

                    NavigationItem(
                        icon = R.drawable.icon_payment,
                        label = "Pembayaran",
                        onClick = { navigation(QrisScanNavigation.QrShow) },
                        modifier = Modifier.weight(1f)
                    )

                    if (isNfcAvailable) {
                        VerticalDivider(
                            modifier = Modifier.height(64.dp),
                            color = Color_E9EEF6
                        )

                        NavigationItem(
                            icon = R.drawable.icon_qris_tap,
                            label = "QRIS Tap",
                            onClick = {
                                if (isNfcEnabled) {
                                    if (isDefaultService) {
                                        pinDialog = true
                                    } else {
                                        val intent = Intent(CardEmulation.ACTION_CHANGE_DEFAULT)
                                        intent.putExtra(
                                            CardEmulation.EXTRA_SERVICE_COMPONENT,
                                            ComponentName(
                                                "id.co.bri.brimo",
                                                "id.co.bri.brimo.nfcpayment.NFCPaymentService"
                                            )
                                        )
                                        intent.putExtra(
                                            CardEmulation.EXTRA_CATEGORY,
                                            CardEmulation.CATEGORY_PAYMENT
                                        )
                                        context.startActivity(intent)
                                    }
                                } else {
                                    nfcBottomSheet = true
                                }
                            },
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun NavigationItem(
    icon: Int,
    label: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.clickable(onClick = onClick),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            painter = painterResource(icon),
            contentDescription = label,
            modifier = Modifier.size(32.dp),
            tint = Color.Black
        )

        Spacer(modifier = Modifier.height(12.dp))

        Text(
            text = label,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@androidx.annotation.OptIn(ExperimentalGetImage::class)
@Composable
fun CameraScreen(
    onFlash: ((Boolean) -> Unit) -> Unit = {},
    onQrScanned: (String) -> Unit = {},
    onQrCode: (() -> Unit) -> Unit = {},
    onQrError: () -> Unit = {},
    onCameraError: () -> Unit = {}
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val context = LocalContext.current

    // Global
    var isQrScan by rememberSaveable { mutableStateOf(false) }
    onQrCode {
        isQrScan = false
    }

    // Camera
    val cameraProviderFuture = remember {
        ProcessCameraProvider.getInstance(context)
    }
    var camera: Camera? by remember { mutableStateOf(null) }
    onFlash {
        camera?.apply {
            cameraControl.enableTorch(it)
        }
    }

    // Barcode
    val options = remember {
        BarcodeScannerOptions.Builder()
            .setBarcodeFormats(Barcode.FORMAT_QR_CODE)
            .build()
    }
    val scanner = remember {
        BarcodeScanning.getClient(options)
    }

    // Camera
    AndroidView(
        modifier = Modifier.fillMaxSize(),
        factory = { ctx ->
            val previewView = PreviewView(ctx).apply {
                implementationMode = PreviewView.ImplementationMode.COMPATIBLE
            }

            cameraProviderFuture.addListener(
                {
                    val cameraProvider = cameraProviderFuture.get()
                    val selector = CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()
                    val preview = Preview.Builder().build().apply {
                        surfaceProvider = previewView.surfaceProvider
                    }
                    val imageAnalysis = ImageAnalysis.Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .build()
                        .apply {
                            setAnalyzer(ContextCompat.getMainExecutor(context)) { imageProxy ->
                                if (isQrScan) {
                                    imageProxy.close()
                                    return@setAnalyzer
                                }

                                val image = imageProxy.image
                                if (image != null) {
                                    val inputImage = InputImage.fromMediaImage(
                                        image, imageProxy.imageInfo.rotationDegrees
                                    )

                                    scanner.process(inputImage)
                                        .addOnSuccessListener { barcodes ->
                                            for (barcode in barcodes) {
                                                barcode.rawValue?.let {
                                                    isQrScan = true
                                                    onQrScanned(it)
                                                }
                                            }
                                        }
                                        .addOnFailureListener {
                                            onQrError()
                                        }
                                        .addOnCompleteListener {
                                            imageProxy.close()
                                        }
                                } else {
                                    imageProxy.close()
                                }
                            }
                        }

                    try {
                        cameraProvider.unbindAll()
                        camera = cameraProvider.bindToLifecycle(
                            lifecycleOwner,
                            selector,
                            preview,
                            imageAnalysis
                        )
                    } catch (_: Throwable) {
                        onCameraError()
                    }
                },
                ContextCompat.getMainExecutor(context)
            )

            previewView
        }
    )
}

@androidx.compose.ui.tooling.preview.Preview
@Composable
private fun PreviewQrisScanScreen() {
    MainTheme {
        QrisScanContent(
            isNfcAvailable = true,
            isNfcEnabled = false,
            isDefaultService = false,
            state = QrisScanState(
                qrisScanRoute = QrisScanRoute(
                    fastMenu = false
                )
            )
        )
    }
}
