package id.co.bri.brimo.payment.feature.briva.ui.base

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.EmptyState
import id.co.bri.brimo.payment.core.design.helper.highlightedText
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.response.base.HistoryResponse

@Composable
internal fun BrivaHistorySection(
    data: List<HistoryResponse>,
    onSelect: (HistoryResponse) -> Unit = {},
    isBriva: Boolean = false
) {
    if (data.isEmpty()) {
        Box(modifier = Modifier.fillMaxSize()) {
            EmptyState(
                modifier = Modifier.align(Alignment.Center),
                title = "Belum Ada Daftar Riwayat",
                description = "Semua transaksi yang telah dibuat akan tampil di sini."
            )
        }
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            itemsIndexed(data) { index, item ->
                ItemBrivaHistory(
                    item = item,
                    onSelect = onSelect,
                    isBriva = isBriva
                )

                Spacer(modifier = Modifier.height(16.dp))

                if (index < data.size - 1) {
                    DividerHorizontal()
                }
            }
        }
    }
}

@Composable
internal fun ItemBrivaHistory(
    highlight: String = "",
    item: HistoryResponse,
    onSelect: (HistoryResponse) -> Unit = {},
    isBriva: Boolean = false
) {
    val title = item.title.orEmpty()
    val highlightedTitle = highlightedText(highlight, title)
    val value = "${item.subtitle.orEmpty()} - ${item.description.orEmpty()}"
    val highlightedValue = highlightedText(highlight, value)

    val icon = if (isBriva) R.drawable.icon_briva else R.drawable.thumbnail

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                onSelect(item)
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(item.iconPath)
                .crossfade(true)
                .decoderFactory(SvgDecoder.Factory())
                .build(),
            contentDescription = null,
            modifier = Modifier
                .size(32.dp)
                .clip(CircleShape),
            placeholder = painterResource(id = icon),
            error = painterResource(id = icon),
            contentScale = ContentScale.Crop
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = highlightedTitle,
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(2.dp))

            Text(
                text = highlightedValue,
                modifier = Modifier.fillMaxWidth(),
                style = MaterialTheme.typography.bodySmall
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        Text(
            text = "",
            fontWeight = FontWeight.SemiBold,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Preview
@Composable
private fun PreviewBrivaHistory() {
    MainTheme {
        BrivaHistorySection(
            data = listOf(
                HistoryResponse(
                    listType = "",
                    iconName = "",
                    iconPath = "",
                    title = "Title",
                    subtitle = "Subtitle",
                    description = "Description",
                    value = ""
                )
            )
        )
    }
}
