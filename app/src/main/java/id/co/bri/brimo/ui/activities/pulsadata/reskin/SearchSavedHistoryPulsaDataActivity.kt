package id.co.bri.brimo.ui.activities.pulsadata.reskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.widget.SearchView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryAdapterNs
import id.co.bri.brimo.adapters.SavedAdapterNs
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataException
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataResult
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaErrorCode
import id.co.bri.brimo.databinding.ActivitySearchSavedHistoryBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.listrikrevamp.reskin.FavoriteEventAction
import id.co.bri.brimo.ui.fragments.UpdateSavedItemNsFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.HapusConfirmationBottomSheetFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.util.RxBus
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import javax.inject.Inject
import kotlin.collections.forEach

class SearchSavedHistoryPulsaDataActivity: NewSkinBaseActivity(), SavedAdapterNs.ClickItem, HistoryAdapterNs.ClickItem, IPulsaDataReskinView {
    private var _binding: ActivitySearchSavedHistoryBinding? = null
    protected val binding get() = _binding!!

    private lateinit var savedAdapter: SavedAdapterNs
    private lateinit var historyAdapter: HistoryAdapterNs
    private var filteredSavedResponses = mutableListOf<SavedResponse>()
    private var filteredHistoryResponses = mutableListOf<HistoryResponse>()
    private var defaultIcon = 0
    private var lastClickTime = 0L
    private var currentSearchQuery = ""
    val providerList: MutableList<ProviderItem> = mutableListOf()

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    private val compositeDisposable = CompositeDisposable()

    companion object Companion {
        private var savedResponse: MutableList<SavedResponse> = mutableListOf()
        private var historyResponse: MutableList<HistoryResponse> = mutableListOf()

        private var dataFormPulsa: FormPulsaDataResponse?= null

        @JvmStatic
        fun launchIntent(caller: Activity,
                         savedResponses: MutableList<SavedResponse>,
                         historyResponses: MutableList<HistoryResponse>,
                         dataForm: FormPulsaDataResponse,
                         fromFastMenu: Boolean) {
            dataFormPulsa = dataForm
            isFromFastMenu = fromFastMenu
            savedResponse = savedResponses
            historyResponse = historyResponses

            caller.apply {
                startActivityForResult(
                    Intent(
                        this,
                        SearchSavedHistoryPulsaDataActivity::class.java
                    ), Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivitySearchSavedHistoryBinding.inflate(layoutInflater)

        setContentView(binding.root)
        compositeDisposable.add(
            RxBus.listen(FavoriteEventAction::class.java)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe { action ->
                    showSnackbar(action.message, ALERT_CONFIRM)
                    if(action is FavoriteEventAction.Edit) {
                        showProgress()
                        execForm()
                    }
                }
        )
        onBindIntentData()

        injectDependency()

        onBindView()
    }

    private fun onBindView() {
        GeneralHelperNewSkin.setToolbar(
            this,binding.toolbar.toolbar,
            "Pulsa & Paket Data",
        )

        providerList.addAll(dataFormPulsa!!.provider)

        // Initialize filtered lists as empty initially (blank search result)
        filteredSavedResponses.clear()
        filteredHistoryResponses.clear()

        updateEmptyState()
        setupAdapters()
        setupSearchView()
    }

    private fun setupAdapters() {
        // Saved adapter
        initiateSavedAdapter()

        // History adapter
        initiateHistoryAdapter()
    }

    fun initiateHistoryAdapter() {
        binding.rvRiwayat.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        historyAdapter =
            HistoryAdapterNs(
                this,
                historyResponse,
                this,
                0,
                isFromFastMenu,
                "pulsa",
            )
        binding.rvRiwayat.adapter = historyAdapter
    }

    fun initiateSavedAdapter() {
        binding.rvDaftarFavorit.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        savedAdapter =
            SavedAdapterNs(this, savedResponse, this, 0, BaseActivity.isFromFastMenu)
        binding.rvDaftarFavorit.adapter = savedAdapter
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@SearchSavedHistoryPulsaDataActivity
            start()
        }
    }

    private fun execForm() {
        presenter.executeRequest(
            url = if(isFromFastMenu) GeneralHelper.getString(R.string.url_form_pulsa_data_v5)
            else GeneralHelper.getString(R.string.url_form_pulsa_data_revamp),
            requestParam = if(isFromFastMenu) presenter.getFastMenuRequest() else null,
            responseType = FormPulsaDataResponse::class.java
        )
    }

    private fun execFavorite(url: String, savedId: Int, isSaved: FavoriteType) {
        presenter.executeRequest(
            url = url,
            requestParam = SavedListNs(
                savedId = savedId
            ),
            responseType = RestResponse::class.java,
            isSaved = isSaved
        )
    }

    private fun setupSearchView() {
        // Request focus on SearchView when activity starts
        binding.searchView.post {
            binding.searchView.requestFocus()
        }

        // Handle focus change to manually update background
        binding.searchView.setOnQueryTextFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                binding.searchView.setBackgroundResource(R.drawable.bg_input_double_border_focused_ns)
            } else {
                binding.searchView.setBackgroundResource(R.drawable.bg_input_black_100_brimo_ns)
            }
        }

        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                currentSearchQuery = newText ?: ""
                filterData(currentSearchQuery)
                return false
            }
        })
    }

    private fun filterData(query: String) {
        val lowerCaseQuery = query.lowercase()

        // Filter saved responses
        filteredSavedResponses.clear()
        if (query.isNotEmpty()) {
            savedResponse.forEach { saved ->
                if (saved.title?.lowercase()?.contains(lowerCaseQuery) == true) {
                    filteredSavedResponses.add(saved)
                }
            }
        }

        // Filter history responses
        filteredHistoryResponses.clear()
        if (query.isNotEmpty()) {
            historyResponse.forEach { history ->
                if (history.title?.lowercase()?.contains(lowerCaseQuery) == true) {
                    filteredHistoryResponses.add(history)
                }
            }
        }

        // Update adapters with search query for highlighting
        savedAdapter.setSearchQuery(query)
        historyAdapter.setSearchQuery(query)

        // Notify adapters
        savedAdapter.notifyDataSetChanged()
        historyAdapter.notifyDataSetChanged()

        // Update empty state
        updateEmptyState(query.isNotEmpty())
    }

    private fun updateEmptyState(isSearching: Boolean = false) {
        binding.contentFavorit.isVisible = filteredSavedResponses.isNotEmpty()
        binding.contentRiwayat.isVisible = filteredHistoryResponses.isNotEmpty()

        binding.llStartSearch.isVisible = !isSearching
        binding.llNoDataSearchFound.isVisible = isSearching &&
                filteredSavedResponses.isEmpty() &&
                filteredHistoryResponses.isEmpty()
    }

    private fun updateSavedItemInLists(data: SavedResponse, position: Int, updateAction: (SavedResponse) -> Unit) {
        // Find and update item in original list
        val originalIndex = savedResponse.indexOfFirst { it.value == data.value }
        if (originalIndex != -1) {
            updateAction(savedResponse[originalIndex])
        }

        // Find and update item in filtered list
        val filteredIndex = filteredSavedResponses.indexOfFirst { it.value == data.value }
        if (filteredIndex != -1) {
            updateAction(filteredSavedResponses[filteredIndex])
            savedAdapter.notifyItemChanged(filteredIndex)
        }
    }

    fun findProviderByPrefix(phone: String): ProviderItem? {
        val cleanedPhone = phone.replace(Regex("[^0-9]"), "")
        val prefix4 = cleanedPhone.take(4)
        val prefix5 = if (cleanedPhone.length >= 5) cleanedPhone.take(5) else ""

        return providerList.firstOrNull { provider ->
            (prefix5.isNotEmpty() && GeneralHelper.isContains(provider.prefix, prefix5)) ||
                    GeneralHelper.isContains(provider.prefix, prefix4)
        }
    }

    fun normalizePhoneNumber(input: String): String {
        return input.replace(Regex("^((\\+62)|62|0)"), "")
    }

    private fun handlingError(code: String) {
        println("handlingError: $code")
        when (code) {
            PulsaErrorCode.EXCEPTION_93.code -> {
                showGeneralErrorDialog()
            }
            PulsaErrorCode.EXCEPTION_12.code -> {
                showSnackbar("Terjadi gangguan sistem. Silakan coba lagi, ya.", ALERT_ERROR)
            }
        }
    }

    private fun showGeneralErrorDialog(
        title: String = "Terjadi Kesalahan",
        message: String = "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
        imgName: String = "ic_sad_illustration",
        onRetry: (() -> Unit)? = null
    ) {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_sad_illustration,
            imgName,
            title,
            message,
            btnFirstFunction = { onRetry?.invoke() },
            btnSecondFunction = { /* optional */ },
            false,
            firstBtnTxt = getString(R.string.retry),
            secondBtnTxt = getString(R.string.close),
            false,
            showCloseButton = true,
            showPill = true
        )
    }

    private fun onBindIntentData() {
    }

    override fun onClickSavedItem(data: SavedResponse?) {
        finish()
        val number = data?.subtitle!!.replace(Regex("[^0-9]"), "")

        findProviderByPrefix(number)?.let {
            InquiryPulsaDataReskinActivity.launchIntent(this, isFromFastMenu, ReqInquiry(
                normalizePhoneNumber(number),
                it, dataFormPulsa!!.referenceNumber,
                if(isFromFastMenu) dataFormPulsa!!.accountModel else mutableListOf()
            ),savedResponse)
        }
    }

    override fun onClickUpdateItem(
        savedResponse: SavedResponse?,
        position: Int
    ) {
        val updateSavedItemFragment = UpdateSavedItemNsFragment(savedResponse, {savedResponseItem, type, position ->
            savedResponseItem?.let { item ->
                val savedId = item.value.split("|")[0].toInt()

                when (type) {
                    Constant.EditOptionNs.EDIT -> {
                        val number = item.subtitle.orEmpty()
                        val provider = findProviderByPrefix(number) ?: return@let

                        dataFormPulsa?.let {
                            AddSavedPulsaDataActivity.launchIntent(
                                this,
                                ReqInquiry(
                                    normalizePhoneNumber(number),
                                    provider,
                                    it.referenceNumber,
                                    mutableListOf(),
                                    item.title,
                                    item.value.split("|")[0]
                                ),
                                isFromFastMenu
                            )
                        }
                    }

                    Constant.EditOptionNs.FAV,
                    Constant.EditOptionNs.NON_FAV,
                    Constant.EditOptionNs.HAPUS -> {
                        val (url, favType, withConfirmation) = when (type) {
                            Constant.EditOptionNs.FAV -> Triple(
                                GeneralHelper.getString(R.string.url_favorite_pulsa),
                                FavoriteType.favorite,
                                false
                            )
                            Constant.EditOptionNs.NON_FAV -> Triple(
                                GeneralHelper.getString(R.string.url_unfavorite_pulsa),
                                FavoriteType.unfavorite,
                                false
                            )
                            Constant.EditOptionNs.HAPUS -> Triple(
                                GeneralHelper.getString(R.string.url_delete_pulsa),
                                FavoriteType.removeFavorite,
                                true
                            )
                            else -> return@let
                        }

                        if (withConfirmation) {
                            HapusConfirmationBottomSheetFragment.newInstance(
                                savedResponseItem = item,
                                onConfirm = {
                                    showProgress()
                                    execFavorite(url, savedId, favType)
                                },
                                onCancel = {}
                            ).show(supportFragmentManager, "HapusConfirmationBottomSheet")
                        } else {
                            showProgress()
                            execFavorite(url, savedId, favType)
                        }
                    }
                }
            }

        }, position)
        updateSavedItemFragment.show(supportFragmentManager, "")
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse?) {
        val number = historyResponse?.subtitle!!.replace(Regex("[^0-9]"), "")

        findProviderByPrefix(number)?.let {
            InquiryPulsaDataReskinActivity.launchIntent(this, isFromFastMenu, ReqInquiry(
                normalizePhoneNumber(number),
                it, dataFormPulsa!!.referenceNumber,
                if(isFromFastMenu) dataFormPulsa!!.accountModel else mutableListOf()
            ),savedResponse.toMutableList())
        }
    }

    override fun onSuccess(res: PulsaDataResult) {
        when (res) {
            is PulsaDataResult.Form -> {
                hideProgress()
                val formPulsaDataResponse = res.data

                historyResponse.apply {
                    clear()
                    addAll(formPulsaDataResponse.history)
                }
                savedResponse.apply {
                    clear()
                    addAll(formPulsaDataResponse.saved)
                }

                dataFormPulsa = formPulsaDataResponse
                providerList.addAll(dataFormPulsa!!.provider)

                savedAdapter.notifyDataSetChanged()
                historyAdapter.notifyDataSetChanged()
            }
            is PulsaDataResult.Favorite -> {
                val type = res.data

                execForm()

                showSnackbar(when (type) {
                    FavoriteType.favorite -> "Daftar berhasil di Pin."
                    FavoriteType.removeFavorite -> "Daftar Favorit berhasil dihapus."
                    FavoriteType.unfavorite -> "Daftar Favorit berhasil diunpin."
                    else -> ""
                }, ALERT_CONFIRM)
            }
            else -> Unit
        }
    }

    override fun onExceptionReskin(exception: PulsaDataException) {
        when (exception) {
            is PulsaDataException.KnownError -> handlingError(exception.code)
            is PulsaDataException.UnknownError -> {
                OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                    supportFragmentManager,
                    R.drawable.ic_sad_illustration,
                    "ic_sad_illustration",
                    "Terjadi Kesalahan",
                    "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
                    btnFirstFunction = {

                    },
                    btnSecondFunction = {

                    },
                    false,
                    firstBtnTxt = "Coba Lagi",
                    secondBtnTxt = "Tutup",
                    false,
                    showCloseButton = true,
                    showPill = true
                )
            }
        }
    }

    override fun onSuccessAccountList(
        accountList: MutableList<AccountModel>,
        mainAccount: AccountModel
    ) {
    }

    override fun onDestroy() {
        super.onDestroy()
        isFromFastMenu = false
    }
}