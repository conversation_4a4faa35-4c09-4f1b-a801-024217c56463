package id.co.bri.brimo.ui.activities.pulsadata.reskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayoutMediator
import id.co.bri.brimo.databinding.ActivityInquiryPulsaDataReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.bill.PulsaReskinFragment
import android.os.Parcelable
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.tabs.TabLayout
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.KonfirmasiPulsaRequest
import id.co.bri.brimo.models.apimodel.response.DataListRevamp
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.PulsaList
import id.co.bri.brimo.ui.fragments.SumberDanaFragment
import id.co.bri.brimo.ui.fragments.bill.DataReskinFragment
import id.co.bri.brimo.util.RxBus
import kotlinx.parcelize.Parcelize
import java.io.InputStreamReader
import javax.inject.Inject
import androidx.core.graphics.toColorInt
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataException
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataResult
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaErrorCode
import id.co.bri.brimo.models.apimodel.request.revamppulsa.FastConfirmationPaketCustomRequest
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.ui.activities.pulsadata.reskin.event.EventInquiryPulsaData
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment

class InquiryPulsaDataReskinActivity: NewSkinBaseActivity(),
    SumberDanaFragment.SelectSumberDanaInterface, IPulsaDataReskinView {
    private var _binding: ActivityInquiryPulsaDataReskinBinding? = null
    protected val binding get() = _binding!!

    private lateinit var bindInquiry: ReqInquiry
    private var useApi = true

    private var counter: Int = 0

    private var nominalSelected: PulsaList ?= null

    private var nominalDataSelected: DataListRevamp ?= null

    protected var mListFailed: List<Int>? = null
    protected var mListAccountModel: List<AccountModel>? = null

    private var selectedAccount: AccountModel?= null
    private var buyType: String = "pulsa"

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    companion object {
        const val TAG = "InquiryPulsaDataReskinActivity"
        private var dataSaved: MutableList<SavedResponse> = mutableListOf()

        private var dataNominal: String?= null

        private var dataType = ""

        @JvmStatic
        fun launchIntent(
            caller: Activity, fromFastMenu: Boolean, reqInquiry: ReqInquiry,
            savedList: MutableList<SavedResponse>, nominal: String?= null, type: String = ""
        ) {
            isFromFastMenu = fromFastMenu
            dataSaved = savedList
            dataNominal = nominal
            dataType = type

            val bundle = Bundle()

            Intent(caller, InquiryPulsaDataReskinActivity::class.java).let { intent ->
                intent.putExtras(bundle.apply{
                    putParcelable(TAG, reqInquiry)
                })
                caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityInquiryPulsaDataReskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBindIntentData()

        injectDependency()
        GeneralHelper.setToolbarNs(this, binding.toolbar.toolbar, "Pulsa & Paket Data")

        onBindView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@InquiryPulsaDataReskinActivity
            if(useApi) {
                if(!isFromFastMenu) getAccountList()
            }
            start()
        }
    }

    private fun onBindView() {
        binding.sdnView.apply {
            setOnClickSumberDana {
                counter++
                val selectedIndex = mListAccountModel?.indexOfFirst {
                    selectedAccount?.acoountString == it.acoountString
                } ?: 0

                val amount = if (buyType == "pulsa") nominalSelected?.value ?: 0 else nominalDataSelected?.amount ?: 0
                SumberDanaFragment(
                    mListAccountModel,
                    this@InquiryPulsaDataReskinActivity,
                    counter,
                    mListFailed,
                    selectedIndex,
                    amount,
                    isFromFastMenu
                ).show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
            }

            setOnClickButton {
                showProgress()
                execConfirmation()
            }

            setOnClickRefreshSaldo { presenter.getSaldo() }
            setOnClickRefreshAccount { presenter.getAccountList() }
        }

        // Setup default account if not using API
        if (!useApi) {
            mListAccountModel = parseFromJson()
            selectedAccount = mListAccountModel?.firstOrNull()
            binding.sdnView.account = selectedAccount
        }

        binding.bivNoPelanggan.apply {
            setColorStyle("#181C21".toColorInt(), "#181C21".toColorInt())
            setEnabled(false)
            setText(bindInquiry.phone)
            setExpandHint(true)
            setDistancePrefix(resources.getDimensionPixelSize(R.dimen.size_8dp))
            setStartIconWithUrl(bindInquiry.identityPhone.iconPath)
        }

        val tabTitles = listOf("Pulsa", "Paket Data")
        binding.viewPager.adapter = object: FragmentStateAdapter(this) {
            override fun getItemCount() = tabTitles.size
            override fun createFragment(position: Int): Fragment = when (position) {
                0 -> PulsaReskinFragment(bindInquiry.identityPhone.pulsaList, dataNominal?.toInt()) { s ->
                    nominalSelected = s
                    nominalDataSelected = null
                    binding.sdnView.payAmount = nominalSelected?.value ?: 0
                }
                else -> DataReskinFragment(bindInquiry.identityPhone.dataList, bindInquiry.identityPhone.omniUrl) { s ->
                    nominalDataSelected = s
                    nominalSelected = null
                    binding.sdnView.payAmount = nominalDataSelected?.amount ?: 0
                }
            }
        }

        binding.viewPager.currentItem = if (dataType == "pulsa" || dataType.isEmpty()) 0 else 1

        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.customView = TextView(tab.view.context).apply {
                text = tabTitles[position]
                gravity = Gravity.START or Gravity.CENTER_VERTICAL
                layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                setTextSize(TypedValue.COMPLEX_UNIT_PX, resources.getDimension(R.dimen.size_16sp))
                setTextColor(binding.tabLayout.tabTextColors)
                textAlignment = View.TEXT_ALIGNMENT_VIEW_START
            }
        }.attach()

        binding.tabLayout.addOnTabSelectedListener(object: TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                buyType = if (tab.position == 0) "pulsa" else "data"
                RxBus.send(EventInquiryPulsaData(isClear = true, type = buyType))
            }
            override fun onTabUnselected(tab: TabLayout.Tab) {}
            override fun onTabReselected(tab: TabLayout.Tab) {}
        })

        binding.tabLayout.post {
            (binding.tabLayout.getChildAt(0) as? LinearLayout)?.let { tabStrip ->
                for (i in 0 until tabStrip.childCount) {
                    (tabStrip.getChildAt(i) as? ViewGroup)?.apply {
                        (layoutParams as? LinearLayout.LayoutParams)?.apply {
                            width = LinearLayout.LayoutParams.WRAP_CONTENT
                            gravity = Gravity.START
                        }
                        setPadding(0, 0, 0, 0)
                        forEachChild<TextView> { tv ->
                            tv.gravity = Gravity.START or Gravity.CENTER_VERTICAL
                            tv.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
                        }
                        invalidate()
                    }
                }
            }
        }
    }

    // Extension function to iterate children of a ViewGroup
    private inline fun <reified T : View> ViewGroup.forEachChild(action: (T) -> Unit) {
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            if (child is T) action(child)
        }
    }

    private fun onBindIntentData() {
        intent?.let {
            bindInquiry = it.getParcelableExtra(TAG)!!

            if(isFromFastMenu) {
                mListAccountModel = bindInquiry.accountList
                selectedAccount = bindInquiry.accountList.find { it.isDefault == 1 }?: bindInquiry.accountList[0]
                binding.sdnView.account = selectedAccount
            }
        }
    }

    private fun execConfirmation() {
        val param = KonfirmasiPulsaRequest(
            bindInquiry.referrenceNumber,
            if(buyType.contains("pulsa")) bindInquiry.identityPhone.pulsaCode else bindInquiry.identityPhone.dataCode,
            "0${bindInquiry.phone}",
            if(buyType.contains("pulsa")) nominalSelected?.value.toString() else nominalDataSelected?.amount.toString(),
            "",
            if(buyType.contains("pulsa")) nominalSelected?.value.toString() else nominalDataSelected?.id,
            buyType,
            ""
        )
        presenter.executeRequest(
            url = if(isFromFastMenu) GeneralHelper.getString(R.string.url_confirmation_pulsa_data_fm_revamp)
            else GeneralHelper.getString(R.string.url_confirmation_pulsa_data_revamp),
            requestParam = if(isFromFastMenu) FastConfirmationPaketCustomRequest(
                presenter.getFastMenuRequest(),
                "",
                param.phoneNumber,
                param.providerId,
                param.saveAs,
                param.item,
                param.purchasedType,
                param.note,
                param.amount
            ) else param,
            responseType = GeneralConfirmationResponse::class.java,
        )
    }

    private fun parseFromJson(): List<AccountModel> {
        val inputStream = assets.open("data/mockjsonaccount.json")
        val reader = InputStreamReader(inputStream)

        val gson = Gson()
        val listType = object : TypeToken<List<AccountModel>>() {}.type

        return gson.fromJson(reader, listType)
    }

    override fun onSelectSumberDana(bankModel: AccountModel) {
        selectedAccount = bankModel
        binding.sdnView.account = selectedAccount
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        this.mListFailed = list
    }

    override fun onSuccessAccountList(accountList: MutableList<AccountModel>, mainAccount: AccountModel) {
        mListAccountModel = accountList
        selectedAccount = mainAccount
        binding.sdnView.account = mainAccount
    }

    override fun onErrorFetch() {
        binding.sdnView.setErrorFetching()
    }

    override fun onSuccess(res: PulsaDataResult) {
        when (res) {
            is PulsaDataResult.Confirmation -> {
                finish()
                selectedAccount?.let {
                    ConfirmationPulsaDataActivity.launchIntent(this, isFromFastMenu, res.data, it, dataSaved)
                }
            }
            else -> Unit
        }
    }

    override fun onExceptionReskin(exception: PulsaDataException) {
        when (exception) {
            is PulsaDataException.KnownError -> handlingError(exception.code)
            is PulsaDataException.UnknownError -> {
                OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                    supportFragmentManager,
                    R.drawable.ic_sad_illustration,
                    "ic_sad_illustration",
                    "Terjadi Kesalahan",
                    "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
                    btnFirstFunction = {
                        showGeneralErrorDialog {
                            showProgress()
                            execConfirmation()
                        }
                    },
                    btnSecondFunction = {

                    },
                    false,
                    firstBtnTxt = "Coba Lagi",
                    secondBtnTxt = "Tutup",
                    false,
                    showCloseButton = true,
                    showPill = true
                )
            }
        }
    }

    private fun showGeneralErrorDialog(
        title: String = "Terjadi Kesalahan",
        message: String = "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
        imgName: String = "ic_sad_illustration",
        onRetry: (() -> Unit)? = null
    ) {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_sad_illustration,
            imgName,
            title,
            message,
            btnFirstFunction = { onRetry?.invoke() },
            btnSecondFunction = { /* optional */ },
            false,
            firstBtnTxt = getString(R.string.retry),
            secondBtnTxt = getString(R.string.close),
            false,
            showCloseButton = true,
            showPill = true
        )
    }

    private fun handlingError(code: String) {
        println("handlingError: $code")
        when (code) {
            PulsaErrorCode.EXCEPTION_93.code -> {
                showGeneralErrorDialog()
            }
            PulsaErrorCode.EXCEPTION_12.code -> {
                showGeneralErrorDialog {
                    showProgress()
                    execConfirmation()
                }
//                showSnackbar("Terjadi gangguan sistem. Silakan coba lagi, ya.", ALERT_ERROR)
            }
        }
    }
}

fun Boolean.toggle() = !this

@Parcelize
data class ReqInquiry(
    val phone: String,
    val identityPhone: ProviderItem,
    val referrenceNumber: String,
    val accountList: List<AccountModel> = mutableListOf(),
    val name: String = "",
    val savedId: String = "",
): Parcelable