package id.co.bri.brimo.ui.activities;

import static id.co.bri.brimo.domain.config.Constant.TAG_NOTIF;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentTransaction;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;

import java.io.IOException;

import javax.inject.Inject;

import id.co.bri.brimo.BuildConfig;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.login.ILoginPresenter;
import id.co.bri.brimo.contract.IView.login.ILoginView;
import id.co.bri.brimo.databinding.ActivityLoginBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.GpsTracker;
import id.co.bri.brimo.domain.helpers.ValidationHelper;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.LoginResponse;
import id.co.bri.brimo.security.MyCryptStatic;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity;
import id.co.bri.brimo.ui.activities.waiting.OtpSmsActivity;
import id.co.bri.brimo.ui.activities.waiting.WaitingActivity;
import id.co.bri.brimo.ui.customviews.BilingualUI;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefault;
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog;
import id.co.bri.brimo.ui.fragments.PinFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment;
import id.co.bri.brimo.util.LocaleUtilKt;
import kotlin.Unit;

public class LoginActivity extends BaseActivity implements
        ILoginView,
        View.OnClickListener,
        DialogExitCustom.DialogDefaultListener,
        PinFragment.SendPin,
        DialogInformation.OnActionClick {

    private ActivityLoginBinding binding;



    private static final String TAG = "LoginActivity";

    @Inject
    ILoginPresenter<ILoginView> loginActivityPresenter;

    private static String mDescError;
    static boolean fromFastMenus = false;
    static boolean fromFastMenuReminders = false;
    static boolean isPinReady = false;
    private boolean isMNV = false;
    private Bundle extras = null;

    //data Notifikasi
    private String notifikasiString = null;
    private String urlIbWeb;
    private static NotifikasiModel notifikasiModel = null;
    private DialogSetDefault dialogSetDefault;

    private static boolean isPopup = false;
    private static boolean isPopupNewSkin = false;
    private static boolean isChangePass = false;
    private String code = "";

    private String sRefNum = "";

    private final String[] PERMISSIONS_MAP = {Manifest.permission.ACCESS_FINE_LOCATION};

    public static void launchIntentError(Activity caller, String desc) {
        Intent intent = new Intent(caller, LoginActivity.class);
        caller.startActivity(intent);
        mDescError = desc;
        isPopup = false;
        caller.finish();
    }

    public static void launchIntentFastMenu(Activity caller, String desc, boolean fromFastMenu, boolean fromFastMenuReminder) {
        Intent intent = new Intent(caller, LoginActivity.class);
        caller.startActivity(intent);
        mDescError = desc;
        isPopup = false;
        fromFastMenus = fromFastMenu;
        fromFastMenuReminders = fromFastMenuReminder;
    }

    public static void launchIntentSessionExp(Activity caller, String desc, Object objectJson) {
        Intent intent = new Intent(caller, LoginActivity.class);
        caller.startActivity(intent);
        mDescError = desc;
        isPopup = false;
        if (objectJson instanceof NotifikasiModel) {
            try {
                notifikasiModel = (NotifikasiModel) objectJson;
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG, "launchIntentSessionExp: ", e);
            }
        }
        caller.finish();
    }

    public static void launchIntentSessionEnd(Activity caller, String desc) {
        // menampilkan error ketika session expired
        Intent intent = new Intent(caller, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        caller.startActivity(intent);
        isPopup = false;
        mDescError = desc;
        caller.finish();
    }

    public static void launchIntentWithDialog(Activity caller, String desc) {
        Intent intent = new Intent(caller, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        isPopup = true;
        intent.putExtra(Constant.AKUN_TERBLOKIR, desc);
        caller.startActivity(intent);
    }

    public static void launchIntentWithDialogNewSkin(Activity caller, String desc, Boolean isPassChange) {
        Intent intent = new Intent(caller, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        isPopupNewSkin = true;
        isChangePass = isPassChange;
        intent.putExtra(Constant.AKUN_TERBLOKIR, desc);
        caller.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityLoginBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setStatusColor(R.color.primary_blue80);
        setAntiScreenShoots(true);

        GeneralHelper.setHelperContext(this);


        injectDependency();

        binding.btnLogin.setOnClickListener(this);
        binding.txtLupaPassword.setOnClickListener(this);
        binding.etUsername.setFilters(new InputFilter[]{ValidationHelper.getInputFilterText()});

        setListener();
        onClickKontakKami();
        requestPermission(this, PERMISSIONS_MAP);

        //show snackbar
        if (mDescError != null) {
            onException(mDescError);
            mDescError = null;
        }

        //jika session habis atau logout
        if (loginActivityPresenter != null && (code == null || code.isEmpty())) {
            loginActivityPresenter.updateLoginFlag(false);
        }

        //check popup
        if (isPopup) {
            FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(this, Constant.AKUN_TERBLOKIR, Constant.TITLE_AKUN_TERBLOKIR, getIntent().getStringExtra(Constant.AKUN_TERBLOKIR)
                    , Constant.IMAGE_AKUN_TERBLOKIR, false);
            fragmentBottomDialog.show(getSupportFragmentManager(), "");
            isPopup = false;
        }

        if (isPopupNewSkin) {
            if (isChangePass) {
                OpenBottomSheetGeneralNewSkinFragment.INSTANCE.showDialogInformation(
                        getSupportFragmentManager(), "", "ic_blocked_illustrator",
                        "Akses Kamu Dibatasi Sementara", "Akunmu dibatasi karena kesalahan login. Atur ulang PIN terlebih dahulu.",
                        createKotlinFunction0(firstBtnFunction), false, "Atur Ulang Password Sekarang");
            } else {
                OpenBottomSheetGeneralNewSkinFragment.INSTANCE.showDialogInformation(
                        getSupportFragmentManager(), "", "ic_blocked_illustrator",
                        "Akses Kamu Dibatasi Sementara", "Akunmu dibatasi karena kesalahan login. Atur ulang PIN terlebih dahulu.",
                        createKotlinFunction0(firstBtnFunction), false, "Atur Ulang PIN Sekarang");
            }

        }

        Glide.with(this).load(R.drawable.slice_brimo_default).into(binding.imgBanner);
        initSwitchButton();
        showSuccessSnackbarChangeLanguage();
    }

    private void setDeepLinkWA() {
        Uri uri = getIntent().getData();
        if (uri != null) {
            String url = uri.getQuery();
            code = url.substring(url.indexOf("=") + 1);
        }

        if (code != null && loginActivityPresenter.getLoginFlag()) {
            loginActivityPresenter.updateLoginFlag(true);
            Intent intentLogin = new Intent(LoginActivity.this, DashboardIBActivity.class);
            intentLogin = addIntentNotifikasiTrx(intentLogin);
            intentLogin.putExtra(Constant.LINK_CODE, code);
            startActivity(intentLogin);
            finish();
        }
    }

    private void onClickKontakKami() {
        binding.llKontakKami.setOnClickListener(view -> {
            KontakKamiActivity.launchIntentFreshInstall(this, false, false);
        });
    }

    @Override
    public void onClick(View view) {

        switch (view.getId()) {
            case R.id.btnLogin:
                loginActivityPresenter.updateUserAlias(getUsername());
                loginActivityPresenter.onLoginSubmit();
                break;
            case R.id.txtLupaPassword:
                OpenBottomSheetGeneralNewSkinFragment.INSTANCE.showDialogConfirmation(
                        getSupportFragmentManager(), R.drawable.lock_password, "ic_account_saved",
                        "Lupa Password", "Jika lupa password, kamu bisa mengatur ulang password melalui verifikasi data",
                        createKotlinFunction0(firstBtnFunction), createKotlinFunction0(secBtnFunction), false, "Atur Ulang Password", "Batalkan", false, false, true
                );
//                InfoForgotUserPassActivity.launchIntent(this, true);
                break;
            default:
                break;
        }
    }

    Runnable firstBtnFunction = () -> {
        Intent intent = new Intent(LoginActivity.this, OnboardingInputNumberForgetPassActivity.class);
        startActivity(intent);
    };

    Runnable secBtnFunction = () -> {
        // do nothing
    };

    @Override
    public String getUsername() {
        return binding.etUsername.getText().toString();
    }

    @Override
    public String getPassword() {
        return binding.etPassword.getText().toString();
    }

    @Override
    public void onLoginSuccess() {
        fromFastMenuReminders = getIntent().getBooleanExtra("fromFastMenuReminder", fromFastMenuReminders);

        if (fromFastMenuReminders) {
            parseDataNotifFastMenu(getIntent());
        } else {
            if (fromFastMenus) {
                MutasiActivity.launchIntentLogin(this, fromFastMenus);
            } else {
                Intent intentLogin = new Intent(LoginActivity.this, DashboardIBActivity.class);
                intentLogin = addIntentNotifikasiTrx(intentLogin);
                intentLogin.putExtra(Constant.LINK_CODE, code);
                startActivity(intentLogin);
                finish();
            }
        }
    }

    @Override
    public void onSubmitSuccess(LoginResponse response) {
        if (response.isPinReady())
            KonfirmasiPinActivity.launchIntentPersistant(LoginActivity.this, response.getRefNum());
        else if (response.getType().equalsIgnoreCase(GeneralHelper.getString(R.string.otp)))
            OtpSmsActivity.launchIntent(this, response);
        else
            WaitingActivity.launchIntent(this, true, response.getOtpExpiredInSecond(), response.getRefNum());
    }

    @Override
    public void onDeviceChanged(String desc, LoginResponse loginResponse) {
        isPinReady = loginResponse.isPinReady();
        sRefNum = loginResponse.getRefNum();
        isMNV = false;
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this,
                GeneralHelper.getString(R.string.selamat_datang_kembali), desc);
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onCreatePin() {
        BuatPinActivity.launchIntent(this);
    }

    @Override
    public void onChangeUsername(String deskripsi) {
        Bundle bundle = new Bundle();
        bundle.putString(Constant.DESCRIPTION, deskripsi);
        Intent intentEditUsername = new Intent(LoginActivity.this, EditUsernameActivity.class);

        intentEditUsername.putExtras(bundle);
        startActivity(intentEditUsername);
        finish();
    }

    @Override
    public void onLogout(String message) {
        showSnackbarErrorMessageRevamp(message, ALERT_ERROR, LoginActivity.this, false);
    }

    @Override
    public void setVisibleFingerprint(boolean visibility) {
        //do nothing
    }

    private void setListener() {
        binding.etPassword.addTextChangedListener(activityTextListener);
        binding.etUsername.addTextChangedListener(activityTextListener);
        ValidationHelper.disableCopyPaste(binding.etPassword);
        ValidationHelper.disableCopyPaste(binding.etUsername);
    }

    @Override
    protected void afterText(Editable editable) {
        cekInput();
    }

    /**
     * Validasi field username dan password
     */
    protected void cekInput() {
        if (binding.etUsername.length() > 0 && binding.etPassword.length() > 0) {
            binding.btnLogin.setEnabled(true);
            binding.btnLogin.setBackground(
                    ContextCompat.getDrawable(
                            this,
                            R.drawable.rounded_button_blue));
            binding.btnLogin.setEnabled(true);
            binding.btnLogin.setTextColor(
                    ContextCompat.getColor(
                            this,
                            R.color.neutral_light10));
        } else {
            binding.btnLogin.setBackground(
                    ContextCompat.getDrawable(
                            this,
                            R.drawable.rounded_button_neutral_light20));
            binding.btnLogin.setTextColor(
                    ContextCompat.getColor(
                            this,
                            R.color.neutral_light40));
            binding.btnLogin.setEnabled(false);
        }
    }

    @Override
    public void onChangeDevice(Integer otpExpiredSeconds) {
        WaitingActivity.launchIntent(this, true, otpExpiredSeconds, sRefNum);
    }

    @Override
    public void onException12() {

    }

    @Override
    public void onChangeMNV(String desc, LoginResponse loginResponse) {
        isPinReady = loginResponse.isPinReady();
        sRefNum = loginResponse.getRefNum();
        isMNV = true;
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this,
                GeneralHelper.getString(R.string.title_dialog_login_mnv), desc,
                GeneralHelper.getString(R.string.btnBatal), GeneralHelper.getString(R.string.lanjutkan));
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onExceptionLoginExceed(ExceptionResponse response) {
        GeneralHelper.showBlockedAccountDialog(
                getSupportFragmentManager(),
                response,
                submitBtnAction,
                cancelBtnAction);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (loginActivityPresenter != null) {
            loginActivityPresenter.setView(this);
            loginActivityPresenter.setUrlLogin(GeneralHelper.getString(R.string.url_login_v5));
            loginActivityPresenter.setUrlLogout(GeneralHelper.getString(R.string.url_logout_v5));
            loginActivityPresenter.setUrlChange(GeneralHelper.getString(R.string.url_change_device_v2));
            loginActivityPresenter.start();
        }
    }


    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showBottomDialog(this, message);
        else
            showSnackbarErrorMessageRevamp(message, -2, this, false);
    }

    @Override
    public void onResume() {
        super.onResume();

        if (loginActivityPresenter != null) {
            loginActivityPresenter.setView(this);
            loginActivityPresenter.setUrlLogin(GeneralHelper.getString(R.string.url_login_v5));
            loginActivityPresenter.setUrlLogout(GeneralHelper.getString(R.string.url_logout_v5));
            loginActivityPresenter.setUrlChange(GeneralHelper.getString(R.string.url_change_device_v2));
            loginActivityPresenter.start();

            cekInput();
            //show snackbar
            if (mDescError != null) {
                onException(mDescError);
            }
            mDescError = null;
        }
    }

    @Override
    protected void onDestroy() {
        //unsubscribe presenter
        loginActivityPresenter.stop();

        super.onDestroy();
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {
        // do nothing
    }

    public static void forceLogout(Activity caller, String message) {
        Intent intent = new Intent(caller, LoginActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

        mDescError = message;

        caller.startActivity(intent);
        caller.setResult(RESULT_OK);
        caller.finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_CREATE_PIN) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK);
                finish();
            }
        }
        if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_CANCELED) {
                if (data != null) {
                    showSnackbarErrorMessageRevamp(data.getStringExtra("desc"), ALERT_ERROR, this, false);
                }
            }
        }
        if (requestCode == Constant.REQ_MNV && resultCode == RESULT_CANCELED)
            if (data != null) {
                showSnackbarErrorMessageRevamp(data.getStringExtra(Constant.TAG_ERROR_MESSAGE), ALERT_ERROR, this, false);
            }
    }


    /**
     * @return
     */
    private Intent addIntentNotifikasiTrx(Intent newIntent) {
        if (notifikasiModel != null && newIntent != null) {

            //reminder payment
            if (notifikasiModel.getType().equals(Constant.REMINDER_BLAST_TYPE)) {
                notifikasiString = null;
            } else if (notifikasiModel.getType().equals(Constant.RECOMENDATION_BLAST_TYPE)) {
                notifikasiString = null;
            } else if (notifikasiModel.getType().equals(Constant.TARTUN_NDS_TYPE)) {
                notifikasiString = null;
            } else if (!notifikasiModel.getType().equals(Constant.PROMO_BLAST_TYPE)) {
                notifikasiString = new Gson().toJson(notifikasiModel);
                if (notifikasiString != null) {
                    newIntent.putExtra(Constant.TAG_NOTIF, notifikasiString);
                }
            } else if (notifikasiModel.getType().equals(Constant.BlastType.CASHBACK_BLAST_TYPE)) {
                notifikasiString = null;
            }
        }
        notifikasiModel = null;

        return newIntent;
    }

    private void parseDataNotifFastMenu(Intent intent) {
        if (intent != null) {

            // Get data from notifikasi
            extras = intent.getExtras();

            if (extras != null) {
                notifikasiString = extras.getString(Constant.TAG_NOTIF);

                if (notifikasiString != null) {
                    if (!notifikasiString.isEmpty()) {

                        try {
                            Gson gson = new Gson();
                            notifikasiModel = gson.fromJson(notifikasiString, NotifikasiModel.class);

                            if (notifikasiString != null) {
                                Intent intent1 = new Intent(LoginActivity.this, DashboardIBActivity.class);
                                intent1.putExtra(TAG_NOTIF, notifikasiString);
                                intent1.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

                                startActivity(intent1);
                                finish();
                            }
                            notifikasiString = null;

                        } catch (Exception e) {
                            if (!GeneralHelper.isProd())
                                Log.e(TAG, "parseDataNotif: ", e);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void onClickYes() {
        if (isMNV) {
            PinFragment pinFragment = new PinFragment(this, this);
            pinFragment.show();
        } else if (isPinReady)
            KonfirmasiPinActivity.launchIntentPersistant(LoginActivity.this, sRefNum);
        else loginActivityPresenter.changeDevice(sRefNum);
    }

    @Override
    public void onException99(String message) {
        dialogSetDefault = new DialogSetDefault(this,
                GeneralHelper.getString(R.string.maaf_sistem_sedang_sibuk), message,
                GeneralHelper.getString(R.string.ok),
                GeneralHelper.getString(R.string.Cancel), Constant.DIALOG_LOGIN);
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogSetDefault, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    protected void onClickYesRC99(int reqId) {
        if (reqId == Constant.DIALOG_LOGIN) {
            //get build URL
            String url = null;
            try {
                url = MyCryptStatic.decryptAsBase64(BuildConfig.M_RESET_URL);

                if (url != null) {
                    //goto URL
                    Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    startActivity(browserIntent);
                }
            } catch (IOException e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "onClickYesRC99: ", e);
                }
            }
        }
    }

    @Override
    public void onSendPinComplete(String pin) {
        LoadingMNVActivity.launchIntent(this, pin, sRefNum);
    }

    @Override
    public void onLupaPin() {
        DialogInformation dialog = new DialogInformation(this, "ic_forbidden_lupa_pin",
                GeneralHelper.getString(R.string.title_maaf_lupa_pin),
                GeneralHelper.getString(R.string.desc_maaf_lupa_pin_username),
                GeneralHelper.getString(R.string.ok),
                this, true, false);
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialog, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onClickAction() {
        // do nothing
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);

        setIntent(intent);
    }

    private void getLocation() {
        GpsTracker gpsTracker = new GpsTracker(this);
        if (gpsTracker.canGetLocation()) {
            loginActivityPresenter.getLocation(gpsTracker.getLatitude() + "," + gpsTracker.getLongitude());
        }
    }

    protected void requestPermission(Activity activity, String... permissions) {
        if (!hasPermissions(activity, permissions)) {
            ActivityCompat.requestPermissions(activity, permissions, Constant.REQUEST_LOCATION_MAP);
        } else {
            getLocation();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        try {
            if (requestCode == Constant.REQUEST_LOCATION_MAP) {
                getLocation();
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "onRequestPermissionsResult: ", e);
            }
        }
    }

    private void initSwitchButton(){
        binding.switchButton.setText(LocaleUtilKt.getSavedLanguage(this));
        binding.switchButton.setOnClickListener( () -> {
            new BilingualUI(this, getSupportFragmentManager(), LocaleUtilKt.getSavedLanguage(this),(value) ->{
                LocaleUtilKt.applyLanguageContext(this, value);
                recreatedActivity();

                return Unit.INSTANCE;
            });
            return null;
        });
    }

    public void showSuccessSnackbarChangeLanguage() {
        if (getIntent().getBooleanExtra(Constant.IS_CHANGE_LANGUAGE, false)) {
            GeneralHelper.showSnackBarGreen(
                    findViewById(R.id.content),
                    GeneralHelper.getString(R.string.txt_language_change_successfully)
            );
        }
        getIntent().removeExtra(Constant.IS_CHANGE_LANGUAGE);
    }
}