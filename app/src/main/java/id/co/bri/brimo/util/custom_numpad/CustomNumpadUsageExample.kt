package id.co.bri.brimo.util.custom_numpad

import android.app.Activity
import android.view.MotionEvent
import android.widget.EditText
import com.google.android.material.textfield.TextInputLayout
import id.co.bri.brimo.util.attachNumpad
import id.co.bri.brimo.util.handleCustomNumpadTouchEvent

/**
 * Example implementation showing how to use the enhanced CustomNumpadHelper
 * with proper keyboard dismissal and btnContainer positioning
 */
class CustomNumpadUsageExample {
    
    companion object {
        /**
         * Example of how to set up CustomNumpadHelper in an Activity
         */
        fun setupCustomNumpadInActivity(
            activity: Activity,
            editText: EditText,
            numpadType: NumpadType = NumpadType.PHONE
        ): CustomNumpadHelper {
            
            // Method 1: Direct EditText attachment
            return editText.attachNumpad(
                activity = activity,
                type = numpadType,
                onPinComplete = { pin ->
                    // Handle when user completes input
                    println("Pin entered: $pin")
                },
                onFocusChanged = { hasFocus ->
                    // Handle focus changes
                    println("Focus changed: $hasFocus")
                }
            )
        }
        
        /**
         * Example of how to set up CustomNumpadHelper with TextInputLayout
         */
        fun setupCustomNumpadWithTextInputLayout(
            activity: Activity,
            textInputLayout: TextInputLayout,
            numpadType: NumpadType = NumpadType.NOMINAL
        ) {
            
            // Method 2: TextInputLayout attachment
            textInputLayout.attachNumpad(
                activity = activity,
                type = numpadType,
                onPinComplete = { pin ->
                    // Handle when user completes input
                    println("Pin entered: $pin")
                },
                onAttached = { numpadHelper ->
                    // Store reference to numpadHelper for later use
                    // e.g., this.numpadHelper = numpadHelper
                },
                onFocusChanged = { hasFocus ->
                    // Handle focus changes
                    println("Focus changed: $hasFocus")
                }
            )
        }
        
        /**
         * Example of how to handle touch events in Activity's dispatchTouchEvent
         */
        fun handleTouchEventsInActivity(
            activity: Activity,
            ev: MotionEvent,
            numpadHelper: CustomNumpadHelper
        ): Boolean {
            
            // Use the utility extension function
            activity.handleCustomNumpadTouchEvent(ev, numpadHelper)
            
            // Or implement manually:
            /*
            if (ev.action == MotionEvent.ACTION_DOWN) {
                if (numpadHelper.isKeyboardShowing()) {
                    val tappedOutsideNumpad = !numpadHelper.isTouchInsideNumpad(ev)
                    
                    val view = activity.currentFocus
                    val tappedOutsideEditText = if (view is EditText) {
                        val outRect = Rect()
                        view.getGlobalVisibleRect(outRect)
                        !outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())
                    } else {
                        true
                    }

                    if (tappedOutsideEditText && tappedOutsideNumpad) {
                        view?.clearFocus()
                        numpadHelper.hideKeyboard()
                        return true
                    }
                }
            }
            */
            
            return false
        }
    }
}

/**
 * Example Activity implementation
 */
/*
class ExampleActivity : AppCompatActivity() {
    private lateinit var numpadHelper: CustomNumpadHelper
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_example)
        
        val editText = findViewById<EditText>(R.id.etExample)
        
        // Setup custom numpad
        numpadHelper = editText.attachNumpad(
            activity = this,
            type = NumpadType.PHONE,
            onPinComplete = { pin ->
                // Handle completed input
                Toast.makeText(this, "Entered: $pin", Toast.LENGTH_SHORT).show()
            },
            onFocusChanged = { hasFocus ->
                // Handle focus changes if needed
            }
        )
    }
    
    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        // Handle custom numpad touch events
        if (::numpadHelper.isInitialized) {
            handleCustomNumpadTouchEvent(ev, numpadHelper)
        }
        
        return super.dispatchTouchEvent(ev)
    }
}
*/

/**
 * Key Features of Enhanced CustomNumpadHelper:
 * 
 * 1. KEYBOARD DISMISSAL:
 *    - Works even when EditText has text content
 *    - Properly handles touch events outside keyboard and EditText
 *    - Uses utility extension function for easy implementation
 * 
 * 2. BTNCONTAINER POSITIONING:
 *    - Automatically finds btnContainer in the layout
 *    - Adjusts padding to accommodate keyboard height
 *    - Restores original position when keyboard is hidden
 *    - Supports multiple btnContainer naming conventions
 * 
 * 3. IMPROVED FOCUS HANDLING:
 *    - Better focus change management
 *    - Click handling even when EditText already has focus
 *    - Proper keyboard state tracking
 * 
 * 4. EASY INTEGRATION:
 *    - Simple attachNumpad extension functions
 *    - Utility function for touch event handling
 *    - Minimal code changes required in existing activities
 */
