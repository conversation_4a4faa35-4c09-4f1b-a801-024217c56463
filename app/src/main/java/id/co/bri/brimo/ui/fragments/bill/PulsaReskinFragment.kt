package id.co.bri.brimo.ui.fragments.bill

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentPulsaReskinBinding
import id.co.bri.brimo.databinding.ItemNominalReskinBinding
import id.co.bri.brimo.models.apimodel.response.PulsaList
import id.co.bri.brimo.ui.activities.pulsadata.reskin.event.EventInquiryPulsaData
import id.co.bri.brimo.ui.fragments.NewSkinBaseFragment
import id.co.bri.brimo.util.RxBus
import io.reactivex.android.schedulers.AndroidSchedulers

class PulsaReskinFragment(
    private val nominalList: List<PulsaList>,
    private val nominal: Int?= null,
    private val onItemClick: (PulsaList) -> Unit
): NewSkinBaseFragment() {

    private var _binding: FragmentPulsaReskinBinding? = null
    protected val binding get() = _binding!!

    private var indexSelected: Int?= null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPulsaReskinBinding.inflate(inflater, container, false)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        nominal?.let {
            indexSelected = nominalList.indexOfFirst { it.value == nominal }
        }

        val myAdapter = NominalAdapter(nominalList, indexSelected) { selectedNominal ->
            onItemClick.invoke(selectedNominal)
        }

        binding.recyclerView.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = myAdapter
        }

        RxBus.listen(EventInquiryPulsaData::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                if(event.type == "pulsa") {
                    myAdapter.clearItemClick()
                }
            }
    }
}

class NominalAdapter(
    private val items: List<PulsaList>,
    private val indexSelected: Int?= null,
    private val onItemClick: (PulsaList) -> Unit
) : RecyclerView.Adapter<NominalAdapter.ViewHolder>() {

    private var selectedPosition = RecyclerView.NO_POSITION

    inner class ViewHolder(val binding: ItemNominalReskinBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener {
                val previousPosition = selectedPosition
                selectedPosition = adapterPosition
                notifyItemChanged(previousPosition)
                notifyItemChanged(selectedPosition)
                onItemClick(items[adapterPosition])
            }

            indexSelected?.let {
                selectedPosition = it
            }
        }

        fun bind(nominal: String, isSelected: Boolean) {
            binding.textNominal.text = nominal
            binding.root.setBackgroundResource(
                if (isSelected) R.drawable.bg_selected_reskin else R.drawable.bg_unselected_reskin
            )
            binding.textNominal.setTextColor(
                ContextCompat.getColor(binding.root.context,
                    if (isSelected) R.color.white else R.color.black
                )
            )
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemNominalReskinBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val isSelected = position == selectedPosition
        holder.bind(items[position].text, isSelected)
    }

    fun clearItemClick() {
        selectedPosition = RecyclerView.NO_POSITION
        notifyDataSetChanged()
    }
}
