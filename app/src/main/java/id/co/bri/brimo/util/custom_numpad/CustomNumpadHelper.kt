package id.co.bri.brimo.util.custom_numpad

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.text.InputType
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.GridLayout
import android.widget.ImageView
import android.widget.Space
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatImageButton
import androidx.core.content.ContextCompat
import id.co.bri.brimo.R

class CustomNumpadHelper(
    private val activity: Activity,
    private val editText: EditText,
    private val inputType: NumpadType,
    private val onPinEntered: (String) -> Unit,
    private val onFocusChanged: ((Boolean) -> Unit)? = null,
) {
    private var keyboardView: View? = null
    private var pin: String = ""
    private var isKeyboardVisible = false
    private var originalBottomPadding = 0
    private var btnContainer: View? = null
    private var ignoreNextFocusChange = false

    init {
        // Setup agar EditText bisa seleksi & cursor muncul tanpa ubah XML
        editText.apply {
            isCursorVisible = true
            isLongClickable = true
            setTextIsSelectable(true)
            inputType = InputType.TYPE_NULL // nonaktifkan keyboard sistem
        }

        // Find btnContainer automatically
        findBtnContainer()
    }

    private fun findBtnContainer() {
        val rootView = activity.findViewById<ViewGroup>(android.R.id.content)
        btnContainer = findViewByIdRecursive(rootView, "btnContainer")
            ?: findViewByIdRecursive(rootView, "btn_container")
            ?: findViewByIdRecursive(rootView, "buttonContainer")

        btnContainer?.let {
            originalBottomPadding = it.paddingBottom
        }
    }

    private fun findViewByIdRecursive(parent: ViewGroup, targetIdName: String): View? {
        for (i in 0 until parent.childCount) {
            val child = parent.getChildAt(i)
            val resourceName = try {
                activity.resources.getResourceEntryName(child.id)
            } catch (e: Exception) {
                null
            }

            if (resourceName == targetIdName) {
                return child
            }

            if (child is ViewGroup) {
                val result = findViewByIdRecursive(child, targetIdName)
                if (result != null) return result
            }
        }
        return null
    }

    fun showKeyboard() {
        if (keyboardView == null) {
            keyboardView = LayoutInflater.from(activity).inflate(R.layout.layout_custom_keyboard, null).apply {
                // Don't make the container clickable to avoid consuming touch events
                isClickable = false
                isFocusable = false
            }
            val params = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT,
                Gravity.BOTTOM
            )
            keyboardView!!.layoutParams = params

            val rootView = activity.findViewById<ViewGroup>(android.R.id.content)
            rootView.addView(keyboardView)

            // Measure keyboard height first
            keyboardView!!.measure(
                View.MeasureSpec.makeMeasureSpec(rootView.width, View.MeasureSpec.EXACTLY),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )

            keyboardView!!.translationY = keyboardView!!.measuredHeight.toFloat()
            keyboardView!!.alpha = 0f

            setupKeyboard(keyboardView!!)
        }

        if (!isKeyboardVisible) {
            isKeyboardVisible = true

            // Ensure EditText has focus and cursor is visible
            editText.post {
                if (!editText.hasFocus()) {
                    editText.requestFocus()
                }
                editText.isCursorVisible = true
            }

            keyboardView?.apply {
                visibility = View.VISIBLE

                // Force layout to ensure proper positioning
                post {
                    // Adjust btnContainer position after layout is complete
                    adjustBtnContainerForKeyboard(true)
                }

                animate()
                    ?.translationY(0f)
                    ?.alpha(1f)
                    ?.setDuration(250)
                    ?.setInterpolator(DecelerateInterpolator())
                    ?.start()
            }

            onFocusChanged?.invoke(true)
        }
    }

    fun hideKeyboard() {
        if (isKeyboardVisible) {
            isKeyboardVisible = false

            // Restore btnContainer position
            adjustBtnContainerForKeyboard(false)

            keyboardView?.animate()
                ?.translationY(keyboardView!!.measuredHeight.toFloat())
                ?.alpha(0f)
                ?.setDuration(200)
                ?.setInterpolator(AccelerateInterpolator())
                ?.withEndAction {
                    keyboardView?.visibility = View.GONE
                }
                ?.start()

            onFocusChanged?.invoke(false)
        }
    }

    private fun adjustBtnContainerForKeyboard(show: Boolean) {
        btnContainer?.let { container ->
            if (show) {
                keyboardView?.let { keyboard ->
                    val keyboardHeight = keyboard.measuredHeight
                    container.setPadding(
                        container.paddingLeft,
                        container.paddingTop,
                        container.paddingRight,
                        originalBottomPadding + keyboardHeight
                    )
                }
            } else {
                container.setPadding(
                    container.paddingLeft,
                    container.paddingTop,
                    container.paddingRight,
                    originalBottomPadding
                )
            }
        }
    }

    fun isKeyboardShowing(): Boolean = isKeyboardVisible

    fun setIgnoreNextFocusChange(ignore: Boolean) {
        ignoreNextFocusChange = ignore

        // When ignoring focus change, ensure cursor remains visible
        if (ignore && isKeyboardVisible) {
            editText.post {
                if (!editText.hasFocus()) {
                    editText.requestFocus()
                }
                editText.isCursorVisible = true
            }
        }
    }

    fun shouldIgnoreFocusChange(): Boolean {
        val shouldIgnore = ignoreNextFocusChange
        if (ignoreNextFocusChange) {
            ignoreNextFocusChange = false // Reset after use
        }
        return shouldIgnore
    }

    fun ensureCursorVisible() {
        if (isKeyboardVisible) {
            editText.post {
                if (!editText.hasFocus()) {
                    editText.requestFocus()
                }
                editText.isCursorVisible = true
            }
        }
    }

    private fun setupKeyboard(view: View) {
        val gridLayout = view.findViewById<GridLayout>(R.id.number_pad)
        val context = gridLayout.context
        val keys = if (inputType == NumpadType.PHONE) phoneKeys() else nominalKeys()

        gridLayout.removeAllViews()

        for (key in keys) {
            val layoutParams = GridLayout.LayoutParams().apply {
                width = 0
                height = dpToPx(context, 52)
                columnSpec = GridLayout.spec(GridLayout.UNDEFINED, 1f)
                setMargins(dpToPx(context, 6), dpToPx(context, 6), dpToPx(context, 6), dpToPx(context, 6))
            }

            val viewToAdd: View = when (key) {
                "←" -> {
                    AppCompatImageButton(context).apply {
                        setImageResource(R.drawable.ic_backspace)
                        layoutParams.setGravity(Gravity.FILL)
                        scaleType = ImageView.ScaleType.CENTER_INSIDE
                        this.layoutParams = layoutParams
                        setBackgroundColor(Color.TRANSPARENT)

                        setOnClickListener {
                            val start = editText.selectionStart.coerceAtLeast(0)
                            val end = editText.selectionEnd.coerceAtLeast(0)

                            pin = editText.text.toString()

                            if (start != end) {
                                // Hapus teks yang diseleksi
                                pin = pin.removeRange(start, end)
                                editText.setText(pin)
                                editText.setSelection(start)
                            } else if (start > 0) {
                                // Hapus karakter sebelum cursor
                                pin = pin.removeRange(start - 1, start)
                                editText.setText(pin)
                                editText.setSelection(start - 1)
                            }
                        }
                    }
                }

                "" -> {
                    Space(context).apply {
                        minimumHeight = dpToPx(context, 52)
                        this.layoutParams = layoutParams
                    }
                }

                else -> {
                    AppCompatButton(context).apply {
                        text = key
                        textSize = 20f
                        setTypeface(typeface, Typeface.BOLD)
                        gravity = Gravity.CENTER
                        background = ContextCompat.getDrawable(context, R.drawable.rounded_keypad_button)
                        this.layoutParams = layoutParams

                        setOnClickListener {
                            val start = editText.selectionStart.coerceAtLeast(0)
                            val end = editText.selectionEnd.coerceAtLeast(0)

                            pin = editText.text.toString().substring(0, start) +
                                    key +
                                    editText.text.toString().substring(end)

                            editText.setText(pin)
                            editText.setSelection(start + key.length)
                        }
                    }
                }
            }

            gridLayout.addView(viewToAdd)
        }
    }

    private fun dpToPx(context: Context, dp: Int): Int {
        return (dp * context.resources.displayMetrics.density).toInt()
    }

    private fun nominalKeys() = listOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "000", "←")
    private fun phoneKeys() = listOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "", "0", "←")

    fun isTouchInsideNumpad(ev: MotionEvent): Boolean {
        val view = keyboardView ?: return false

        // Only check if keyboard is visible and properly laid out
        if (!isKeyboardVisible || view.visibility != View.VISIBLE) {
            return false
        }

        val location = IntArray(2)
        view.getLocationOnScreen(location)
        val x = ev.rawX.toInt()
        val y = ev.rawY.toInt()

        // Ensure view has proper dimensions
        val viewWidth = if (view.width > 0) view.width else view.measuredWidth
        val viewHeight = if (view.height > 0) view.height else view.measuredHeight

        val isInside = x >= location[0] && x <= location[0] + viewWidth &&
                y >= location[1] && y <= location[1] + viewHeight

        // Debug logging
        Log.d("CustomNumpad", "Touch at ($x, $y), Keyboard at (${location[0]}, ${location[1]}) size ${viewWidth}x${viewHeight}, isInside: $isInside")

        return isInside
    }
}

enum class NumpadType {
    NOMINAL, PHONE
}