package id.co.bri.brimo.data.api.observer

import androidx.databinding.Observable
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.domain.PingConnection
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.models.apimodel.response.RestResponse
import io.reactivex.observers.DisposableObserver
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.util.DecodeResult
import id.co.bri.brimo.util.ResponseDecoder
import id.co.bri.brimo.util.extension.DebugType
import retrofit2.HttpException
import java.net.ConnectException
import java.net.SocketTimeoutException
import kotlin.apply
import kotlin.jvm.java
import kotlin.text.isNullOrEmpty

abstract class ApiReskinObserver(
    protected var view: IMvpView,
    protected var sequenceNumber: String
): DisposableObserver<Any>(), Observable, PingConnection.PingConnectionListener {

    companion object {
        private val TAG = ApiReskinObserver::class.java.name
    }

    protected var restResponse: RestResponse? = null
    protected var stringResponse: String? = null
    protected var responseId: String? = null
    private var pingConnection: PingConnection? = null

    override fun onNext(n: Any) {

        if (n !is String) {
            onError(IllegalStateException("onNext: Unidentified Object"))
            return
        }

        stringResponse = n
        when (val result = ResponseDecoder.decodeStringResponse(n, sequenceNumber)) {
            is DecodeResult.Success -> {
                // TODO: filtering success or failed
                if(result.restResponse.desc.contains("fail")) {
                    handleSpecificError(result.restResponse)
                } else {
                    onApiCallSuccess(result.restResponse)
                }
            }
            is DecodeResult.Error -> {
                handleSpecificError(result.restResponse)
            }
            is DecodeResult.Exception -> onError(result.throwable)
        }
    }

    private fun handleSpecificError(restResponse: RestResponse) {
        view.hideProgress()
        when (restResponse.code) {
            Constant.RE_SESSION_END -> view.onSessionEnd(restResponse.desc)
            Constant.RE_HIT_EXCEED -> view.onException06(restResponse.getData(ExceptionResponse::class.java))
            Constant.RE99 -> view.onException99(restResponse.desc)
            Constant.RE_LIMIT_EXCEED -> view.onExceptionLimitExceed(restResponse.getData(GeneralResponse::class.java))
            Constant.RE01 -> view.onException(Constant.SERVER_UNDER_MAINTENANCE)
            Constant.RE_FITUR_OFF -> view.onExceptionFO(restResponse.getData(EmptyStateResponse::class.java))
            else -> onApiCallError(restResponse.mapping())
        }
    }

    override fun onError(e: Throwable) {
        view.hideProgress()
        when (e) {
            is SocketTimeoutException -> getConnection()
            is HttpException -> {
                when (e.code()) {
                    428 -> view.onException(RestResponse.ResponseCodeEnum.RC_DISABLE_SINGALARITY.value)
                    426 -> view.onException(RestResponse.ResponseCodeEnum.RC_ENABLE_SINGALARITY.value)
                    else -> view.onException(Constant.SERVER_UNDER_MAINTENANCE)
                }
            }
            is ConnectException -> getConnection()
            else -> {
                GeneralHelper.debugMessage(e, TAG, DebugType.DEBUG)
                view.onException(Constant.KONEKSI_TERPUTUS)
            }
        }
    }

    private fun setStatusResponse(times: Float) {
        view.hideProgress()

        view.onException(if(times < 1000 && times != 0f)
                Constant.SERVER_UNDER_MAINTENANCE
            else
                Constant.KONEKSI_TERPUTUS
        )
    }

    private fun getConnection() {
        pingConnection = PingConnection().apply {
            setPingConnectionListener(this@ApiReskinObserver)
            getTimePing()
        }
    }

    override fun onNetworkChanged(time: Float) {
        setStatusResponse(time)
    }

    override fun notConnected() {
        view.onException(Constant.SERVER_UNDER_MAINTENANCE)
    }

    override fun addOnPropertyChangedCallback(callback: Observable.OnPropertyChangedCallback?) {
        // no-op
    }

    override fun removeOnPropertyChangedCallback(callback: Observable.OnPropertyChangedCallback?) {
        // no-op
    }

    override fun onComplete() {
        // no-op
    }

    protected abstract fun onApiCallSuccess(response: RestResponse)
    protected abstract fun onApiCallError(errRes: ResExceptionErr)
}

data class ResExceptionErr(
    val code: String,
    val desc: String,
)

fun RestResponse.mapping(): ResExceptionErr = ResExceptionErr(
    code = code,
    desc = desc,
)