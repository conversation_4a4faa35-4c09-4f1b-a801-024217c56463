package id.co.bri.brimo.ui.fragments.bill

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentDataReskinBinding
import id.co.bri.brimo.databinding.ItemPaketDataReskinBinding
import id.co.bri.brimo.models.apimodel.response.DataListRevamp
import id.co.bri.brimo.ui.activities.pulsadata.reskin.event.EventInquiryPulsaData
import id.co.bri.brimo.ui.fragments.NewSkinBaseFragment
import id.co.bri.brimo.util.RxBus
import id.co.bri.brimo.util.textWithStrikethrough
import io.reactivex.android.schedulers.AndroidSchedulers

class DataReskinFragment(
    private val nominalList: List<DataListRevamp>,
    private val urlBanner: String,
    private val onItemClick: (DataListRevamp) -> Unit
): NewSkinBaseFragment() {

    private var _binding: FragmentDataReskinBinding? = null
    protected val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDataReskinBinding.inflate(inflater, container, false)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val myAdapter = DataAdapter(nominalList) { selectedNominal ->
            onItemClick.invoke(selectedNominal)
        }

        binding.recyclerView.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = myAdapter
        }

        RxBus.listen(EventInquiryPulsaData::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                if(event.type == "data") {
                    myAdapter.clearItemClick()
                }
            }
    }
}

class DataAdapter(
    private val items: List<DataListRevamp>,
    private val onItemClick: (DataListRevamp) -> Unit
) : RecyclerView.Adapter<DataAdapter.ViewHolder>() {

    private var selectedPosition = RecyclerView.NO_POSITION

    inner class ViewHolder(val binding: ItemPaketDataReskinBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener {
                val previousPosition = selectedPosition
                selectedPosition = adapterPosition
                notifyItemChanged(previousPosition)
                notifyItemChanged(selectedPosition)
                onItemClick(items[adapterPosition])
            }
        }

        fun bind(data: DataListRevamp, isSelected: Boolean) {
            binding.textNominal.text = data.amountString
            binding.root.setBackgroundResource(
                if (isSelected) R.drawable.bg_selected_reskin else R.drawable.bg_unselected_reskin
            )
            binding.textNominal.setTextColor(
                ContextCompat.getColor(binding.root.context,
                    if (isSelected) R.color.white else R.color.black
                )
            )
            binding.textTitle.setTextColor(
                ContextCompat.getColor(binding.root.context,
                    if (isSelected) R.color.white else R.color.black
                )
            )

            binding.textTitle.text = data.title
            binding.tvStrip.textWithStrikethrough(data.stripAmountString)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemPaketDataReskinBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val isSelected = position == selectedPosition
        holder.bind(items[position], isSelected)
    }

    fun clearItemClick() {
        selectedPosition = RecyclerView.NO_POSITION
        notifyDataSetChanged()
    }
}