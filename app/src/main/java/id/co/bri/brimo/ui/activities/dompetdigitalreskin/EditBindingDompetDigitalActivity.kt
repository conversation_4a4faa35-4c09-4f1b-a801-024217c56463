package id.co.bri.brimo.ui.activities.dompetdigitalreskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dompetdigital.EditBindingEwalletAdapter
import id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin.IEditBindingDompetDigitalPresenter
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.IEditBindingDompetDigitalView
import id.co.bri.brimo.databinding.ActivityEditBindingDompetDigitalBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse
import id.co.bri.brimo.models.apimodel.response.EwalletUnbindingResponse
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.HapusWalletBindingConfirmationBottomSheet
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import javax.inject.Inject

class EditBindingDompetDigitalActivity : NewSkinBaseActivity(),
    IEditBindingDompetDigitalView,
    EditBindingEwalletAdapter.OnUnbindClickListener {

    private lateinit var binding: ActivityEditBindingDompetDigitalBinding
    private lateinit var adapter: EditBindingEwalletAdapter
    private var connectedWallets: ArrayList<EwalletProductResponse> = ArrayList()
    private var selectedWalletForUnbind: EwalletProductResponse? = null
    private var cellPhoneNumber: String? = null

    @Inject
    lateinit var presenter: IEditBindingDompetDigitalPresenter<IEditBindingDompetDigitalView>

    companion object {
        const val TAG = "EditBindingEwalletActivity"
        const val EXTRA_CONNECTED_WALLETS = "extra_connected_wallets"
        private const val EXTRA_CELLPHONE_NUMBER = "extra_cellphone_number"

        // Result codes for different actions
        const val RESULT_CONNECT_NEW_WALLET = 200
        const val RESULT_WALLET_UNBOUND_LIST_EMPTY = 201
        const val RESULT_WALLET_UNBOUND_LIST_NOT_EMPTY = 202
        const val RESULT_WALLET_CANCELED = 203

        @JvmStatic
        fun launchIntent(caller: Activity, connectedWallets: ArrayList<EwalletProductResponse>, mCellPhoneNumber: String?) {
            val intent = Intent(caller, EditBindingDompetDigitalActivity::class.java)
            intent.putExtra(EXTRA_CONNECTED_WALLETS, Gson().toJson(connectedWallets))
            intent.putExtra(EXTRA_CELLPHONE_NUMBER, mCellPhoneNumber ?: "")
            caller.startActivityForResult(intent, Constant.REQ_EDIT_BIND_WALLET)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEditBindingDompetDigitalBinding.inflate(layoutInflater)
        setContentView(binding.root)

        cellPhoneNumber = intent.getStringExtra(EXTRA_CELLPHONE_NUMBER) ?: ""

        parseDataIntent()
        injectDependency()
        setupView()
        setupRecyclerView()
        buttonClick()
    }

    private fun parseDataIntent() {
        intent.extras?.let { extras ->
            val connectedWalletsJson = extras.getString(EXTRA_CONNECTED_WALLETS)
            if (!connectedWalletsJson.isNullOrEmpty()) {
                val type = object : TypeToken<ArrayList<EwalletProductResponse>>() {}.type
                connectedWallets = Gson().fromJson(connectedWalletsJson, type) ?: ArrayList()
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlEwalletUnBinding(GeneralHelper.getString(R.string.url_ewallet_unbinding))
    }

    private fun setupView() {
        GeneralHelperNewSkin.setToolbar(
            this,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.title_edit_e_wallet)
        )
    }

    private fun setupRecyclerView() {
        binding.rvDaftarFavorit.layoutManager = LinearLayoutManager(this)
        adapter = EditBindingEwalletAdapter(this, connectedWallets, cellPhoneNumber ?: "", this)
        binding.rvDaftarFavorit.adapter = adapter
    }

    private fun buttonClick() {
        binding.btnSubmit.setOnClickListener {
            // Set result to indicate user wants to connect new wallet
            setResult(RESULT_CONNECT_NEW_WALLET)
            finish()
        }
    }

    // EditBindingEwalletAdapter.OnUnbindClickListener implementation
    override fun onUnbindClick(wallet: EwalletProductResponse) {
        selectedWalletForUnbind = wallet
        showUnbindConfirmationBottomSheet(wallet)
    }

    private fun showUnbindConfirmationBottomSheet(wallet: EwalletProductResponse) {
        val titleTxt = String.format(
            GeneralHelper.getString(R.string.txt_unbind_e_wallet),
            wallet.title
        )

        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            fragmentManager = supportFragmentManager,
            imgDrawable = R.drawable.ic_warning_illustration,
            imgName = "ic_warning_illustration",
            titleTxt = titleTxt,
            subTitleTxt = GeneralHelper.getString(R.string.desc_unbind_e_wallet),
            btnFirstFunction = {
                // Close the dialog
            },
            btnSecondFunction = {
                presenter.setUnbindingEwallet(wallet.type)
            },
            isClickableOutside = true,
            withBgSecondBtn = false,
            firstBtnTxt = GeneralHelper.getString(R.string.batal),
            secondBtnTxt = GeneralHelper.getString(R.string.delete),
            showCloseButton = true,
            showPill = true
        )
    }

    // IEditBindingEwalletView implementation
    override fun onSuccessUnbindingeWallet(ewalletUnbindingResponse: EwalletUnbindingResponse?) {
        // Ensure progress is hidden - add safety call even though presenter should handle it
        hideProgress()

        // Remove the unbound wallet from the list
        selectedWalletForUnbind?.let { wallet ->
            connectedWallets.remove(wallet)
            adapter.notifyDataSetChanged()
        }

        // Check if connected wallets list is empty after unbinding
        if (connectedWallets.isEmpty()) {
            // No more connected wallets, return to FormDompetDigitalReskinActivity and refetch
            setResult(RESULT_WALLET_UNBOUND_LIST_EMPTY)
                finish()
        } else {
            // Still have connected wallets, just refresh the binding list
            setResult(RESULT_WALLET_UNBOUND_LIST_NOT_EMPTY)

            showSnackbarErrorMessage(
                "E-wallet berhasil dihapus",
                Constant.ALERT_CONFIRM,
                this,
                false
            )
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        setResult(RESULT_WALLET_CANCELED)
    }

    // IMvpView implementation
    override fun onException(message: String) {
        hideProgress()
        showSnackbarErrorMessage(
            message ?: "Terjadi kesalahan",
            Constant.ALERT_ERROR,
            this,
            false
        )
    }
}