package id.co.bri.brimo.ui.activities.pulsadata.reskin

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Rect
import android.net.Uri
import android.os.Bundle
import android.provider.ContactsContract
import android.text.Editable
import android.text.InputType
import android.text.Spannable
import android.text.SpannableString
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.MotionEvent
import android.view.View
import android.widget.EditText
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryAdapterNs
import id.co.bri.brimo.adapters.SavedAdapterNs
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataException
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataResult
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaErrorCode
import id.co.bri.brimo.data.api.observer.ResExceptionErr
import id.co.bri.brimo.databinding.ActivityFormPulsaDataReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.listrikrevamp.reskin.FavoriteEventAction
import id.co.bri.brimo.ui.fragments.UpdateSavedItemNsFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.HapusConfirmationBottomSheetFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.util.RxBus
import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import id.co.bri.brimo.util.custom_numpad.NumpadType
import id.co.bri.brimo.util.parseFromJson
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import javax.inject.Inject

class FormPulsaDataReskinActivity: NewSkinBaseActivity(), IPulsaDataReskinView, SavedAdapterNs.ClickItem, HistoryAdapterNs.ClickItem {
    private var _binding: ActivityFormPulsaDataReskinBinding? = null
    protected val binding get() = _binding!!

    val providerList: MutableList<ProviderItem> = mutableListOf()
    private var isProviderFound: Boolean = false
    private lateinit var currentProvider: ProviderItem

    private lateinit var dataFormPulsa: FormPulsaDataResponse

    // Custom hint
    private lateinit var customHint : SpannableString

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    lateinit var historyAdapter: HistoryAdapterNs
    lateinit var savedAdapter: SavedAdapterNs

    protected var historyResponses: ArrayList<HistoryResponse> = ArrayList<HistoryResponse>()
    protected var savedResponses: ArrayList<SavedResponse> = ArrayList<SavedResponse>()

    private lateinit var numpadHelper: CustomNumpadHelper

    // Current tab
    private var currentTab = TAB_FAVORIT

    private lateinit var skeleton: SkeletonScreen

    private var useApi = true

    var activityTextListener: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            beforeText()
        }

        override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            changeText(charSequence, i, i1, i2)
        }

        override fun afterTextChanged(editable: Editable) {
            afterText(editable)
            updateHintBasedOnState()

            binding.bivNoPelanggan.removeAllEndIcons()
            if (editable.toString().isNotEmpty()) {
                binding.bivNoPelanggan.addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                    binding.bivNoPelanggan.clearText()
                }
            }
            binding.bivNoPelanggan.addEndIcon(R.drawable.ic_contact_reskin) {
                checkContactPermission()
            }
        }
    }

    private val compositeDisposable = CompositeDisposable()

    private fun updateHintBasedOnState() {
        val isExpanded = binding.bivNoPelanggan.hasFocus() || binding.bivNoPelanggan.getText().isNotEmpty()
        if (isExpanded) {
            binding.bivNoPelanggan.setHint(getString(R.string.hint_label_no_hp))
        } else {
            binding.bivNoPelanggan.setHint(customHint)
        }
    }

    companion object {
        const val TAG = "FormPulsaDataReskinActivity"

        const val TAB_FAVORIT = 0
        const val TAB_RIWAYAT = 1

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean) {
            isFromFastMenu = fromFastMenu
            caller.apply {
                startActivityForResult(
                    Intent(
                    this,
                    FormPulsaDataReskinActivity::class.java
                ), Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityFormPulsaDataReskinBinding.inflate(layoutInflater)
        skeleton = Skeleton.bind(binding.bslContent.binding.llContent)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_form_pulsa_reskin)
            .show()
//        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()

        compositeDisposable.add(
            RxBus.listen(FavoriteEventAction::class.java)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe { action ->
                    showSnackbar(action.message, ALERT_CONFIRM)
                }
        )
    }

    private fun onBindView() {
        listOf(binding.llContentMain, binding.btnSubmit).forEach { it.isVisible = !isFromFastMenu }

        binding.bivNoPelanggan.attachNumpad(this, NumpadType.PHONE, { pin ->

        }, onFocusChanged = { hasFocus ->
            updateHintBasedOnState()
        }, onAttached = { numpad ->
            numpadHelper = numpad
        })
        binding.bslContent.apply {
            setTextToolbar(this@FormPulsaDataReskinActivity, "Pulsa & Paket Data")
        }
        binding.bivNoPelanggan.apply {
            setDistancePrefix(resources.getDimensionPixelSize(R.dimen.size_8dp))
            setInputType(InputType.TYPE_CLASS_NUMBER)
            setHint(customHint)
            addEndIcon(R.drawable.ic_contact_reskin) {
                checkContactPermission()
            }
            addTextChangedListener(activityTextListener)
        }
        binding.btnSubmit.setOnClickListener {
            if(!isProviderFound) {
                binding.bivNoPelanggan.setError("Nomor tidak ditemukan. Coba cek lagi nomor kamu.")
            } else {
                InquiryPulsaDataReskinActivity.launchIntent(this, isFromFastMenu, ReqInquiry(
                    binding.bivNoPelanggan.getText(),
                    currentProvider, dataFormPulsa.referenceNumber,
                    if(isFromFastMenu) dataFormPulsa.accountModel else mutableListOf()
                ),savedResponses.toMutableList())
            }
        }

        binding.searchviewBriva.setOnClickListener {
            SearchSavedHistoryPulsaDataActivity.launchIntent(
                this,
                savedResponses,
                historyResponses,
                dataFormPulsa,
                isFromFastMenu,
            )
        }

        setupAdapters()
        setupTabFunctionality()

        bindWithMock()
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@FormPulsaDataReskinActivity
            if(useApi) executeRequest(
                url = if(isFromFastMenu) GeneralHelper.getString(R.string.url_form_pulsa_data_v5)
                else GeneralHelper.getString(R.string.url_form_pulsa_data_revamp),
                requestParam = if(isFromFastMenu) getFastMenuRequest() else null,
                responseType = FormPulsaDataResponse::class.java
            )
            start()
        }
    }

    private fun execForm() {
        presenter.executeRequest(
            url = if(isFromFastMenu) GeneralHelper.getString(R.string.url_form_pulsa_data_v5)
            else GeneralHelper.getString(R.string.url_form_pulsa_data_revamp),
            requestParam = if(isFromFastMenu) presenter.getFastMenuRequest() else null,
            responseType = FormPulsaDataResponse::class.java
        )
    }

    private fun execFavorite(url: String, savedId: Int, isSaved: FavoriteType) {
        presenter.executeRequest(
            url = url,
            requestParam = SavedListNs(
                savedId = savedId
            ),
            responseType = RestResponse::class.java,
            isSaved = isSaved
        )
    }

    private fun onBindIntentData() {
        customHint = customPrefixHint(
            GeneralHelper.getString(R.string.hint_prefix_62),
            GeneralHelper.getString(R.string.hint_label_no_hp)
        )
    }

    protected fun customPrefixHint(prefix: String, label: String): SpannableString {
        val colorPrefix = ContextCompat.getColor(this, R.color.text_disabled_default_ns)
        val colorLabel = ContextCompat.getColor(
            this, if (isFromFastMenu) R.color.text_disabled_default_ns else R.color.text_black_default_ns
        )

        return SpannableString("$prefix  $label").apply {
            setSpan(ForegroundColorSpan(colorPrefix), 0, prefix.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            setSpan(ForegroundColorSpan(colorLabel), prefix.length, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    override fun onSuccessAccountList(accountList: MutableList<AccountModel>, mainAccount: AccountModel) {

    }

    private fun validatePhoneNumberAndUpdateButton() {
        val minLength = 9
        val maxLength = 12
        val inputLength = binding.bivNoPelanggan.getText().length

        binding.bivNoPelanggan.apply {
            when {
                inputLength in 1 until minLength -> setError(
                    getString(R.string.phone_minimal_input, minLength + 1)
                )
                inputLength > maxLength -> setError(
                    getString(R.string.phone_maksimal_input, maxLength + 1)
                )
                else -> clearError()
            }
        }

        updateSubmitButtonState()
    }

    private fun updateSubmitButtonState() {
        val minLength = 9
        val maxLength = 12
        val inputLength = binding.bivNoPelanggan.getText().length

        val isPhoneNumberValid = inputLength in minLength..maxLength

        binding.btnSubmit.isEnabled = isPhoneNumberValid
    }

    override fun changeText(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
        super.changeText(charSequence, i, i1, i2)

        val input = charSequence.toString()
        val normalized = normalizePhoneNumber(input)

        validatePhoneNumberAndUpdateButton()

        // Hanya update jika input berubah setelah normalisasi
        if (input != normalized) {
            binding.bivNoPelanggan.removeTextChangedListener(activityTextListener)
            binding.bivNoPelanggan.setText(normalized)
            binding.bivNoPelanggan.setSelection(normalized.length)
            binding.bivNoPelanggan.addTextChangedListener(activityTextListener)

            binding.bivNoPelanggan.removeAllEndIcons()
            if (input.isNotEmpty()) {
                binding.bivNoPelanggan.addEndIcon(R.drawable.ic_clear_ns) {
                    binding.bivNoPelanggan.clearText()
                }
            }
            binding.bivNoPelanggan.addEndIcon(R.drawable.ic_contact_reskin) {
                checkContactPermission()
            }
        }

        when {
            (charSequence.length >= 3 && charSequence.length < 5) -> {
                if (!isProviderFound) {
                    showingToForm("0$normalized")
                }
            }

            (charSequence.length == 5 || (charSequence.length >= 5 && !isProviderFound)) -> {
                showingToForm("0$normalized")
            }

            charSequence.length < 4 -> {
                binding.bivNoPelanggan.setStartIconWithUrl("")
            }
        }
    }

    private fun showingToForm(number: String) {
        findProviderByPrefix(number)?.let {
            currentProvider = it
            binding.bivNoPelanggan.setStartIconWithUrl(it.iconPath)
            isProviderFound = true
            binding.bivNoPelanggan.clearError()
        }
    }

    private fun bindWithMock() {
        if(!useApi) {
            skeleton.hide()
            hideProgress()
            val formPulsaDataResponse: FormPulsaDataResponse = parseFromJson(this, "data/mockjson/pulsa/formpulsa.json")

            historyResponses.apply {
                clear()
                addAll(formPulsaDataResponse.history)
            }
            savedResponses.apply {
                clear()
                addAll(formPulsaDataResponse.saved)
            }

            dataFormPulsa = formPulsaDataResponse
            providerList.addAll(dataFormPulsa.provider)

            savedAdapter.notifyDataSetChanged()
            historyAdapter.notifyDataSetChanged()

            updateEmptyStates()
        }
    }

    fun normalizePhoneNumber(input: String): String {
        return input.replace(Regex("^((\\+62)|62|0)"), "")
    }

    protected fun checkContactPermission() {
        if (ContextCompat.checkSelfPermission(
                this, Manifest.permission.READ_CONTACTS
            ) == PackageManager.PERMISSION_DENIED
        ) {
            ActivityCompat.requestPermissions(
                this, arrayOf(Manifest.permission.READ_CONTACTS), Constant.REQ_READ_CONTACT
            )
        } else {
            pickContact()
        }
    }

    private fun pickContact() {
        val intent = Intent(Intent.ACTION_PICK, ContactsContract.Contacts.CONTENT_URI)
        intent.type = ContactsContract.CommonDataKinds.Phone.CONTENT_TYPE
        startActivityForResult(intent, Constant.REQ_READ_CONTACT)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode != Constant.REQ_READ_CONTACT) return

        if (grantResults.firstOrNull() == PackageManager.PERMISSION_GRANTED) {
            pickContact()
        } else {
            GeneralHelper.showToast(this, GeneralHelper.getString(R.string.access_not_granted))
        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode != Constant.REQ_READ_CONTACT || resultCode != RESULT_OK) return

        val contactUri = data?.data ?: return
        val number = getContactNumber(contactUri) ?: run {
            GeneralHelper.showToast(this, GeneralHelper.getString(R.string.contact_not_found))
            return
        }

        onContactPicked(number)
    }

    @SuppressLint("Range")
    private fun getContactNumber(uri: Uri): String? {
        val projection = arrayOf(ContactsContract.CommonDataKinds.Phone.NUMBER)
        return contentResolver.query(uri, projection, null, null, null)?.use { cursor ->
            if (!cursor.moveToFirst()) return null
            cursor.getString(cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER))
                ?.replace("[^\\d]".toRegex(), "")
                ?.let { if (it.startsWith("0")) it else "0${it.drop(2)}" }
        }
    }

    protected open fun onContactPicked(number: String) {
        binding.bivNoPelanggan.setText(number.removeRange(0, 1))
        binding.btnSubmit.setEnabled(true)
        findProviderByPrefix(number)?.let {
            currentProvider = it
        }
    }

    private fun setupTabFunctionality() {
        binding.tabFavorit.setOnClickListener { switchToFavoritTab() }
        binding.tabRiwayat.setOnClickListener { switchToRiwayatTab() }

        // Default tab tanpa reset pencarian
        switchToFavoritTab()
    }

    private fun setupAdapters() {
        // Saved adapter
        initiateSavedAdapter()

        // History adapter
        initiateHistoryAdapter()
    }

    fun initiateHistoryAdapter() {
        binding.rvRiwayat.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        historyAdapter =
            HistoryAdapterNs(
                this,
                historyResponses,
                this,
                0,
                isFromFastMenu,
                "pulsa",
            )
        binding.rvRiwayat.adapter = historyAdapter
    }

    fun initiateSavedAdapter() {
        binding.rvDaftarFavorit.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        savedAdapter =
            SavedAdapterNs(this, savedResponses, this, 0, isFromFastMenu)
        binding.rvDaftarFavorit.adapter = savedAdapter
    }

    private fun switchToFavoritTab() {
        currentTab = TAB_FAVORIT
        updateTabAppearance()
        showFavoritContent()
    }

    private fun switchToRiwayatTab() {
        currentTab = TAB_RIWAYAT
        updateTabAppearance()
        showRiwayatContent()
    }

    private fun setTabActive(tab: TextView, isActive: Boolean) {
        val colorRes = if (isActive) R.color.text_brand_primary_ns else R.color.black
        val bgRes = if (isActive) R.drawable.rounded_button_soft_ns else R.drawable.rounded_button_neutral_ns
        tab.setTextColor(ContextCompat.getColor(this, colorRes))
        tab.setBackgroundResource(bgRes)
    }

    private fun updateTabAppearance() {
        setTabActive(binding.tabFavorit, currentTab == TAB_FAVORIT)
        setTabActive(binding.tabRiwayat, currentTab == TAB_RIWAYAT)
    }

    private fun showFavoritContent() {
        binding.contentFavorit.visibility = View.VISIBLE
        binding.contentRiwayat.visibility = View.GONE
    }

    private fun showRiwayatContent() {
        binding.contentFavorit.visibility = View.GONE
        binding.contentRiwayat.visibility = View.VISIBLE
    }

    private fun toggleEmptyState(isEmpty: Boolean, recycler: View, emptyView: View) {
        recycler.visibility = if (isEmpty) View.GONE else View.VISIBLE
        emptyView.visibility = if (isEmpty) View.VISIBLE else View.GONE
    }

    private fun updateEmptyStates() {
        toggleEmptyState(savedResponses.isEmpty(), binding.rvDaftarFavorit, binding.llNoDataSaved)
        toggleEmptyState(historyResponses.isEmpty(), binding.rvRiwayat, binding.llNoHistory)
    }

    override fun onClickSavedItem(savedResponse: SavedResponse?) {
        val number = savedResponse?.subtitle!!.replace(Regex("[^0-9]"), "")

        findProviderByPrefix(number)?.let {
            InquiryPulsaDataReskinActivity.launchIntent(this, isFromFastMenu, ReqInquiry(
                normalizePhoneNumber(number),
                it, dataFormPulsa.referenceNumber,
                if(isFromFastMenu) dataFormPulsa.accountModel else mutableListOf()
            ),savedResponses.toMutableList())
        }
    }

    override fun onClickUpdateItem(
        savedResponse: SavedResponse?,
        position: Int
    ) {
        val updateSavedItemFragment = UpdateSavedItemNsFragment(savedResponse, {savedResponseItem, type, position ->
            savedResponseItem?.let { item ->
                val savedId = item.value.split("|")[0].toInt()

                when (type) {
                    Constant.EditOptionNs.EDIT -> {
                        val number = item.subtitle.orEmpty()
                        val provider = findProviderByPrefix(number) ?: return@let

                        AddSavedPulsaDataActivity.launchIntent(
                            this,
                            ReqInquiry(
                                normalizePhoneNumber(number),
                                provider,
                                dataFormPulsa.referenceNumber,
                                mutableListOf(),
                                item.title,
                                item.value.split("|")[0]
                            ),
                            isFromFastMenu
                        )
                    }

                    Constant.EditOptionNs.FAV,
                    Constant.EditOptionNs.NON_FAV,
                    Constant.EditOptionNs.HAPUS -> {
                        val (url, favType, withConfirmation) = when (type) {
                            Constant.EditOptionNs.FAV -> Triple(
                                GeneralHelper.getString(R.string.url_favorite_pulsa),
                                FavoriteType.favorite,
                                false
                            )
                            Constant.EditOptionNs.NON_FAV -> Triple(
                                GeneralHelper.getString(R.string.url_unfavorite_pulsa),
                                FavoriteType.unfavorite,
                                false
                            )
                            Constant.EditOptionNs.HAPUS -> Triple(
                                GeneralHelper.getString(R.string.url_delete_pulsa),
                                FavoriteType.removeFavorite,
                                true
                            )
                            else -> return@let
                        }

                        if (withConfirmation) {
                            HapusConfirmationBottomSheetFragment.newInstance(
                                savedResponseItem = item,
                                onConfirm = {
                                    showProgress()
                                    execFavorite(url, savedId, favType)
                                },
                                onCancel = {}
                            ).show(supportFragmentManager, "HapusConfirmationBottomSheet")
                        } else {
                            showProgress()
                            execFavorite(url, savedId, favType)
                        }
                    }
                }
            }

        }, position)
        updateSavedItemFragment.show(supportFragmentManager, "")
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse?) {
        historyResponse?.let { history ->
            val number = history.subtitle.replace(Regex("[^0-9]"), "")
            val (type, nominal) = history.value.split("|")

            findProviderByPrefix(number)?.let { provider ->
                InquiryPulsaDataReskinActivity.launchIntent(
                    this,
                    isFromFastMenu,
                    ReqInquiry(
                        normalizePhoneNumber(number),
                        provider,
                        dataFormPulsa.referenceNumber,
                        if (isFromFastMenu) dataFormPulsa.accountModel else mutableListOf()
                    ),
                    savedResponses.toMutableList(),
                    nominal = nominal,
                    type = type
                )
            }
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val view = currentFocus
        if (ev?.action == MotionEvent.ACTION_DOWN && view is EditText) {
            val outRect = Rect().apply { view.getGlobalVisibleRect(this) }
            if (!outRect.contains(ev.rawX.toInt(), ev.rawY.toInt()) &&
                !numpadHelper.isTouchInsideNumpad(ev)
            ) {
                view.clearFocus()
                numpadHelper.hideKeyboard()
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    fun findProviderByPrefix(phone: String): ProviderItem? {
        val cleanedPhone = phone.replace(Regex("[^0-9]"), "")
        val prefix4 = cleanedPhone.take(4)
        val prefix5 = cleanedPhone.take(5)

        return providerList.firstOrNull { provider ->
            GeneralHelper.isContains(provider.prefix, prefix5) ||
                    GeneralHelper.isContains(provider.prefix, prefix4)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isFromFastMenu = false
        compositeDisposable.dispose()
    }

    override fun onSuccess(res: PulsaDataResult) {
        when (res) {
            is PulsaDataResult.Form -> {
                skeleton.hide()
                hideProgress()
                val formPulsaDataResponse = res.data

                historyResponses.apply {
                    clear()
                    addAll(formPulsaDataResponse.history)
                }
                savedResponses.apply {
                    clear()
                    addAll(formPulsaDataResponse.saved)
                }

                dataFormPulsa = formPulsaDataResponse
                providerList.addAll(dataFormPulsa.provider)

                savedAdapter.notifyDataSetChanged()
                historyAdapter.notifyDataSetChanged()

                updateEmptyStates()
            }
            is PulsaDataResult.Favorite -> {
                val type = res.data

                execForm()

                showSnackbar(when (type) {
                    FavoriteType.favorite -> "Daftar berhasil di Pin."
                    FavoriteType.removeFavorite -> "Daftar Favorit berhasil dihapus."
                    FavoriteType.unfavorite -> "Daftar Favorit berhasil diunpin."
                    else -> ""
                }, ALERT_CONFIRM)
            }
            else -> Unit
        }
    }

    override fun onExceptionReskin(exception: PulsaDataException) {
        when (exception) {
            is PulsaDataException.KnownError -> handlingError(exception.body)
            is PulsaDataException.UnknownError -> {
                OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                    supportFragmentManager,
                    R.drawable.ic_sad_illustration,
                    "ic_sad_illustration",
                    "Terjadi Kesalahan",
                    "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
                    btnFirstFunction = {

                    },
                    btnSecondFunction = {

                    },
                    false,
                    firstBtnTxt = "Coba Lagi",
                    secondBtnTxt = "Tutup",
                    false,
                    showCloseButton = true,
                    showPill = true
                )
            }
        }
    }

    private fun handlingError(data: ResExceptionErr) {
        when (data.code) {
            PulsaErrorCode.EXCEPTION_93.code -> showGeneralErrorDialog()

            PulsaErrorCode.EXCEPTION_12.code -> {
                if(data.desc.contains("PIN")) {
                    showSnackbar(data.desc, ALERT_ERROR)
                } else {
                    showGeneralErrorDialog { execForm() }
                }
            }

            PulsaErrorCode.EXCEPTION_01.code -> showSnackbar(data.desc, ALERT_ERROR)

            PulsaErrorCode.EXCEPTION_61.code -> OpenBottomSheetGeneralNewSkinFragment.showDialogInformationDismiss(
                fragmentManager = supportFragmentManager,
                imgPath = "",
                imgName = "ic_warning_illustration",
                titleTxt = GeneralHelper.getString(R.string.txt_limit_harian_title),
                subTitleTxt = GeneralHelper.getString(R.string.txt_limit_harian_desc),
                btnFirstFunction = { /* Dismiss */ },
                isClickableOutside = true,
                firstBtnTxt = GeneralHelper.getString(R.string.mengerti),
                showCloseButton = true
            )
        }
    }

    private fun showGeneralErrorDialog(
        title: String = "Terjadi Kesalahan",
        message: String = "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
        imgName: String = "ic_sad_illustration",
        onRetry: (() -> Unit)? = null
    ) {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_sad_illustration,
            imgName,
            title,
            message,
            btnFirstFunction = { onRetry?.invoke() },
            btnSecondFunction = { /* optional */ },
            false,
            firstBtnTxt = getString(R.string.retry),
            secondBtnTxt = getString(R.string.close),
            false,
            showCloseButton = true,
            showPill = true
        )
    }
}