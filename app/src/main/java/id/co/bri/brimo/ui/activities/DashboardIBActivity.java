package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.facebook.react.ReactInstanceManager;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.gson.Gson;

import org.json.JSONObject;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.BaseApp;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.dashboard.IDashboardActivityPresenter;
import id.co.bri.brimo.contract.IView.dashboard.IDashboardActivityView;
import id.co.bri.brimo.databinding.ActivityDashboardIbBinding;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.config.MenuConfig;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.biometric.BiometricCallback;
import id.co.bri.brimo.domain.helpers.biometric.BiometricUtility;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.OnboardingItemModel;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.request.splitbill.NotifikasiSplitBillRequest;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.CashbackResponse;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse;
import id.co.bri.brimo.models.apimodel.response.NotifikasiRequestEwallet;
import id.co.bri.brimo.models.apimodel.response.PromoResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.cc.BindingCcKkiResponse;
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ConfirmationLifestyleResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.DashboardLifestyleMenuResponse;
import id.co.bri.brimo.models.apimodel.response.transferrevamp.DetailAftResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.NotifikasiMokirimRequest;
import id.co.bri.brimo.models.daomodel.RateUs;
import id.co.bri.brimo.payment.app.PaymentActivity;
import id.co.bri.brimo.reactnative.BrimodSDK;
import id.co.bri.brimo.security.MyCryptStatic;
import id.co.bri.brimo.ui.activities.base.BaseDashboardActivity;
import id.co.bri.brimo.ui.activities.cc_sof.BindingCcKkiActivity;
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.InquiryDompetDigitalRevampActivity;
import id.co.bri.brimo.ui.activities.lifestyle.DashboardLifestyleActivity;
import id.co.bri.brimo.ui.activities.lifestyle.KonfirmasiLifestyleActivity;
import id.co.bri.brimo.ui.activities.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiActivity;
import id.co.bri.brimo.ui.activities.listrikrevamp.InquiryListrikRevampActivity;
import id.co.bri.brimo.ui.activities.safetymode.KonfirmasiSafetyModeActivity;
import id.co.bri.brimo.ui.activities.ssc.ListComplainActivity;
import id.co.bri.brimo.ui.activities.transactionlimitinformation.TransactionLimitInformationActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.DetailAftActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.MutationFragment;
import id.co.bri.brimo.ui.fragments.dashboardhistory.HistoryDashboardFragment;
import id.co.bri.brimo.ui.fragments.RateUsBottomFragment;
import id.co.bri.brimo.ui.fragments.ReactNativeFragment;
import id.co.bri.brimo.ui.fragments.TransferAliasFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment;
import id.co.bri.brimo.ui.fragments.dashboardrevamp.DashboardRevampFragment;
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment;

import id.co.bri.brimo.ui.fragments.profilerevamp.ProfileRevampFragment;
import id.co.bri.brimo.util.extension.DebugType;
import id.co.bri.brimo.util.ota.DownloadManager;
import id.co.bri.brimo.util.ota.OTADownloadProgressDialog;

import java.util.Objects;

public class DashboardIBActivity extends BaseDashboardActivity implements
        IDashboardActivityView, DialogExitCustom.DialogDefaultListener,
        BiometricCallback {

    private static final String TAG_FRAGMENT_ONE = "fragment_one";
    private static final String TAG_FRAGMENT_TWO = "fragment_two";
    private static final String TAG_FRAGMENT_FOUR = "fragment_four";
    private static final String TAG_FRAGMENT_FIVE = "fragment_five";

    private boolean backPressedOnce = false;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private final Runnable resetBackFlag = () -> backPressedOnce = false;

    public OnWindowFocusChangeListener focusChangeListener;

    @Inject
    IDashboardActivityPresenter<IDashboardActivityView> presenter;

    private ActivityDashboardIbBinding binding;


    static BottomNavigationView layoutNav;

//    private DashboardRevampFragment dashboardIBFragmentRevamp;
    private ReactNativeFragment dashboardIBFragmentRevamp;

    private ProfileRevampFragment fragmentProfileRevamp;

//    private MutationFragment mutationFragment;
    private ReactNativeFragment mutationFragment;

    private HistoryDashboardFragment historyDashboardFragment;

    private TransferAliasFragment transferFragment;

    private boolean isOnboardingShowing = true;
    private boolean isTransactionSuccess = false;
    protected static boolean mIsFromScan = false;
    protected static boolean mFromFastMenuReminder = false;

    protected static boolean mIsShowDialog = false;
    protected static String mDrawableDialog, mTitleDialog, mDetailDialog;

    public static final BubbleShowCaseSequence s = new BubbleShowCaseSequence();
    public static final BubbleShowCaseSequence r = new BubbleShowCaseSequence();
    protected static String errorMessage = null;
    static String successMessage = null;

    //data Notifikasi
    private Bundle extras = null;
    private String notifikasiString = null;
    private NotifikasiModel notifikasiModel = null;
    private String refNumber, codeLink, typeFlagLimit = "";

    private String depositProcedure = "";
    private String additionalPayload = null, additionalPayloadString = "";
    public static final String TAG_SWITCH_PROFILE = "profile";

    private String TAG_PROFILE = "ProfileRevampFragment";

    // OTA Download fields
    private DownloadManager downloadManager;
    private OTADownloadProgressDialog progressDialog;
    private boolean isDownloading = false;

    public String getTagProfile() {
        return TAG_PROFILE;
    }

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, DashboardIBActivity.class);
        caller.startActivity(intent);
        caller.finish();
    }

    public static void launchIntentError(Activity caller, String message) {
        Intent intent = new Intent(caller, DashboardIBActivity.class);
        errorMessage = message;
        caller.startActivity(intent);
        caller.finish();

    }

    public static void launchIntentSuccess(Activity caller, String message, boolean removeStackActivity) {
        Intent intent = new Intent(caller, DashboardIBActivity.class);
        if (removeStackActivity)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        successMessage = message;
        caller.startActivity(intent);
        caller.finish();

    }

    public static void launchIntentEditUsername(Activity caller, String sEdit) {
        Intent intent = new Intent(caller, DashboardIBActivity.class);
        successMessage = sEdit;
        caller.startActivity(intent);
        caller.finish();
    }

    public static void launchIntentSearchMenu(Activity caller) {
        Intent intent = new Intent(caller, DashboardIBActivity.class);
        intent.putExtra(TAG_SWITCH_PROFILE, true);
        caller.startActivity(intent);
        caller.finish();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        injectDependency();
        binding = ActivityDashboardIbBinding.inflate(getLayoutInflater());

        layoutNav = findViewById(R.id.navigation);

        layoutNav.setItemIconTintList(null);

        applyBottomNavTextStyle(layoutNav);

        //update bar head dengan warna putih
        setStatusColorAndStatusBar(R.color.toolbar_blue, View.SYSTEM_UI_FLAG_VISIBLE);

        //jika ada deskripsi error dari activity sebelumnya
        if (errorMessage != null) {
            onException(errorMessage);
            errorMessage = null;
        } else if (successMessage != null) {
            infoSuccessDashboardIbFragment(successMessage);
            successMessage = null;
        }

        if (getIntent().getExtras() != null &&
                !getIntent().hasExtra("depositProcedure")) {
            parseDataNotifDetailInbox(getIntent());
        }

        if (Boolean.TRUE.equals(mIsShowDialog)) {
            GeneralHelper.showDialogIlustration(this, mDrawableDialog, mTitleDialog, mDetailDialog);
            mIsShowDialog = false;
        }

        if (getIntent().hasExtra("depositProcedure"))
            depositProcedure = getIntent().getStringExtra("depositProcedure");

        binding.ivScan.setOnClickListener(v -> QrMPMActivity.launchIntent(this, false, false));

        BiometricUtility.INSTANCE.setBiometricType(this);

        if (getIntent().hasExtra(TAG_SWITCH_PROFILE))
            if (getIntent().getBooleanExtra(TAG_SWITCH_PROFILE, false)) {
                switchToProfile();
            }

        // Initialize OTA Download Manager
        downloadManager = new DownloadManager(this);
        setupOTADownload();
        // Trigger OTA download untuk React Native fragment
        triggerOTADownload();

        // Register custom back handler using OnBackPressedDispatcher
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                handleCustomBack();
            }
        });
    }

    private void parseDataNotifDetailInbox(Intent intent) {
        if (intent == null) return;

        extras = intent.getExtras();
        if (extras == null) return;

        notifikasiString = extras.getString(Constant.TAG_NOTIF);
        codeLink = extras.getString(Constant.LINK_CODE);
        typeFlagLimit = extras.getString(Constant.TYPE_FLAG);

        if (notifikasiString != null && !notifikasiString.isEmpty()) {
            try {
                Gson gson = new Gson();
                notifikasiModel = gson.fromJson(notifikasiString, NotifikasiModel.class);
                if (notifikasiModel.getAdditional() != null && !notifikasiModel.getAdditional().isEmpty()) {
                    additionalPayload = notifikasiModel.getAdditional();
                    JSONObject jsonObject = new JSONObject(additionalPayload);
                    additionalPayloadString = jsonObject.toString();
                }

                handleNotificationType(gson);
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e("TAG", "parseDataNotif: ", e);
            }
        } else if (codeLink != null && !codeLink.isEmpty()) {
            handleCodeLink();
        } else if (typeFlagLimit != null && !typeFlagLimit.isEmpty()) {
            handleGoToLimitInformation();
        }
    }

    private void handleGoToLimitInformation() {
        Intent newIntent = new Intent(this, TransactionLimitInformationActivity.class);
        newIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        startActivity(newIntent);
    }

    private void handleNotificationType(Gson gson) {
        if (notifikasiModel == null) return;

        switch (notifikasiModel.getType()) {
            case Constant.BlastType.TRX_PURCHASE:
            case Constant.BlastType.TRX_PAYMENT:
            case Constant.BlastType.TRX_TRANSFER:
            case Constant.CARDLESS_BLAST_TYPE:
                handleTransactionNotification(gson);
                break;
            case Constant.BlastType.REMINDER:
                handleReminderNotification(gson);
                break;
            case Constant.BlastType.RECOMENDATION:
                handleRecommendationNotification();
                break;
            case Constant.BlastType.TARTUN_NDS:
                KonfirmasiTartunNdsActivity.launchIntent(this, notifikasiModel, false);
                break;
            case Constant.BlastType.SAFETY_MODE:
                KonfirmasiSafetyModeActivity.Companion.launchIntent(this, notifikasiModel, false);
                break;
            case Constant.EXPENSE_TRANSACTION_PFM:
                callbackToPFM(notifikasiString, MenuConfig.PFMMenuFragment.PFM_OUTCOME);
            case Constant.INCOME_TRANSACTION_PFM:
                callbackToPFM(notifikasiString, MenuConfig.PFMMenuFragment.PFM_INCOME);
                break;
            case Constant.BlastType.CASHBACK_BLAST_TYPE:
                CashbackResponse cashbackResponse = gson.fromJson(additionalPayloadString, CashbackResponse.class);
                DetailPromoCashbackActivity.launchIntent(this, cashbackResponse, false, "", DetailPromoCashbackActivity.PageType.APPLY_CASHBACK, false);
            case Constant.BlastType.DIRECT_FEATURE:
                CashbackResponse response = gson.fromJson(additionalPayloadString, CashbackResponse.class);
                if (Integer.parseInt(response.getFeatureId()) == MenuConfig.MenuId.MENU_TRAVEL) {
                    presenter.getDataDashboardLifestyleMenu(GeneralHelper.getString(R.string.url_dashboard_lifestyle_menu));
                } else {
                    MenuConfig.onChangeMenu(Integer.parseInt(response.getFeatureId()), this, null);
                }
                break;
            case Constant.NOTIF_KK_GPN_BLAST_TYPE:
            case Constant.NOTIF_BINDING_GPN_BLAST_TYPE:
            case Constant.NOTIF_BINDING_TRX_GPN_BLAST_TYPE:
                handleBindingGPN();
                break;
            case Constant.BlastType.AFT_NOTIF_SCHEDULER_BLAST_TYPE:
                handleAftNotifSchedulerBlast(notifikasiModel);
                break;
            case Constant.BlastType.DIRECT_SPECIFIC_FEATURE:
                try {
                    JSONObject jsonObject = new JSONObject(additionalPayloadString);
                    String menuName = jsonObject.getString("menu_name");
                    handleToSpecificFeature(menuName);
                } catch (Exception e) {
                    GeneralHelper.debugMessage(e, TAG_FRAGMENT_ONE, DebugType.DEBUG);
                }
                break;
            case Constant.BlastType.CIA_NOTIF_BLAST_TYPE:
                ListComplainActivity.launchIntentFromNotif(notifikasiModel, Constant.CIAType.TYPE_COMPLAINT_IN_APPS_GENERAL, this);
                break;
            default:
                break;
        }
    }

    private void handleBindingGPN() {
        presenter.setInquiryUrl(MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlConfirmationRevamp()));
        presenter.getDataInquiry(notifikasiModel.getRequestContent(),notifikasiModel.getTypeInquiry(),MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlConfirmationRevamp()),MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlPaymentRevamp()), GeneralHelper.getString(R.string.txt_konfirmasi), notifikasiModel.getTypeInquiryRevamp());
    }

    private void handleToSpecificFeature(String menuName) {
        MenuConfig.navigationToSpecificFeature(menuName, this);
    }

    private void handleAftNotifSchedulerBlast(NotifikasiModel notifikasiModel) {
        if (notifikasiModel.getTypeNotif() == null) return;
        switch (notifikasiModel.getTypeNotif()) {
            case Constant.AFTNotifSchedulerBlastTypeInquiry.aftFailed:
                presenter.getDataNotif(notifikasiModel.getRequestContent(), MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlNotif()), notifikasiModel.getTypeNotif());
                break;
            default:
                break;
        }
    }

    private void handleTransactionNotification(Gson gson) {
        if (notifikasiModel.getType().equalsIgnoreCase(Constant.BlastType.REMINDER)) {
            handleReminderNotification(gson);
        } else {
            refNumber = notifikasiModel.getRequestRefnum();
            updatePaymentDashboardFragment(refNumber, notifikasiString);
        }
    }

    private void handleReminderNotification(Gson gson) {
        if (notifikasiModel.getTypeInquiryRevamp() != null && !notifikasiModel.getTypeInquiryRevamp().isEmpty()) {
            presenter.setInquiryUrl(MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlInquiryRevamp()));
            String requestContent = notifikasiModel.getRequestContent();
            String urlConfirmationRevamp = MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlConfirmationRevamp());
            String urlPaymentRevamp = MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlPaymentRevamp());
            String titleInquiry = notifikasiModel.getTitileInquiry();
            String typeInquiryRevamp = notifikasiModel.getTypeInquiryRevamp();

            if (typeInquiryRevamp.equals(Constant.PaymentReminderTypeInquiry.revampEwallet)) {
                handleEwalletPaymentReminder(gson, requestContent, urlConfirmationRevamp, urlPaymentRevamp, titleInquiry, typeInquiryRevamp);
            } else if (typeInquiryRevamp.equals(Constant.PaymentReminderTypeInquiry.kirimBarang)) {
                handleMokirimInsufficient(gson, requestContent, urlConfirmationRevamp, urlPaymentRevamp);
            } else {
                presenter.getDataInquiry(requestContent, notifikasiModel.getTypeInquiry(), urlConfirmationRevamp, urlPaymentRevamp, titleInquiry, typeInquiryRevamp);
            }
        } else {
            presenter.setInquiryUrl(MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlInquiry()));
            presenter.getDataInquiry(notifikasiModel.getRequestContent(), notifikasiModel.getTypeInquiry(), MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlConfirmation()), MyCryptStatic.encryptAsBase64(notifikasiModel.getUrlPayment()), notifikasiModel.getTitileInquiry(), notifikasiModel.getTypeInquiryRevamp());
        }
    }

    private void handleEwalletPaymentReminder(Gson gson, String requestContent, String urlConfirmationRevamp, String urlPaymentRevamp, String titleInquiry, String typeInquiryRevamp) {
        NotifikasiRequestEwallet notifikasiRequestEwallet = gson.fromJson(requestContent, NotifikasiRequestEwallet.class);
        presenter.getDataInquiry(notifikasiRequestEwallet.ewalletRequest(), notifikasiModel.getTypeInquiry(), urlConfirmationRevamp, urlPaymentRevamp, titleInquiry, typeInquiryRevamp);
    }

    private void handleMokirimInsufficient(Gson gson, String requestContent, String urlConfirmationRevamp, String urlPaymentRevamp) {
        if (!notifikasiModel.isSplitbill().isEmpty() && Boolean.parseBoolean(notifikasiModel.isSplitbill())) {
            handleSplitBillReminder(gson, requestContent, urlConfirmationRevamp, urlPaymentRevamp);
        } else {
            NotifikasiMokirimRequest notifikasiMokirimRequest = gson.fromJson(requestContent, NotifikasiMokirimRequest.class);

            presenter.setInquiryUrl(urlConfirmationRevamp);
            presenter.getConfirmationMokirim(notifikasiMokirimRequest, urlPaymentRevamp);
        }
    }

    private void handleSplitBillReminder(Gson gson, String requestContent, String urlConfirmation, String urlPayment) {
        NotifikasiSplitBillRequest notifikasiSplitBillRequest = gson.fromJson(requestContent, NotifikasiSplitBillRequest.class);

        presenter.setInquiryUrl(urlConfirmation);
        presenter.getConfirmationSplitBill(notifikasiSplitBillRequest, urlPayment);
    }

    private void handleRecommendationNotification() {
        presenter.setPromoUrl(GeneralHelper.getString(R.string.url_detail_promo));
        presenter.getDataProduct(notifikasiModel.getPromoId(), notifikasiModel.getBlastId(), notifikasiModel.getTitleButton(), notifikasiModel);
    }

    private void handleCodeLink() {
        if (codeLink.equalsIgnoreCase(Constant.BRIGUNA_NAME)) {
            BrigunaDigitalActivity.launchIntent(this);
        } else {
            if (Integer.parseInt(codeLink) == MenuConfig.MenuId.MENU_TRAVEL) {
                presenter.getDataDashboardLifestyleMenu(GeneralHelper.getString(R.string.url_dashboard_lifestyle_menu));
            } else {
                MenuConfig.onChangeMenu(Integer.parseInt(codeLink), this, null);
            }
        }
    }

    private void callbackToPFM(String mNotificationString, int pfmMenu) {
        CatatanKeuanganActivity.launchIntent(this, false, false, mNotificationString, true, pfmMenu);
    }

    /**
     * Menampilkan snack bar Hijau untuk response suksesaaa
     *
     * @param sMessage text sukses yang akan ditampilkan
     */
    private void infoSuccessDashboardIbFragment(String sMessage) {
        Bundle props = new Bundle();
        props.putString("sMessage", sMessage);
        dashboardIBFragmentRevamp = ReactNativeFragment.newInstance("BrimoHomepage", props);
        getSupportFragmentManager().beginTransaction().replace(R.id.fragmentDashboardIB, dashboardIBFragmentRevamp, TAG_FRAGMENT_ONE).commit();
        bottomNavigationView.setSelectedItemId(R.id.navigation_home);
        setStatusColorAndStatusBar(R.color.white, View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
    }

    private void updatePaymentDashboardFragment(String refNumber, String notifikasiString) {
        historyDashboardFragment = HistoryDashboardFragment.newInstance(refNumber, notifikasiString);
        getSupportFragmentManager().beginTransaction().replace(R.id.fragmentDashboardIB, historyDashboardFragment, TAG_FRAGMENT_FOUR).commit();
        bottomNavigationView.setSelectedItemId(R.id.navigation_inbox);
        setStatusColorAndStatusBar(R.color.toolbar_blue, View.SYSTEM_UI_FLAG_VISIBLE);
    }

    @Override
    public int getLayoutResource() {
        return R.layout.activity_dashboard_ib;
    }

    @Override
    protected void initiateNavBar() {
        //set onclick menu navigation bar
        bottomNavigationView.setOnNavigationItemSelectedListener(
                new BottomNavigationView.OnNavigationItemSelectedListener() {

                    @Override
                    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                        switch (item.getItemId()) {
                            case R.id.navigation_home:
                                if (successMessage == null) {
                                    item.setIcon(GeneralHelper.getImageId(getBaseContext(), "ic_home_color_revamp"));
                                    resetNotSelectedIcon(bottomNavigationView, item);
                                    getTheme().applyStyle(R.style.AppThemeWhiteBar, true);
                                    switchFragment(0, TAG_FRAGMENT_ONE);
//                                    setStatusColorAndStatusBar(R.color.toolbar_blue, View.SYSTEM_UI_FLAG_VISIBLE);
                                    currentPos = 0;
                                    bottomNavigationView.post(() -> applyBottomNavTextStyle(layoutNav));
                                    isHome = true;

                                    // Trigger OTA download untuk React Native fragment
                                    triggerOTADownload();
                                }
                                return true;
                            case R.id.navigation_mutasi:
                                item.setIcon(GeneralHelper.getImageId(getBaseContext(), "ic_mutasi_color_revamp"));
                                resetNotSelectedIcon(bottomNavigationView, item);
                                switchFragment(1, TAG_FRAGMENT_TWO);
//                                setStatusColorAndStatusBar(R.color.toolbar_blue, View.SYSTEM_UI_FLAG_VISIBLE);
                                currentPos = 1;
                                isHome = false;
                                bottomNavigationView.post(() -> applyBottomNavTextStyle(layoutNav));

                                // Trigger OTA download untuk React Native fragment
                                triggerOTADownload();
                                return true;
                            case R.id.navigation_transfer:
//                                QrMPMActivity.launchIntent(DashboardIBActivity.this, false, false);
                                Intent intentQr = new Intent(DashboardIBActivity.this, PaymentActivity.class);
                                intentQr.putExtra("destination", "qris_scan");
                                startActivity(intentQr);
                                return true;
                            case R.id.navigation_inbox:
                                item.setIcon(GeneralHelper.getImageId(getBaseContext(), "ic_riwayat_color_revamp"));
                                resetNotSelectedIcon(bottomNavigationView, item);
                                switchFragment(3, TAG_FRAGMENT_FOUR);
                                currentPos = 3;
                                setupTransparentStatusBar();
//                                setStatusColorAndStatusBar(R.color.toolbar_blue, View.SYSTEM_UI_FLAG_VISIBLE);
                                isHome = false;
                                bottomNavigationView.post(() -> applyBottomNavTextStyle(layoutNav));
                                return true;
                            case R.id.navigation_akun:
                                item.setIcon(GeneralHelper.getImageId(getBaseContext(), "ic_settings_color_revamp"));
                                resetNotSelectedIcon(bottomNavigationView, item);
                                switchFragment(4, TAG_FRAGMENT_FIVE);
//                                setStatusColorAndStatusBar(R.color.toolbar_blue, View.SYSTEM_UI_FLAG_VISIBLE);
                                currentPos = 4;
                                isHome = false;
                                bottomNavigationView.post(() -> applyBottomNavTextStyle(layoutNav));
                                return true;
                        }
                        return false;
                    }
                });

        buildFragmentsList();

        // Set the 0th Fragment to be displayed by default.
        if (successMessage == null) {
            switchFragment(0, TAG_FRAGMENT_ONE);
        }

        applyBottomNavTextStyle(layoutNav);
    }

    private void resetNotSelectedIcon(BottomNavigationView bottomNavigationView, MenuItem item) {
        Menu menu = bottomNavigationView.getMenu();
        for (int i = 0; i < menu.size(); i++) {
            MenuItem itemMenu = menu.getItem(i);
            if (itemMenu != item) {
                switch (itemMenu.getItemId()) {
                    case R.id.navigation_home:
                        itemMenu.setIcon(GeneralHelper.getImageId(getBaseContext(), "ic_home_no_color_revamp"));
                        break;
                    case R.id.navigation_mutasi:
                        itemMenu.setIcon(GeneralHelper.getImageId(getBaseContext(), "ic_mutasi_no_color_revamp"));
                        break;
                    case R.id.navigation_inbox:
                        itemMenu.setIcon(GeneralHelper.getImageId(getBaseContext(), "ic_riwayat_no_color_revamp"));
                        break;
                    case R.id.navigation_akun:
                        itemMenu.setIcon(GeneralHelper.getImageId(getBaseContext(), "ic_settings_no_color_revamp"));
                        break;
                    default:
                        break;
                }
            }
        }
        applyBottomNavTextStyle(layoutNav);
    }

    private void buildFragmentsList() {
//        dashboardIBFragmentRevamp = DashboardRevampFragment.newInstance("");
        dashboardIBFragmentRevamp = ReactNativeFragment.newInstance("BrimoHomepage", null);
        fragmentProfileRevamp = ProfileRevampFragment.newInstance("");
        historyDashboardFragment = HistoryDashboardFragment.newInstance("", "");
        mutationFragment = ReactNativeFragment.newInstance("BrimoPortfolio", null);

        fragments.add(dashboardIBFragmentRevamp);
        fragments.add(mutationFragment);
        fragments.add(transferFragment);
        fragments.add(historyDashboardFragment);
        fragments.add(fragmentProfileRevamp);
    }


    public static void addMenu(Activity activity, List<BubbleShowCaseBuilder> list) {
        BubbleShowCaseBuilder navMenu = new BubbleShowCaseBuilder(activity) //Activity instance
                .title(activity.getString(R.string.txt_easy_access_title)) //Any title for the bubble view
                .description(activity.getString(R.string.txt_easy_access_sub_title))
                .backgroundColor(Color.WHITE)
                .textColor(Color.BLACK)
                .targetView(layoutNav)
                .buttonTitle(activity.getString(R.string.button_lanjut_ds))
                .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM);
        list.add(3, navMenu);
    }


    private void injectDependency() {

        getActivityComponent().inject(this);

        if (presenter != null) {
            //cek apakah device sudah nge flag login atau logout
            if (presenter.getLoginFlag()) {
                presenter.setView(this);
                presenter.setUrlLogout(GeneralHelper.getString(R.string.url_logout_v5));
                presenter.setUrlRateData(GeneralHelper.getString(R.string.url_rate_us_data));
//                presenter.setUrlGetAktivasiVoiceAssistant(getString(R.string.url_get_aktivasi_voice_assistant));
                presenter.start();
//                presenter.getAktivasiVoiceAssistant();
            } else {
                onSessionEnd(GeneralHelper.getString(R.string.sessionText));
            }

        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu, menu);
        return true;
    }

    @Override
    public void onCloseOnboarding() {
        toggleClickable();
    }

    @Override
    public void onSessionEndToLogin(String message) {
        if (isSessionEnd)
            return;

        isSessionEnd = true;

        if (notifikasiModel != null) {
            try {
                LoginActivity.launchIntentSessionExp(this, message, notifikasiModel);
            } catch (Exception e) {
                super.onSessionEnd(message);
            }

            notifikasiModel = null;
        } else {
            super.onSessionEnd(message);
        }
    }

    @Override
    public void onSuccessGetOnboarding(List<OnboardingItemModel> onboardingItemModels) {
        // Do nothing
    }

    @Override
    public void showRateFragment(RateUs rateUs) {
        RateUsBottomFragment rateUsBottomFragment = new RateUsBottomFragment(this, rateUs.getRateStat(), 1);
        rateUsBottomFragment.setCancelable(false);
        //check is safe pop up dialog
        if (!getSupportFragmentManager().isStateSaved()) {
            rateUsBottomFragment.show(getSupportFragmentManager(), "");
        }
    }

    @Override
    public void showNewRateFragment(long rateStat) {
        if (rateStat < AppConfig.RATE_THHOLD) {
            RateUsBottomFragment rateUsBottomFragment = new RateUsBottomFragment(this);
            rateUsBottomFragment.setCancelable(false);
            //check is safe pop up dialog
            if (!getSupportFragmentManager().isStateSaved()) {
                rateUsBottomFragment.show(getSupportFragmentManager(), "");
            }
        }
    }

    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();
        parameterModel.setStringLabelTujuan("Nomor Tujuan");
        parameterModel.setStringLabelNominal("Nominal Pembayaran");
        parameterModel.setStringButtonSubmit("Bayar");
        parameterModel.setStringLabelMinimum("Pembayaran");
        return parameterModel;
    }

    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();
        parameterKonfirmasiModel.setStringLabelTujuan(GeneralHelper.getString(R.string.nomor_tujuan));
        parameterKonfirmasiModel.setStringButtonSubmit(GeneralHelper.getString(R.string.str_konfirmasi));
        parameterKonfirmasiModel.setDefaultIcon(0);
        return parameterKonfirmasiModel;
    }

    @Override
    public void onSuccessGetInquiry(String typeInquiry, GeneralInquiryResponse generalInquiryResponse, String urlKonfirmasi, String urlPayment, String title) {
        if (typeInquiry != null) {
            if (typeInquiry.equalsIgnoreCase("0")) {
                InquiryGeneralCloseActivity.launchIntent(this, generalInquiryResponse, urlKonfirmasi, urlPayment, title, setParameter(), isFromFastMenu);
            } else if (typeInquiry.equalsIgnoreCase("1")) {
                InquiryGeneralOpenActivity.launchIntent(this, generalInquiryResponse, urlKonfirmasi, urlPayment, title, setParameter(), isFromFastMenu);
            } else if (typeInquiry.equalsIgnoreCase("2")) {
                InquiryPlnTokenActivity.launchIntent(this, generalInquiryResponse, title, urlKonfirmasi, urlPayment, setParameter(), isFromFastMenu);
            } else {
                InquiryGeneralCloseActivity.launchIntent(this, generalInquiryResponse, urlKonfirmasi, urlPayment, title, setParameter(), isFromFastMenu);

            }
        }
    }

    @Override
    public void onSuccessGetInquiryRevamp(String typeInquiry, RestResponse response, String urlKonfirmasi, String urlPayment, String title, String typeInquiryRevamp, String requestContent) {
        if (typeInquiryRevamp != null && !typeInquiryRevamp.isEmpty()) {
            // payment reminder revamp
            if (typeInquiryRevamp.equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.revampListrikPostPaid)) {
                InquiryBrivaRevampResponse inquiryBrivaRevampResponse = response.getData(InquiryBrivaRevampResponse.class);
                ParameterModel parameterModelRevamp = setParameter();
                parameterModelRevamp.setStringButtonSubmit(GeneralHelper.getString(R.string.lanjutkan));
                inquiryBrivaRevampResponse.setPageTitleString(GeneralHelper.getString(R.string.str_konfirmasi));
                InquiryKonfirmasiBrivaRevampCloseActivity.launchIntent(this, inquiryBrivaRevampResponse, urlKonfirmasi, urlPayment, isFromFastMenu, parameterModelRevamp, Constant.TRX_TYPE_LISTRIK);
            } else if (typeInquiryRevamp.equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.revampListrikPrepaid)) {
                InquiryBrivaRevampResponse inquiryBrivaRevampResponse = response.getData(InquiryBrivaRevampResponse.class);
                ParameterModel parameterModelRevamp = setParameter();
                parameterModelRevamp.setStringButtonSubmit(GeneralHelper.getString(R.string.lanjutkan));
                InquiryListrikRevampActivity.launchIntent(this, isFromFastMenu, urlKonfirmasi, urlPayment, inquiryBrivaRevampResponse, parameterModelRevamp, Constant.PLN_TYPE_CODE_PREPAID, "");
            } else if (typeInquiryRevamp.equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.revampEwallet)) {
                InquiryDompetDigitalResponse inquiryDompetDigitalResponse = response.getData(InquiryDompetDigitalResponse.class);
                ParameterModel parameterModelRevamp = setParameter();
                parameterModelRevamp.setStringButtonSubmit(GeneralHelper.getString(R.string.lanjutkan));
                InquiryDompetDigitalRevampActivity.launchIntent(this, inquiryDompetDigitalResponse, urlKonfirmasi, urlPayment, isFromFastMenu, parameterModelRevamp, "");
            }else if (typeInquiryRevamp.equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.kkGPN)) {
                GeneralConfirmationResponse generalConfirmationResponse = response.getData(GeneralConfirmationResponse.class);
                confirmationKKI(generalConfirmationResponse, urlPayment);
            }else if (typeInquiryRevamp.equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.bindingCcGPN)){
                BindingCcKkiResponse bindingCcKkiResponse = response.getData(BindingCcKkiResponse.class);
                confirmationBindingCcKKI(bindingCcKkiResponse, urlPayment);
            }
        } else {
            // payment reminder non revamp
            GeneralInquiryResponse generalInquiryResponse = response.getData(GeneralInquiryResponse.class);
            if (typeInquiry.equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.inquiryOpen)) {
                InquiryGeneralOpenActivity.launchIntent(this, generalInquiryResponse, urlKonfirmasi, urlPayment, title, setParameter(), isFromFastMenu);
            } else if (typeInquiry.equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.listrikPrepaid)) {
                InquiryPlnTokenActivity.launchIntent(this, generalInquiryResponse, title, urlKonfirmasi, urlPayment, setParameter(), isFromFastMenu);
            } else {
                InquiryGeneralCloseActivity.launchIntent(this, generalInquiryResponse, urlKonfirmasi, urlPayment, title, setParameter(), isFromFastMenu);
            }
        }
    }

    private void confirmationKKI(GeneralConfirmationResponse generalConfirmationResponse, String urlPayment){
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();
        parameterKonfirmasiModel.setDefaultIcon(R.drawable.bri);
        parameterKonfirmasiModel.setStringButtonSubmit(GeneralHelper.getString(R.string.txt_konfirmasi));
        KonfirmasiGeneralRevampActivity.launchIntent(this, generalConfirmationResponse, urlPayment, GeneralHelper.getString(R.string.txt_konfirmasi), parameterKonfirmasiModel, isFromFastMenu);
    }

    private void confirmationBindingCcKKI(BindingCcKkiResponse bindingCcKkiResponse, String urlPayment){
        BindingCcKkiActivity.launchIntent(this,bindingCcKkiResponse, urlPayment, isFromFastMenu);
    }

    @Override
    public void onSuccessGetDetailPromoProduct(PromoResponse promoResponse, String promoID, String blastId, String titleButton, NotifikasiModel notifikasiModel) {
        DetailPromoActivity.launchIntentFastMenu(this, promoResponse.getPromo(), promoID, blastId, titleButton, notifikasiModel, false, false, true);

    }

    @Override
    public void onSuccesTartunNds() {

    }

    /*this method used in :
     * aft notif
     */
    @Override
    public void onSuccessGetDataNotif(RestResponse response, String typeNotif) {
        if (typeNotif == null) return;
        switch (typeNotif) {
            case Constant.AFTNotifSchedulerBlastTypeInquiry.aftFailed:
                DetailAftResponse detailAftResponse = response.getData(DetailAftResponse.class);
                DetailAftActivity.launchIntent(this, detailAftResponse);
                break;
            default:
                break;
        }
    }

    @Override
    public void onSuccessDashboardLifestyleMenu(DashboardLifestyleMenuResponse dashboardLifestyleMenuResponse) {
        if (dashboardLifestyleMenuResponse.getMenuDataView().isEmpty()) {
            GeneralHelper.showSnackBarWithTransparentStatusBar(
                    findViewById(R.id.content),
                    GeneralHelper.getString(R.string.maaf_sedang_terjadi_kendala_di_sistem_kami), R.color.error80, R.drawable.ic_cancel_danger_18dp
            );
        } else {
            DashboardLifestyleActivity.launchIntent(
                    this,
                    dashboardLifestyleMenuResponse
            );
        }
    }

    @Override
    public void onSuccessGetConfirmationMokirim(KonfirmasiWebviewEkspedisiResponse konfirmasiWebviewEkspedisiResponse, String urlPaymentMokirim) {
        KonfirmasiWebviewEkspedisiActivity.launchIntent(
                this,
                konfirmasiWebviewEkspedisiResponse,
                urlPaymentMokirim,
                false,
                setParameterKonfirmasi(),
                GeneralHelper.getString(R.string.txt_detail_pengiriman)
        );
    }

    @Override
    public void onSuccessGetConfirmationSplitBill(ConfirmationLifestyleResponse confirmationLifestyleResponse, String urlPayment) {
        KonfirmasiLifestyleActivity.Companion.launchIntent(
                this,
                confirmationLifestyleResponse,
                confirmationLifestyleResponse.getConfirmationPatternCode(),
                confirmationLifestyleResponse.getReceiptPatternCode(),
                "",
                urlPayment

        );
    }

    @Override
    public void onLogout(String message) {
//        FastMenuActivity.launchIntent(this);
        FastMenuNewSkinActivity.launchIntent(this);
    }

    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) {
            GeneralHelper.showBottomDialog(this, message);
        } else {
            switchFragment(0, TAG_FRAGMENT_ONE);
//            dashboardIBFragmentRevamp.onException(message);
        }

    }

    /**
     * Toggle clickable for inner layer of dashboard (disable action when the PFM dialog is not closed yet)
     */
    private void toggleClickable() {
        isOnboardingShowing = !isOnboardingShowing;
        content.setClickable(!isOnboardingShowing);
        if (!isOnboardingShowing) removeOnboarding();
    }

    /**
     * Remove visibility of PFM dialog
     */
    private void removeOnboarding() {
        /*llOnboarding.setVisibility(View.INVISIBLE);*/
    }


    public void onLogoutPressed() {
        OpenBottomSheetGeneralNewSkinFragment.INSTANCE.showDialogConfirmation(
                getSupportFragmentManager(),
                R.drawable.ic_warning_illustration,
                "",
                "Yakin ingin keluar dari aplikasi?",
                "Setelah keluar, kamu perlu login lagi untuk mengakses akun dan melanjutkan aktivitas.",
                createKotlinFunction0(firstBtnFunction),
                createKotlinFunction0(secondBtnFunction),
                true,
                GeneralHelper.getString(R.string.keluar),
                GeneralHelper.getString(R.string.batal2),
                false,
                true,
                true
        );
    }

    Runnable firstBtnFunction = () -> presenter.logOut();

    Runnable secondBtnFunction = () -> {
        // do nothing
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //melanjutkan activity result ke fragment onresultActivity
        for (Fragment fragment : getSupportFragmentManager().getFragments()) {
            fragment.onActivityResult(requestCode, resultCode, data);
        }
        //jika dari transaksi Payment
        if (requestCode == Constant.REQ_PAYMENT) {
            //jika transaksi pembayaran hingga tampilan receipt otomatis saldo di refresh
            if (resultCode == RESULT_OK) {
                if (data != null) {
                    isTransactionSuccess = data.getBooleanExtra(Constant.REQUEST_RECEIPT, false);
                    if (isTransactionSuccess) {
                        // Uncomment this line if want the page always refresh if we go back to the page
//                        dashboardIBFragmentRevamp.updateSaldoUtama();

                        // pengecekan  rateus
                        presenter.CheckRate();
                    } else {
                        switchFragment(3, TAG_FRAGMENT_FOUR);
                        currentPos = 3;
                        setStatusColorAndStatusBar(R.color.toolbar_blue, View.SYSTEM_UI_FLAG_VISIBLE);
                        isHome = false;
                        bottomNavigationView.setSelectedItemId(R.id.navigation_inbox);
                    }
                }
            } else {
                if (data != null) {
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE);
                    if (errorMessage != null) {
                        onException(errorMessage);
                        errorMessage = null;
                    }
                }

            }
        }
    }

    /**
     * Handles custom back navigation logic for DashboardIBActivity.
     * If not on Home tab, navigates to Home. If already on Home, shows logout dialog.
     */
    private void handleCustomBack() {
        Log.d("[DEBUG-BACK]HandleBack", "isHome: " + isHome + ", currentPos: " + currentPos);
        if (!isHome) {
            switchFragment(0, TAG_FRAGMENT_ONE);
            bottomNavigationView.setSelectedItemId(R.id.navigation_home);
            isHome = true;
        } else {
            if (backPressedOnce) {
                onLogoutPressed();
            } else {
                backPressedOnce = true;
                handler.postDelayed(resetBackFlag, 2000); // Reset dalam 2 detik
            }
        }
    }

    @Override
    public void onException06(ExceptionResponse response) {
        CustomBottomDialogFragment customBottomDialogFragment = new CustomBottomDialogFragment(this, response.getTitle(), response.getDescription(), getString(R.string.btn_okay_sad), response.getImageName(), response.getImagePath(), true);
        customBottomDialogFragment.show(this.getSupportFragmentManager(), "");
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateNavigationMenuTitles();
        if (presenter != null) {
            presenter.setDisablePopup(false);
        }
    }

    private void updateNavigationMenuTitles() {
        layoutNav.getMenu().findItem(R.id.navigation_home).setTitle(R.string.title_bar_menu_home);
        layoutNav.getMenu().findItem(R.id.navigation_mutasi).setTitle(R.string.title_bar_portfolio);
        layoutNav.getMenu().findItem(R.id.navigation_transfer).setTitle("");
        layoutNav.getMenu().findItem(R.id.navigation_inbox).setTitle(R.string.title_bar_history);
        layoutNav.getMenu().findItem(R.id.navigation_akun).setTitle(R.string.title_bar_settings);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Cleanup OTA download manager
        if (downloadManager != null) {
            downloadManager.cleanup();
        }
    }

    @Override
    public void onSessionEnd(String message) {
        if (isSessionEnd)
            return;

        isSessionEnd = true;
        super.onSessionEnd(message);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);

        if (intent != null) {
            if (intent.getExtras() != null) {
                parseDataNotifDetailInbox(getIntent());
            }
        }
    }

    @Override
    public void onClickYes() {
        presenter.logOut();
    }

    public interface OnWindowFocusChangeListener {
        void onWindowFocusChanged(Boolean hasFocus);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (focusChangeListener != null) {
            focusChangeListener.onWindowFocusChanged(hasFocus);
        }
    }

    @Override
    public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
        switch (errorCode) {
            case Constant.ERROR_USER_CANCELED:
            case Constant.ERROR_NEGATIVE_BUTTON:
                runOnUiThread(() -> Objects.requireNonNull(
                        fragmentProfileRevamp.getListProfileInfoAdapter()).isSwitchChecked(Constant.ProfileInfoRevamp.BIOMETRIC, false)
                );
                break;
            case Constant.ERROR_CANCELED:
            case Constant.ERROR_LOCKOUT:
                fragmentProfileRevamp.onTooManyAttemps();
                break;
            case Constant.ERROR_NO_BIOMETRICS:
                fragmentProfileRevamp.showDialogFinger();
                break;
            default:
                break;
        }
    }

    public void switchToProfile() {
        switchFragment(4, TAG_FRAGMENT_FIVE);
        currentPos = 4;
        setStatusColorAndStatusBar(R.color.toolbar_blue, View.SYSTEM_UI_FLAG_VISIBLE);
        isHome = false;
        bottomNavigationView.setSelectedItemId(R.id.navigation_akun);
    }

    @Override
    public void onAuthenticationFailed() {
        //do nothing
    }

    @Override
    public void onAuthenticationSuccess() {
        fragmentProfileRevamp.openPin();
    }

    @Override
    public void onBiometricChanged() {
        //do nothing
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        try {
            if (requestCode == Constant.REQUEST_LOCATION_MAP) {
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    fragmentProfileRevamp.getLocation();
                }
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG_PROFILE, "onRequestPermissionsResult: ", e);
            }
        }
    }

    /**
     * Setup OTA download dengan delegate
     */
    private void setupOTADownload() {
        Log.i("OTA", "Setting up OTA download manager");
        downloadManager.setDelegate(new DownloadManager.DownloadManagerDelegate() {
            @Override
            public void downloadProgress(double percentage) {
                Log.d("OTA", "Download progress: " + String.format("%.1f", percentage) + "%");
                if (progressDialog != null) {
                    progressDialog.updateProgress(percentage);
                }
            }

            @Override
            public void downloadCompleted(File bundleFile) {
                Log.i("OTA", "Download completed successfully: " + bundleFile.getAbsolutePath());

                isDownloading = false;
                if (progressDialog != null) {
                    progressDialog.dismiss();
                    progressDialog = null;
                }

                // Simulate reload progress
                simulateReloadProgress();
            }

            @Override
            public void downloadFailed(Exception error) {
                isDownloading = false;
                if (progressDialog != null) {
                    progressDialog.dismiss();
                    progressDialog = null;
                }

                Log.e("OTA", "Download failed: " + error.getMessage(), error);

                // Log error message
                Log.e("OTA", "Download failed with error: " + error.getMessage());
            }
        });
    }

    /**
     * Trigger download OTA bundle
     */
    public void triggerOTADownload() {
        if (BrimodSDK.isForDemo()) {
            if (isDownloading) {
                Log.w("OTA", "Download already in progress, skipping...");
                return;
            }

            // Show progress using view methods for version check
            this.showProgress();
            isDownloading = true;

            downloadManager.checkVersion(
                    id.co.bri.brimo.reactnative.BrimodSDK.OTA_VERSION_CHECK_URL,
                    new DownloadManager.VersionCheckCallback() {
                        @Override
                        public void onSameVersion(String version) {
                            runOnUiThread(() -> {
                                Log.i("OTA", "Bundle version is up to date: " + version);
                                DashboardIBActivity.this.hideProgress();
                                isDownloading = false;
                            });
                        }

                        @Override
                        public void onNewVersion(String newVersion) {
                            runOnUiThread(() -> {
                                Log.i("OTA", "New bundle version available: " + newVersion);
                                DashboardIBActivity.this.hideProgress();
                                // Show progress dialog for download
                                progressDialog = new OTADownloadProgressDialog(DashboardIBActivity.this);
                                progressDialog.show();
                                isDownloading = true;

                                // Use the version name from response as filename
                                String fileName = newVersion;
                                String downloadUrl = id.co.bri.brimo.reactnative.BrimodSDK.OTA_BUNDLE_DOWNLOAD_URL;
                                Log.i("OTA", "Downloading file: " + fileName + " from URL: " + downloadUrl);

                                downloadManager.downloadFile(
                                        downloadUrl,
                                        fileName
                                );
                            });
                        }

                        @Override
                        public void onError(Exception e) {
                            runOnUiThread(() -> {
                                Log.e("OTA", "Version check failed: " + e.getMessage(), e);
                                DashboardIBActivity.this.hideProgress();
                                isDownloading = false;
                                // Optionally show error to user
                                android.widget.Toast.makeText(DashboardIBActivity.this, "Gagal memeriksa update: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                            });
                        }
                    }
            );
        }
    }

    /**
     * Simulate reload progress dengan progress bar
     */
    private void simulateReloadProgress() {
        Log.i("OTA", "Starting reload progress simulation");

        final int[] progress = {0};
        final Handler handler = new Handler(Looper.getMainLooper());

        Runnable progressRunnable = new Runnable() {
            @Override
            public void run() {
                if (progress[0] < 100) {
                    progress[0] += 10; // Increment by 5%
                    if (progressDialog != null) {
                        progressDialog.updateProgress(progress[0]);
                        progressDialog.setMessage("Memuat update terbaru... " + progress[0] + "%");
                    }
                    handler.postDelayed(this, 100); // Update every 100ms
                } else {
                    // Reload completed
                    Log.i("OTA", "Reload progress simulation completed");

                    if (progressDialog != null) {
                        progressDialog.dismiss();
                        progressDialog = null;
                    }

                    // Reload React Native context dengan bundle baru
                    reloadReactNativeContext();
                }
            }
        };

        handler.post(progressRunnable);
    }

    /**
     * Reload React Native context dengan bundle baru
     */
    private void reloadReactNativeContext() {
        Log.i("OTA", "Starting React Native context reload...");

        // Reload React Native context untuk menggunakan bundle baru
        ReactInstanceManager reactInstanceManager = ((BaseApp) getApplication())
                .getReactNativeHost()
                .getReactInstanceManager();

        reactInstanceManager.recreateReactContextInBackground();

        Log.i("OTA", "React Native context reload initiated successfully");
    }

    /**
     * Selects a tab in the BottomNavigationView by index (0=Home, 1=Portfolio, 2=Transfer, 3=Inbox, 4=Akun).
     * Always runs on the main thread to avoid UI thread errors.
     */
    public void selectTabByIndex(int index, Map<String, Object> params) {
        int[] tabIds = {
            R.id.navigation_home,
            R.id.navigation_mutasi,
            R.id.navigation_transfer,
            R.id.navigation_inbox,
            R.id.navigation_akun
        };
        String[] tags = {
            TAG_FRAGMENT_ONE,
            TAG_FRAGMENT_TWO,
            null,
            TAG_FRAGMENT_FOUR,
            TAG_FRAGMENT_FIVE
        };
        if (index >= 0 && index < tabIds.length) {
            runOnUiThread(() -> {
                bottomNavigationView.setSelectedItemId(tabIds[index]);
                if (index == 0) { // HomePage
                    Bundle bundle = mapToBundle(params);
                    dashboardIBFragmentRevamp = ReactNativeFragment.newInstance("BrimoHomepage", bundle);
                    getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragmentDashboardIB, dashboardIBFragmentRevamp, TAG_FRAGMENT_ONE)
                        .commit();
                } else if (index == 1) { // Portfolio
                    Bundle bundle = mapToBundle(params);
                    mutationFragment = ReactNativeFragment.newInstance("BrimoPortfolio", bundle);
                    getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragmentDashboardIB, mutationFragment, TAG_FRAGMENT_TWO)
                        .commit();
                } else if (index == 2) { // Transfer
                    // Biasanya transfer adalah Activity, bukan fragment
//                    QrMPMActivity.launchIntent(DashboardIBActivity.this, false, false);
                    Intent intentQr = new Intent(this, PaymentActivity.class);
                    intentQr.putExtra("destination", "qris_scan");
                    startActivity(intentQr);
                } else {
                    // Untuk tab lain, gunakan switchFragment
                    switchFragment(index, tags[index]);
                }
            });
        }
    }

    private Bundle mapToBundle(Map<String, Object> params) {
        Bundle bundle = new Bundle();
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                Object value = entry.getValue();
                String key = entry.getKey();
                if (value instanceof Integer) {
                    bundle.putInt(key, (Integer) value);
                } else if (value instanceof Boolean) {
                    bundle.putBoolean(key, (Boolean) value);
                } else if (value instanceof Double) {
                    bundle.putDouble(key, (Double) value);
                } else if (value instanceof Long) {
                    bundle.putLong(key, (Long) value);
                } else if (value != null) {
                    bundle.putString(key, value.toString());
                }
            }
        }
        return bundle;
    }

    public void applyBottomNavTextStyle(View view) {
        if (view instanceof ViewGroup) {
            ViewGroup vg = (ViewGroup) view;
            for (int i = 0; i < vg.getChildCount(); i++) {
                View child = vg.getChildAt(i);

                // Handle khusus untuk BottomNavigationItemView
                if (child instanceof ViewGroup) {
                    ViewGroup itemContainer = (ViewGroup) child;
                    for (int j = 0; j < itemContainer.getChildCount(); j++) {
                        View itemChild = itemContainer.getChildAt(j);
                        if (itemChild instanceof TextView) {
                            TextView tv = (TextView) itemChild;
                            updateTextAppearance(tv);
                        }
                    }
                }

                // Handle teks biasa
                if (child instanceof TextView) {
                    TextView tv = (TextView) child;
                    updateTextAppearance(tv);
                } else if (child instanceof ViewGroup) {
                    applyBottomNavTextStyle(child);
                }
            }
        }
    }

    private void updateTextAppearance(TextView tv) {
        int styleRes = tv.isSelected()
                ? R.style.BodyText_Small_SemiBold_NsPrimary600
                : R.style.BodyText_Small_SemiBold_BlackNs600;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            tv.setTextAppearance(styleRes);
        } else {
            tv.setTextAppearance(tv.getContext(), styleRes);
        }
    }
}