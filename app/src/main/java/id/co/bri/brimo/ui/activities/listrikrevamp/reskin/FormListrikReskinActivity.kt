package id.co.bri.brimo.ui.activities.listrikrevamp.reskin

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import androidx.camera.core.ExperimentalGetImage
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryAdapterNs
import id.co.bri.brimo.adapters.SavedAdapterNs
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.IFormListrikReskinPresenter
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.IFormListrikReskinView
import id.co.bri.brimo.databinding.ActivityFormListrikReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.response.DataPlnResponse
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.PlnListResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.UpdateSavedItemNsFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.HapusConfirmationBottomSheetFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.listrik.BSFragmentListrikOption
import id.co.bri.brimo.util.RxBus
import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import id.co.bri.brimo.util.custom_numpad.NumpadType
import id.co.bri.brimo.util.parseFromJson
import io.reactivex.android.schedulers.AndroidSchedulers
import javax.inject.Inject
import kotlin.text.split

@ExperimentalGetImage
class FormListrikReskinActivity: NewSkinBaseActivity(), IFormListrikReskinView,
    View.OnClickListener, SavedAdapterNs.ClickItem, HistoryAdapterNs.ClickItem {
    private var _binding: ActivityFormListrikReskinBinding? = null
    protected val binding get() = _binding!!

    private lateinit var plnList: List<PlnListResponse>

    private var useApi = true

    private lateinit var resData: DataPlnResponse

    @Inject
    lateinit var presenter: IFormListrikReskinPresenter<IFormListrikReskinView>

    lateinit var historyAdapter: HistoryAdapterNs
    lateinit var savedAdapter: SavedAdapterNs

    protected var historyResponses: ArrayList<HistoryResponse> = ArrayList<HistoryResponse>()
    protected var savedResponses: ArrayList<SavedResponse> = ArrayList<SavedResponse>()

    private var isErrorAfterSubmit = false
    private var firstTimeSelectOption = false

    private var isFirstTimeOpenScreen = true

    // Current tab
    private var currentTab = TAB_FAVORIT

    private lateinit var numpadHelper: CustomNumpadHelper

    private lateinit var skeleton: SkeletonScreen

    companion object {
        const val TAG = "InquiryListrikReskinActivity"
        const val INPUT_MAX_LENGTH = 20
        private var currentOption: PlnListResponse? = null

        const val TAB_FAVORIT = 0
        const val TAB_RIWAYAT = 1

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean) {
            isFromFastMenu = fromFastMenu
            caller.apply {
                startActivityForResult(Intent(
                    this,
                    FormListrikReskinActivity::class.java
                ), Constant.REQ_PAYMENT)
            }
        }
    }

    protected var activityTextListener: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            // Override in child class if needed
        }

        override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            changeText(charSequence, i, i1, i2)
        }

        override fun afterTextChanged(editable: Editable) {
            // Override in child class if needed
            binding.bivNoPelanggan.removeAllEndIcons()
            if (editable.toString().isNotEmpty()) {
                binding.bivNoPelanggan.addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                    binding.bivNoPelanggan.clearText()
                    binding.bivNoPelanggan.clearError()
                }
            }
            binding.bivNoPelanggan.addEndIcon(R.drawable.ic_scan_reskin) {
                checkPermissionAndOpenScanner()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityFormListrikReskinBinding.inflate(layoutInflater)
        skeleton = Skeleton.bind(binding.bslContent.binding.llContent)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_form_listrik_reskin)
            .show()

        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()

        RxBus.listen(ScanEvent::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                binding.bivNoPelanggan.setText(event.resScan)
            }

        RxBus.listen(FavoriteEvent::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                if(event.isAddFavorite) {
                    showSnackbar("Daftar Favorit berhasil ditambahkan.", ALERT_CONFIRM)
                    presenter.getDataForm()
                }
            }

        RxBus.listen(FormListrikEvent::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                if(event.isReload) {
                    presenter.getDataForm()
                }
            }
    }

    private fun onBindView() {
        binding.bivNoPelanggan.attachNumpad(this, NumpadType.PHONE, { pin ->

        }, onAttached = { numpad ->
            numpadHelper = numpad
        })
        binding.bivNoPelanggan.isEnabled = currentOption!=null

        binding.bslContent.setTextToolbar(this, "Listrik")
        binding.btnSubmit.setOnClickListener(this)
        binding.etJenisListrik.setOnClickListener { showOptionListrik() }
        binding.llListrikType.setOnClickListener {
            showOptionListrik()
        }
        binding.bivNoPelanggan.apply {
            maxLength(INPUT_MAX_LENGTH)
            setInputType(InputType.TYPE_CLASS_TEXT)
            addTextChangedListener(activityTextListener)
            addEndIcon(R.drawable.ic_scan_reskin) {
                checkPermissionAndOpenScanner()
            }
        }
        binding.btnTokenCheck.setOnClickListener {
            CetakTokenReskinActivity.launchIntent(this@FormListrikReskinActivity, isFromFastMenu)
        }

        binding.llAddSavedList.apply {
            visibility = if(isFromFastMenu) View.GONE else View.VISIBLE
            setOnClickListener {
                AddSavedListrikActivity.launchIntent(this@FormListrikReskinActivity, isFromFastMenu, plnList.toMutableList(), savedResponses.toMutableList())
            }
        }

        setupAdapters()
        setupTabFunctionality()

        binding.searchviewBriva.setOnClickListener {
            SearchSavedHistoryListrikActivity.launchIntent(
                this,
                savedResponses,
                historyResponses,
                isFromFastMenu
            )
        }

//        Handler(Looper.getMainLooper()).postDelayed({
//            bindWithMock()
//        }, 3000)
    }

    private fun bindWithMock() {
        if(!useApi) {
            skeleton.hide()
            val data: DataPlnResponse = parseFromJson(this, "data/mockjson/listrik/formlistrik.json")

            historyResponses.apply {
                clear()
                addAll(data.history)
            }
            savedResponses.apply {
                clear()
                addAll(data.saved)
            }
            resData = data
            plnList = data.plnList

            savedAdapter.notifyDataSetChanged()
            historyAdapter.notifyDataSetChanged()

            updateEmptyStates()
            if(isFirstTimeOpenScreen) showOptionListrik()

            isFirstTimeOpenScreen = false
        }
    }

    private fun setupTabFunctionality() {
        // Set up tab click listeners
        if (binding.tabFavorit != null) {
            binding.tabFavorit.setOnClickListener { view -> switchToFavoritTab() }
        }

        if (binding.tabRiwayat != null) {
            binding.tabRiwayat.setOnClickListener { view -> switchToRiwayatTab() }
        }

        // Default to Favorit tab (without clearing search field on initial load)
        switchToFavoritTab()
    }

    private fun setupAdapters() {
        // Saved adapter
        initiateSavedAdapter()

        // History adapter
        initiateHistoryAdapter()
    }

    fun initiateHistoryAdapter() {
        binding.rvRiwayat.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        historyAdapter =
            HistoryAdapterNs(
                this,
                historyResponses,
                this,
                0,
                isFromFastMenu,
                "listrik"
            )
        binding.rvRiwayat.adapter = historyAdapter
    }

    fun initiateSavedAdapter() {
        binding.rvDaftarFavorit.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        savedAdapter =
            SavedAdapterNs(this, savedResponses, this, 0, isFromFastMenu)
        binding.rvDaftarFavorit.adapter = savedAdapter

    }

    private fun switchToFavoritTab() {
        currentTab = TAB_FAVORIT
        updateTabAppearance()
        showFavoritContent()
    }

    private fun switchToRiwayatTab() {
        currentTab = TAB_RIWAYAT
        updateTabAppearance()
        showRiwayatContent()
    }

    private fun updateTabAppearance() {
        // Reset all tabs
        binding.tabFavorit.setTextColor(ContextCompat.getColor(this, R.color.black))
        binding.tabFavorit.setBackgroundResource(R.drawable.rounded_button_neutral_ns)

        binding.tabRiwayat.setTextColor(ContextCompat.getColor(this, R.color.black))
        binding.tabRiwayat.setBackgroundResource(R.drawable.rounded_button_neutral_ns)

        // Set active tab
        when (currentTab) {
            TAB_FAVORIT -> {
                binding.tabFavorit.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.text_brand_primary_ns
                    )
                )
                binding.tabFavorit.setBackgroundResource(R.drawable.rounded_button_soft_ns)
            }

            TAB_RIWAYAT -> {
                binding.tabRiwayat.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.text_brand_primary_ns
                    )
                )
                binding.tabRiwayat.setBackgroundResource(R.drawable.rounded_button_soft_ns)
            }
        }
    }

    private fun showFavoritContent() {
        binding.contentFavorit.visibility = View.VISIBLE
        binding.contentRiwayat.visibility = View.GONE
    }

    private fun showRiwayatContent() {
        binding.contentFavorit.visibility = View.GONE
        binding.contentRiwayat.visibility = View.VISIBLE
    }

    private fun updateEmptyStates() {
        // Update Favorit tab empty state
        if (savedResponses.isEmpty()) {
            binding.rvDaftarFavorit.visibility = View.GONE
            binding.llNoDataSaved.visibility = View.VISIBLE
        } else {
            binding.rvDaftarFavorit.visibility = View.VISIBLE
            binding.llNoDataSaved.visibility = View.GONE
        }

        // Update Riwayat tab empty state
        if (historyResponses.isEmpty()) {
            binding.rvRiwayat.visibility = View.GONE
            binding.llNoHistory.visibility = View.VISIBLE
        } else {
            binding.rvRiwayat.visibility = View.VISIBLE
            binding.llNoHistory.visibility = View.GONE
        }
    }

    private fun checkPermissionAndOpenScanner() {
        val permissionCheck = ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
        if (permissionCheck != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.CAMERA),
                Constant.REQUEST_CAMERA
            )
        } else {
            // Permission already granted
            ScannerReskinActivity.launchIntent(this, isFromFastMenu)
//            ScannerActivity.launchIntentScanner(this)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == Constant.REQUEST_CAMERA) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Permission granted, now open the scanner
//                ScannerActivity.launchIntentScanner(this)
                ScannerReskinActivity.launchIntent(this, isFromFastMenu)
            } else {
                // Permission denied
                showAlertFinish(getString(R.string.notes_need_permission))
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@FormListrikReskinActivity
            if(useApi) getDataForm()
            start()
        }
    }

    private fun showOptionListrik() {
        BSFragmentListrikOption(plnList) { _, item ->
            currentOption = item
            if(binding.bivNoPelanggan.isError) {
                checkValidation()
            }
            binding.bivNoPelanggan.isEnabled = currentOption!=null

            if(firstTimeSelectOption){
                isErrorAfterSubmit = !isValidationNoMeter()
            }

            binding.etJenisListrik.setText(currentOption?.name)

            //load icon transaction
            GeneralHelper.loadIconTransaction(
                this@FormListrikReskinActivity,
                currentOption?.iconPath,
                currentOption?.iconName!!.split("\\.".toRegex())
                    .dropLastWhile { it.isEmpty() }
                    .toTypedArray()[0],
                binding.ivArea,
                GeneralHelper.getImageId(this@FormListrikReskinActivity, "bri")
            )
            binding.iconContainer.visibility = View.VISIBLE
            binding.iconContainer.setBackgroundResource(R.drawable.bg_white_full_rounded_ns)
            binding.regionTextview.visibility = View.VISIBLE
            binding.ivArea.setPadding(0, 0, 0, 0)
        }.apply {
            show(supportFragmentManager, BSFragmentListrikOption.TAG)
        }
    }

    private fun onBindIntentData() {
    }

    override fun onSuccessGetForm(data: DataPlnResponse) {
        skeleton.hide()

        historyResponses.apply {
            clear()
            addAll(data.history)
        }
        savedResponses.apply {
            clear()
            addAll(data.saved)
        }
        resData = data
        plnList = data.plnList

        savedAdapter.notifyDataSetChanged()
        historyAdapter.notifyDataSetChanged()

        updateEmptyStates()
        if(isFirstTimeOpenScreen) showOptionListrik()

        isFirstTimeOpenScreen = false
    }

    override fun onSuccessInquiry(data: InquiryBrivaRevampResponse) {
        val code = currentOption?.code!!
        if(code.contains("prepaid"))
            InquiryListrikReskinActivity.launchIntent(this, isFromFastMenu, data, InquiryPlnRequest(
                currentOption?.code ?: "", binding.bivNoPelanggan.getText()
            ))
        else ConfirmClosedBillListrikReskinActivity.launchIntent(this, isFromFastMenu,data)
    }

    override fun onSuccessGetConfirmation(brivaConfirmationResponse: GeneralConfirmationResponse?) {
    }

    override fun onSuccessGetPayment(receiptRevampResponse: ReceiptRevampResponse) {
    }

    override fun changeText(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {
        val isNotEmpty = binding.etJenisListrik.length() != 0
        checkValidation()

        if(isErrorAfterSubmit) {
            if(isValidationNoMeter()) {
                binding.bivNoPelanggan.clearError()
                isErrorAfterSubmit = false
            }
        }

        binding.bivNoPelanggan.removeAllEndIcons()
        if(isNotEmpty) {
            binding.btnSubmit.backgroundTintList = ColorStateList.valueOf(
                GeneralHelper.getColor(R.color.primary_default_ns)
            )
            binding.bivNoPelanggan.addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                binding.bivNoPelanggan.clearText()
                binding.bivNoPelanggan.clearError()
            }
        } else {
            binding.btnSubmit.backgroundTintList = ColorStateList.valueOf(
                GeneralHelper.getColor(R.color.black_ns_300)
            )
        }
        binding.bivNoPelanggan.addEndIcon(R.drawable.ic_scan_reskin) {
            checkPermissionAndOpenScanner()
        }
    }

    override fun onClick(view: View?) {
        when (view?.id) {
            R.id.btnSubmit -> {
                presenter.postInquiry(InquiryPlnRequest(
                    currentOption?.code ?: "", binding.bivNoPelanggan.getText()
                ))
                isErrorAfterSubmit = !isValidationNoMeter()
//                checkValidation()
            }
        }
    }

    override fun onClickSavedItem(savedResponse: SavedResponse?) {
        val item = savedResponse!!.value.mappingTo()

        presenter.postInquiry(InquiryPlnRequest(
            item!!.billingType, item!!.idPel
        ))

    }

    override fun onClickUpdateItem(savedResponse: SavedResponse?, position: Int) {
        val updateSavedItemFragment = UpdateSavedItemNsFragment(savedResponse, {savedResponseItem, type, position ->
            when(type) {
                Constant.EditOptionNs.FAV -> {
                    val savedId = savedResponseItem?.value!!.split("|")[0]
                    val productId = savedResponseItem?.value!!.split("|")[1]

                    presenter.favoriteSavedList(param = SavedListNs(
                        saveAs = savedResponseItem?.title,
                        savedId = savedId.toInt(),
                        productId = productId
                    ))
                }
                Constant.EditOptionNs.EDIT -> {

                }
                Constant.EditOptionNs.HAPUS -> {
                    val bottomSheetFragment = HapusConfirmationBottomSheetFragment.newInstance(
                        savedResponseItem = savedResponseItem,
                        onConfirm = {
                            val savedId = savedResponseItem?.value!!.split("|")[0]
                            val productId = savedResponseItem?.value!!.split("|")[1]

                            presenter.removeSavedList(param = SavedListNs(
                                savedId = savedId.toInt(),
                                productId = productId
                            ))
                        },
                        onCancel = {
                            // Just dismiss the bottom sheet, no action needed
                        }
                    )
                    bottomSheetFragment.show(supportFragmentManager, "HapusConfirmationBottomSheet")
                }
                Constant.EditOptionNs.NON_FAV -> {
                    val savedId = savedResponseItem?.value!!.split("|")[0]
                    val productId = savedResponseItem?.value!!.split("|")[1]

                    presenter.unfavoriteSavedList(param = SavedListNs(
                        savedId = savedId.toInt(),
                        productId = productId
                    ))
                }
            }
        }, position)
        updateSavedItemFragment.show(supportFragmentManager, "")
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse?) {
        val item = historyResponse!!.value.mappingTo()

        presenter.postInquiry(InquiryPlnRequest(
            item!!.billingType, item!!.idPel
        ))
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK)
                finish()
            } else {
                this.setResult(RESULT_CANCELED, data)
            }
        }
    }

    fun isValidationNoMeter(): Boolean {
        val minLength = when (currentOption?.code) {
            "prepaid" -> 11
            "postpaid" -> 12
            "nontaglis" -> 13
            else -> 0
        }

        return binding.bivNoPelanggan.getText().length >= minLength &&
                binding.etJenisListrik.length() != 0
    }

    fun getOptionProductCode(code: String): Int {
        return when (code) {
            "prepaid" -> 11
            "postpaid" -> 12
            "nontaglis" -> 13
            else -> 0
        }
    }

    fun checkValidation() {
        binding.btnSubmit.isEnabled = isValidationNoMeter()

        if(!isValidationNoMeter())
            if(binding.bivNoPelanggan.isError) binding.bivNoPelanggan.setError(String.format(
                GeneralHelper.getString(R.string.text_minimal_input),
                getOptionProductCode(currentOption?.code?:"")
            ))
        else {
            binding.bivNoPelanggan.clearError()
//            presenter.postInquiry(InquiryPlnRequest(
//                currentOption?.code ?: "", binding.bivNoPelanggan.getText()
//            ))
        }
    }

    override fun onExceptionWrongIDPel() {
        isErrorAfterSubmit = true
        binding.bivNoPelanggan.apply{
            clearError()
            setError("Nomor tidak ditemukan. Coba cek lagi nomor kamu")
        }
    }

    override fun onExceptionNotAvail(message: String) {
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            fragmentManager = supportFragmentManager,
            imgPath = "",
            imgName = "ic_checklist_receipt_newskin",
            titleTxt = "Tagihan Sudah Dibayar",
            subTitleTxt = "Cek secara berkala untuk informasi tagihan berikutnya, ya.",
            btnFirstFunction = { },
            isClickableOutside = true,
            firstBtnTxt = "Tutup",
            showCloseButton = true
        )
//        showSnackbar(message, GeneralHelperNewSkin.ALERT_ERROR)
    }

    override fun onSuccess(data: RestResponse, type: FavoriteType) {
        presenter.getDataForm()

        when(type) {
            FavoriteType.favorite -> {
                showSnackbar("Daftar berhasil di Pin.", ALERT_CONFIRM)
            }
            FavoriteType.removeFavorite -> {
                showSnackbar("Daftar Favorit berhasil dihapus.", ALERT_CONFIRM)
            }
            FavoriteType.unfavorite -> {
                showSnackbar("Daftar Favorit berhasil diunpin.", ALERT_CONFIRM)
            }
            else -> {}
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val view = currentFocus

        if (ev?.action == MotionEvent.ACTION_DOWN && view is EditText) {
            val outRect = Rect()
            view.getGlobalVisibleRect(outRect)

            val tappedOutsideEditText = !outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())
            val tappedOutsideNumpad = !numpadHelper.isTouchInsideNumpad(ev)

            // ❗ hanya hide jika klik di luar EditText DAN di luar numpad
            if (tappedOutsideEditText && tappedOutsideNumpad) {
                view.clearFocus()
                numpadHelper.hideKeyboard()
            }
        }

        return super.dispatchTouchEvent(ev)
    }

    override fun onSuccessAccountList(
        accountList: MutableList<AccountModel>,
        mainAccount: AccountModel
    ) {

    }
}

fun String.mappingTo(): ItemElectric? {
    return try {
        val parts = this.split("|")

        when (parts.size) {
            2 -> ItemElectric(parts[0], parts[1])
            3 -> ItemElectric(parts[0], parts[1])
            4 -> ItemElectric(parts[1], parts[2])
            else -> throw IllegalArgumentException("Format tidak dikenali")
        }
    } catch (e: Exception) {
        println("Gagal parsing: ${e.message}")
        null
    }
}

data class ItemElectric(
    val billingType: String, val idPel: String
)

data class ScanEvent(
    val resScan: String
)

data class FavoriteEvent(
    val isAddFavorite: Boolean = false,
    val isEdit: Boolean = false,
)

data class FormListrikEvent(
    val isReload: Boolean
)

sealed class FavoriteEventAction(val message: String) {
    object Add : FavoriteEventAction("Daftar Favorit berhasil ditambahkan.")
    object Edit : FavoriteEventAction("Nama berhasil diubah.")
}