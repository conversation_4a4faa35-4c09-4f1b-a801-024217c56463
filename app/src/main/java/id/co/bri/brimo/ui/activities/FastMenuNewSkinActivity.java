package id.co.bri.brimo.ui.activities;

import static id.co.bri.brimo.domain.config.Constant.TAG_NOTIF;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.BuildConfig;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListHomeAdapter;
import id.co.bri.brimo.adapters.PromoFastMenuNewSkinAdapter;
import id.co.bri.brimo.contract.IPresenter.fastmenunewskin.IFastMenuNewSkinPresenter;
import id.co.bri.brimo.contract.IView.fastmenunewskin.IFastMenuNewSkinView;
import id.co.bri.brimo.databinding.ActivityFastMenuNewSkinBinding;
import id.co.bri.brimo.databinding.FragmentBottomSheetGeneralBinding;
import id.co.bri.brimo.domain.SnackBarPosition;
import id.co.bri.brimo.domain.SnackBarType;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.config.MenuConfig;
import id.co.bri.brimo.domain.helpers.BiometricUtils;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin;
import id.co.bri.brimo.domain.helpers.GpsTracker;
import id.co.bri.brimo.domain.helpers.biometric.BiometricCallback;
import id.co.bri.brimo.domain.helpers.biometric.BiometricUtility;
import id.co.bri.brimo.models.AlertMaintenance;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.apimodel.response.DetailPromoResponse;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.FastMenuResponse;
import id.co.bri.brimo.models.apimodel.response.ImageBannerResponse;
import id.co.bri.brimo.models.apimodel.response.LoginResponse;
import id.co.bri.brimo.models.apimodel.response.MaintenanceAlert;
import id.co.bri.brimo.models.apimodel.response.PromoResponse;
import id.co.bri.brimo.models.apimodel.response.SafetyModeResponse;
import id.co.bri.brimo.models.apimodel.response.nfcpayment.NfcPayloadResponse;
import id.co.bri.brimo.models.daomodel.FastMenu;
import id.co.bri.brimo.models.daomodel.FastMenuDefault;
import id.co.bri.brimo.nfcpayment.NFCPayment;
import id.co.bri.brimo.nfcpayment.NFCSettingsContract;
import id.co.bri.brimo.payment.app.PaymentActivity;
import id.co.bri.brimo.security.MyCryptStatic;
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity;
import id.co.bri.brimo.ui.activities.brivarevamp.FormBrivaRevampActivity;
import id.co.bri.brimo.ui.activities.cardless.FormSetorTunaiActivity;
import id.co.bri.brimo.ui.activities.dompetdigitalreskin.FormDompetDigitalReskinActivity;
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.FormDompetDigitalRevamp;
import id.co.bri.brimo.ui.activities.nfcqrtap.NfcPaymentActivity;
import id.co.bri.brimo.ui.activities.pulsadata.FormPulsaDataRevActivity;
import id.co.bri.brimo.ui.activities.pulsadata.reskin.FormPulsaDataReskinActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.FormTransferAliasRevampActivity;
import id.co.bri.brimo.ui.activities.waiting.OtpSmsActivity;
import id.co.bri.brimo.ui.activities.waiting.WaitingActivity;
import id.co.bri.brimo.ui.customviews.BilingualUI;
import id.co.bri.brimo.ui.customviews.bottomsheetdialog.BottomSheetDialogGeneral;
import id.co.bri.brimo.ui.customviews.bottomsheetdialog.BottomSheetDialogType;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefault;
import id.co.bri.brimo.ui.fragments.BottomFragmentLogin;
import id.co.bri.brimo.ui.fragments.PinFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetCustomViewGeneralFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment;
import id.co.bri.brimo.ui.fragments.login.BottomSheet2ButtonLogin;
import id.co.bri.brimo.util.LocaleUtilKt;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;

public class FastMenuNewSkinActivity extends NewSkinBaseActivity implements
        IFastMenuNewSkinView,
        DialogExitCustom.DialogDefaultListener,
        BiometricCallback,
        ListHomeAdapter.OnItemviewClickListener,
        PinFragment.SendPin,
        DialogInformation.OnActionClick, PromoFastMenuNewSkinAdapter.OnClickItem {
    private static final String TAG = "FastMenuActivity";

    private ActivityFastMenuNewSkinBinding binding;

    // Notifications
    private String notifikasiString;
    private NotifikasiModel notifikasiModel;

    // Messages and States
    private static String mDesc;
    private static String desc99;
    private static String successMessage;
    private static String mainUrl;
    private static String mNama = "-";
    private static String mUsername = "";
    private static String mTokenKey = "";

    private static boolean fromFastMenu = true;
    private static float initialYClBrimoVoiceAssistant = 0f;
    private static int initialHeightClBrimoVoiceAssistant;
    private static boolean isFromVoiceAssistant = false;

    private static String imageUrlDecrypt = "";

    private static boolean fromFastMenuReminders = false;
    private static boolean fromFastMenus = false;

    private static boolean isPinReady = false;
    private static boolean isDoneEditing;

    // Misc
    private static String menuCode = "";
    private static String typeFlagLimit = "";
    private static String title = "";
    private String refNum;

    private final int SECOND = 1000;

    protected CountDownTimer countDownTimer;

    private List<FastMenu> mFastMenuList = new ArrayList<>();
    private List<FastMenuDefault> mFastMenuDefaultList = new ArrayList<>();

    private ListHomeAdapter adapter;
    private PromoFastMenuNewSkinAdapter promoAdapter;

    // Fragment
    BottomFragmentLogin fragmentLogin;

    private static boolean isIdleDevice = false;

    public OnWindowFocusChangeListener focusChangeListener;
    private String location = "";
    private final String[] PERMISSIONS_MAP = {Manifest.permission.ACCESS_FINE_LOCATION};
    private NFCPayment nfcPayment;
    private boolean isNfcActive = false;
    private boolean isMNV = false;
    private boolean showPinFromNFC = false;
    private boolean isBioChanged = false;
    private boolean isActivasi = false;

    private ActivityResultLauncher<Unit> nfcSettingsContract = registerForActivityResult(new NFCSettingsContract(), result -> {
        isNfcActive = result;
    });
    private String promoId = "";

    @Inject
    IFastMenuNewSkinPresenter<IFastMenuNewSkinView> activityPresenter;

    public static void launchIntent(Activity caller) {
        // menampilkan halaman Fast Menu
        Intent intent = new Intent(caller, FastMenuNewSkinActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        caller.startActivity(intent);
        caller.finish();
    }

    public static void launchIntentSuccess(Activity caller, String message, boolean removeStackActivity) {
        Intent intent = new Intent(caller, FastMenuNewSkinActivity.class);
        if (removeStackActivity)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        successMessage = message;
        caller.startActivity(intent);
        caller.finish();
    }

    public static void launchIntentSessionEnd(Activity caller, String desc) {
        // menampilkan error ketika session expired
        Intent intent = new Intent(caller, FastMenuNewSkinActivity.class);
        intent.putExtra(Constant.TAG_IDLE,true);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        caller.startActivity(intent);
        mDesc = desc;
        caller.finish();
    }

    public static void launchIntentDialogBox(Activity caller, String desc) {
        // menampilkan error ketika terjadi General Eror
        Intent intent = new Intent(caller, FastMenuNewSkinActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        caller.startActivity(intent);
        desc99 = desc;
        caller.finish();
    }

    public static void launchIntentError(Activity caller, String message) {
        // menampilkan error
        Intent intent = new Intent(caller, FastMenuNewSkinActivity.class);
        caller.startActivity(intent);
        mDesc = message;
        caller.finish();
    }

    public static void launchIntent(Activity caller, boolean mIsDoneEditing) {
        Intent intent = new Intent(caller, FastMenuNewSkinActivity.class);
        isDoneEditing = mIsDoneEditing;
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        caller.startActivity(intent);
        caller.finish();
    }

    public static void launchIntentFromCashback(Activity caller, String featureId) {
        Intent intent = new Intent(caller, FastMenuNewSkinActivity.class);
        caller.startActivity(intent);
        menuCode = featureId;
        caller.finish();
    }

    public static void launchIntentIdleDevice(Activity caller, Boolean isIdle) {
        // menampilkan info idle
        Intent intent = new Intent(caller, FastMenuNewSkinActivity.class);
        intent.putExtra(Constant.TAG_IDLE,isIdle);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        caller.startActivity(intent);
        isIdleDevice = isIdle;
        caller.finish();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setTheme(R.style.AppThemeNewSkinFullTransparent);

        binding = ActivityFastMenuNewSkinBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        setStatusBarDarkIcons();
        nfcPayment = NFCPayment.getInstance(this);


        // Handle post-edit actions
        if (isDoneEditing) {
            showSnackbarErrorMessageRevamp(
                    getResources().getString(R.string.fast_menu_berhasil_diubah),
                    ALERT_CONFIRM, this, false
            );
            isDoneEditing = false;
        }

        setPromoAdapterSkeleton();

        // Inject dependencies
        injectDependency();

        onClickKontakKami();

        // Handle error messages and set dynamic screen image
        handleErrorMessage();
        setDynamicScreenImage();

        // Process Intent data
        handleIntentExtra();

        updateWidget(this);
        setDeepLinkWA();

        // Handle session and login flag
        if (activityPresenter != null && (menuCode == null || menuCode.isEmpty())) {
            activityPresenter.updateLoginFlag(false);
        }

        // Initialize biometric support
        GeneralHelper.checkBiometricSupport();
        BiometricUtility.INSTANCE.setBiometricType(this);

        // Set image URL
        if (activityPresenter != null) {
            imageUrlDecrypt = activityPresenter.getImageUrl();
        }

        checkNfcStatus();

        // Set up button login listeners
        setupButtonLogin();

        setupMainCardHeight();

        // Set up view menu
        setupViewMenu();

        // Set up welcoming text
        setupWelcomingText();

//        onSuccessGetPromo();
    }

    private void setupMainCardHeight(){
        RelativeLayout rlTopBanner = binding.rlTopBanner;
        DisplayMetrics displayMetrics = Resources.getSystem().getDisplayMetrics();
        int screenHeight = displayMetrics.heightPixels;
        int desiredHeight = (int) (screenHeight * 0.464f);

        ViewGroup.LayoutParams layoutParams = rlTopBanner.getLayoutParams();
        layoutParams.height = desiredHeight;
        rlTopBanner.setLayoutParams(layoutParams);

    }

    private void checkNfcStatus() {
        if (nfcPayment.isNfcAvailable()) {
            isNfcActive = nfcPayment.isNfcEnabled();
        }
    }

    private void handleIntentExtra() {
        Intent intent = getIntent();
        Bundle extras = intent.getExtras();
        if (extras != null) {
            if (activityPresenter.getLoginFlag()) {
                parseDataNotifFastMenu(intent);
            } else {
                typeFlagLimit = intent.getStringExtra(Constant.TYPE_FLAG);
            }
        }
    }


    /**
     * Func for get the initial height of Brimo Voice Assistant Constraint Layout
     */
    private void getInitialHeightClBrimoVoiceAssistant() {
//        binding.clVoiceAssistant.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
//            @Override
//            public void onGlobalLayout() {
//                initialHeightClBrimoVoiceAssistant = binding.clVoiceAssistant.getHeight();
//                binding.clVoiceAssistant.getViewTreeObserver().removeOnGlobalLayoutListener(this);
//            }
//        });
    }

    /**
     * Handle slide up BRImo Voice Assistant
     */
    @SuppressLint("ClickableViewAccessibility")
    private void handleSlideUpBRImoVoiceAssistant() {
//        binding.clVoiceAssistant.setOnTouchListener((view, motionEvent) -> {
//            switch (motionEvent.getAction()) {
//                case MotionEvent.ACTION_DOWN:
//                    initialYClBrimoVoiceAssistant = motionEvent.getRawY();
//                    return true;
//
//                case MotionEvent.ACTION_MOVE:
//                    isSlideUp(motionEvent.getRawY());
//                    return true;
//
//                case MotionEvent.ACTION_UP:
//                    if (binding.clVoiceAssistant.getHeight() >= initialHeightClBrimoVoiceAssistant * 1.25) {
//                        VoiceAssistantRevampActivity.Companion.launchIntent(this);
//                        isFromVoiceAssistant = true;
//                    } else {
//                        ViewGroup.LayoutParams params = binding.clVoiceAssistant.getLayoutParams();
//                        params.height = initialHeightClBrimoVoiceAssistant;
//                        binding.clVoiceAssistant.setLayoutParams(params);
//                    }
//                    return true;
//
//                default:
//                    return false;
//            }
//        });
    }

    private void isSlideUp(Float rawY) {
//        if (((initialYClBrimoVoiceAssistant - rawY) + initialHeightClBrimoVoiceAssistant) <= initialHeightClBrimoVoiceAssistant * 1.7 && binding.clVoiceAssistant.getHeight() >= initialHeightClBrimoVoiceAssistant) {
//            ViewGroup.LayoutParams params = binding.clVoiceAssistant.getLayoutParams();
//            params.height = (int) ((initialYClBrimoVoiceAssistant - rawY) + initialHeightClBrimoVoiceAssistant);
//            binding.clVoiceAssistant.setLayoutParams(params);
//        }
    }

    private void setViewVoiceAssistantMenu() {
//        if (activityPresenter.getAktivasiVoiceAssistant()) {
//            binding.clVoiceAssistant.setVisibility(View.VISIBLE);
//        } else {
//            binding.clVoiceAssistant.setVisibility(View.GONE);
//        }
    }

    private void handleBehaviorMenuVoiceAssistant() {
//        if (isFromVoiceAssistant) {
//            ViewGroup.LayoutParams params = binding.clVoiceAssistant.getLayoutParams();
//            params.height = initialHeightClBrimoVoiceAssistant;
//            binding.clVoiceAssistant.setLayoutParams(params);
//        } else {
//            getInitialHeightClBrimoVoiceAssistant();
//        }
    }

    private void onClickKontakKami() {
        binding.toolbar.ivCustomerCare.setOnClickListener(view -> {
            KontakKamiActivity.launchIntentFreshInstall(this, true, true);
        });
    }

    private void showMsgIdle() {
        OpenBottomSheetGeneralNewSkinFragment.INSTANCE.showDialogInformation(
                getSupportFragmentManager(),
                "", // imgPath
                "ic_clock_illustration", // imgName
                "Kamu Keluar dari Akunmu", // titleTxt
                "Kamu sudah tidak aktif selama 10 menit dan kami mengakhiri sesi loginmu untuk menjaga keamanan akunmu. Silakan login kembali.", // subTitleTxt
                () -> {
                    finish();
                    return Unit.INSTANCE;
                },

                true, // isClickableOutside
                "Login", // firstBtnTxt
                true,
                true,
                () -> {
                    return null;
                },
                false
        );

//        FragmentBottomSheetGeneralBinding viewBind = FragmentBottomSheetGeneralBinding.inflate(LayoutInflater.from(this));
//        BottomSheetCustomViewGeneralFragment bottomSheet = new BottomSheetCustomViewGeneralFragment(viewBind.getRoot(), true, true, () -> {
//            return null;
//        });
//        viewBind.tvTopTxt.setVisibility(View.GONE);
//        viewBind.view2.setVisibility(View.GONE);
//        viewBind.ivCenter.setImageResource(
//                GeneralHelper.getImageId(
//                        this, "ilustrasi_sesi_habis"
//                )
//        );
//        viewBind.tvTitle.setText(GeneralHelper.getString(R.string.sesi_habis));
//        viewBind.tvDesc.setText(GeneralHelper.getString(R.string.sesi_habis_desc));
//        viewBind.secondBtn.setVisibility(View.GONE);
//        viewBind.firstBtn.setText(GeneralHelper.getString(R.string.ok));
//        viewBind.firstBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                bottomSheet.dismissNow();
//            }
//        });
//
//        if (!getSupportFragmentManager().isStateSaved()) {
//            bottomSheet.show(getSupportFragmentManager(), "");
//        }
    }

    private void setupViewMenu() {
//        binding.llInfoFastMenu.setOnClickListener(view -> {
//            BottomFragmentInfoFastMenu fragment = new BottomFragmentInfoFastMenu();
//            fragment.show(getSupportFragmentManager(), "");
//        });
//
//        binding.llEditFast.setOnClickListener(view -> {
//            if (!activityPresenter.getUsernameAlias().equals("") && !activityPresenter.getUsernameAlias().isEmpty()) {
//                BottomFragmentLoginEditFastMenu fragmentLoginEditFastMenu = new BottomFragmentLoginEditFastMenu(
//                        this,
//                        mFastMenuList,
//                        mFastMenuDefaultList);
//                fragmentLoginEditFastMenu.show(getSupportFragmentManager(), "");
//            } else {
//                showSnackbarErrorMessageRevamp(GeneralHelper.getString(R.string.alert_login_edit_fast_menu), ALERT_ERROR, this, true);
//            }
//        });
    }

    private void setupWelcomingText() {
        binding.tvName.setText(String.format("Hai, %s", activityPresenter.getNameOfUser()));
    }

    /**
     * Handle data exception untuk menampilkan alert dan snackbar error
     */
    private void handleErrorMessage() {
        if (mDesc != null) {
            onException(mDesc);
            mDesc = null;
        } else if (successMessage != null) {
            showSnackbarErrorMessageRevamp(successMessage, ALERT_CONFIRM, this, false);
            successMessage = null;
        } else if (desc99 != null) {
            dialogSetDefault = new DialogSetDefault(this, GeneralHelper.getString(R.string.maaf_sistem_sedang_sibuk), desc99, GeneralHelper.getString(R.string.ok), GeneralHelper.getString(R.string.batal2), Constant.DIALOG_LOGIN);
            FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
            ft.add(dialogSetDefault, null);
            ft.commitAllowingStateLoss();
        }
    }

    private void setDeepLinkWA() {
        Uri uri = getIntent().getData();
        if (uri != null) {
            String url = uri.getQuery();
            menuCode = url.substring(url.indexOf("=") + 1);
        }

        if (!menuCode.isEmpty() && activityPresenter.getLoginFlag()) {
            activityPresenter.updateLoginFlag(true);
            Intent intentLogin = new Intent(FastMenuNewSkinActivity.this, DashboardIBActivity.class);
            intentLogin = addIntentNotifikasiTrx(intentLogin);
            intentLogin.putExtra(Constant.LINK_CODE, menuCode);
            startActivity(intentLogin);
            finish();
        }
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (activityPresenter != null) {
            activityPresenter.setView(this);
            activityPresenter.setUrlValidate(GeneralHelper.getString(R.string.url_validate_top_up_brizzi_fm));
            activityPresenter.setUrlReadNotif(GeneralHelper.getString(R.string.url_notification_read_fast_menu));
            activityPresenter.setUrlSafetyMode(GeneralHelper.getString(R.string.url_safety_mode_fm));
            activityPresenter.setUrlLogin(GeneralHelper.getString(R.string.url_login_v5));
            activityPresenter.setUrlChange(GeneralHelper.getString(R.string.url_change_device_v2));
            activityPresenter.setUrlPrefrences(GeneralHelper.getString(R.string.url_prefrences_bilingual_fast_menu));
            activityPresenter.setUrlPromo(GeneralHelper.getString(R.string.url_promo_non_featured));
            activityPresenter.checkValidateBRIZZI();
            activityPresenter.getSafetyMode();
            activityPresenter.getDefaultFastMenu();
            activityPresenter.getSavedFastMenu();
            activityPresenter.getPromo();
            activityPresenter.setDetailItemPromoUrl(GeneralHelper.getString(R.string.url_detail_promo));
            activityPresenter.checkAvailabilityNfc(nfcPayment.isNfcAvailable());
            activityPresenter.start();
        }
    }

    @Override
    public String getUrl() {
        return mainUrl;
    }

    @Override
    public void onSubmitSuccess(FastMenuResponse response) {
        if (!mainUrl.equalsIgnoreCase(""))
            mainUrl = response.getUrl();
    }

    private void setupButtonLogin() {
        boolean isBioUpdated = Boolean.TRUE.equals(activityPresenter.getStatusUpdateBio());
        boolean hasBiometricEnrolled = BiometricUtils.Companion.hasBiometricEnrolled(getBaseContext());
        isActivasi = Boolean.TRUE.equals(activityPresenter.getStatusAktivasi());

        if (isActivasi) {
            Drawable icon = ContextCompat.getDrawable(this, R.drawable.ic_fingerprint_new_skin);
            binding.btnLogin.setIcon(icon);
            binding.btnLogin.setIconGravity(MaterialButton.ICON_GRAVITY_TEXT_END);
        }

        binding.btnLogin.setOnClickListener(view -> {
            promoId="";
            boolean isLockedPermanently = BiometricUtility.INSTANCE.isFingerprintLockedPermanently();

            if (isLockedPermanently) {
                // Jika fingerprint terkunci permanen, langsung pakai password
                goToInputPassword();
                return;
            }
            if (!isBioUpdated) {
                if (hasBiometricEnrolled) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        BiometricUtility.INSTANCE.setBiometricTypeFinger(this);
                        BiometricUtility.INSTANCE.displayPromptForEnroll(this, false);
                    } else {
                        goToInputPassword();
                    }
                }
            } else if (isBioChanged && !activityPresenter.isBioChangedDialogShown()) {
                openBioChanged();
            } else {
                if (hasBiometricEnrolled) {
                    if (isActivasi) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            BiometricUtility.INSTANCE.displayPromptForLogin(this);
                        }
                    } else {
                        goToInputPassword();
                    }
                } else {
                    activityPresenter.updateStatusAktivasi(false);
                    goToInputPassword();
                }
            }
        });

        if (isIdleDevice) {
            showMsgIdle();
            isIdleDevice = false;
        }

//        setViewVoiceAssistantMenu();
//        binding.btnLogin.setOnClickListener(view -> callFragmentLogin());

//        handleSlideUpBRImoVoiceAssistant();
        initSwitchButton();

//        handleSlideUpBRImoVoiceAssistant();
        requestPermission(this, PERMISSIONS_MAP);
        showSuccessSnackbarChangeLanguage();
    }


    @Override
    protected void onResume() {
        super.onResume();
        //menampilkan snackbar error
        handleErrorMessage();

        if (activityPresenter != null) {
            activityPresenter.setDisablePopup(false);
            activityPresenter.setUrlLogin(GeneralHelper.getString(R.string.url_login_v5));
            activityPresenter.setUrlLogout(GeneralHelper.getString(R.string.url_logout_v5));
            activityPresenter.setUrlChange(GeneralHelper.getString(R.string.url_change_device_v2));
            activityPresenter.getDefaultFastMenu();
            activityPresenter.getSavedFastMenu();
            activityPresenter.checkAvailabilityNfc(nfcPayment.isNfcAvailable());
        }

        handleBehaviorMenuVoiceAssistant();

        checkBioChanged();
    }


    @Override
    public void onInitiateResourceSuccess(String username, String tokenKey, String nama) {
        mUsername = username;
        mTokenKey = tokenKey;
        mNama = nama;
    }

    @Override
    public void onSuccessTimerSafetyMode(SafetyModeResponse safetyModeResponse) {
//        try {
//            int ttl = Integer.parseInt(safetyModeResponse.getTtl());
//            binding.safetyMode.rlSafetyMode.setVisibility(ttl == 0 ? View.GONE : View.VISIBLE);
//            if (ttl != 0) {
//                setTextTimer(ttl);
//                binding.safetyMode.tvDescSafety.setText(safetyModeResponse.getDesc());
//                binding.safetyMode.imgTanyaSafety.setVisibility(View.GONE);
//            }
//        } catch (NumberFormatException e) {
//            binding.safetyMode.rlSafetyMode.setVisibility(View.GONE);
//        }

        handleAlertMaintenance(safetyModeResponse);
    }

    private void handleAlertMaintenance(SafetyModeResponse response) {
//        AlertMaintenance alert = response.getAlertMaintenance();
//        boolean hasAlert = alert != null && !isAlertMaintenanceEmpty(alert);
//
//        binding.alertMaintenance.clAlert.setVisibility(hasAlert ? View.VISIBLE : View.GONE);
//
//        if (hasAlert) {
//            binding.alertMaintenance.clAlert.bringToFront();
//            binding.alertMaintenance.tvWarningTitle.setText(alert.getTitle());
//            binding.alertMaintenance.tvWarningSubtitle.setText(alert.getSubTitle());
//        }
//
//        binding.alertMaintenance.ivClose.setOnClickListener(view -> binding.alertMaintenance.clAlert.setVisibility(View.GONE));
    }

    private boolean isAlertMaintenanceEmpty(AlertMaintenance alert) {
        return (alert.getTitle() == null || alert.getTitle().isEmpty()) &&
                (alert.getSubTitle() == null || alert.getSubTitle().isEmpty());
    }

    @Override
    public void onSafetyMode01() {
//        binding.safetyMode.rlSafetyMode.setVisibility(View.GONE);
//        binding.safetyMode.imgTanyaSafety.setVisibility(View.GONE);
    }

    private void setTextTimer(int timer) {
//        int countDown = SECOND * timer;
//        countDownTimer = new CountDownTimer(countDown, SECOND) {
//
//            @SuppressLint("DefaultLocale")
//            public void onTick(long millisUntilFinished) {
//                int seconds = (int) millisUntilFinished / SECOND;
//
//                binding.safetyMode.timerSafetyMode.setText(String.format("%02d : %02d : %02d",
//                        seconds / 3600, (seconds % 3600) / 60, (seconds % 60)));
//            }
//
//            public void onFinish() {
//                binding.safetyMode.rlSafetyMode.setVisibility(View.GONE);
//            }
//
//        }.start();
    }

    @Override
    public void onLoginSuccess() {
        activityPresenter.updateStatusUpdateBio(true);
        activityPresenter.updateStatusAktivasi(true);
        fromFastMenuReminders = getIntent().getBooleanExtra("fromFastMenuReminder", fromFastMenuReminders);

        if (fromFastMenuReminders) {
            parseDataNotifFastMenu(getIntent());
        } else {
            if (fromFastMenus) {
                MutasiActivity.launchIntentLogin(this, fromFastMenus);
            }else if (!promoId.isEmpty()){
                activityPresenter.getDetailPromoItem(promoId);

//                Intent intentLogin = new Intent(FastMenuNewSkinActivity.this, DashboardIBActivity.class);
//                intentLogin = addIntentNotifikasiTrx(intentLogin);
//                intentLogin.putExtra(Constant.LINK_CODE, menuCode);
//                intentLogin.putExtra(Constant.TYPE_FLAG, typeFlagLimit);
//                startActivity(intentLogin);
//                finish();
            }else {
                Intent intentLogin = new Intent(FastMenuNewSkinActivity.this, DashboardIBActivity.class);
                intentLogin = addIntentNotifikasiTrx(intentLogin);
                intentLogin.putExtra(Constant.LINK_CODE, menuCode);
                intentLogin.putExtra(Constant.TYPE_FLAG, typeFlagLimit);
                startActivity(intentLogin);
                finish();
            }
        }
    }

    @Override
    public void onSubmitLoginSuccess(LoginResponse loginResponse) {
        if (loginResponse.isPinReady())
            KonfirmasiPinActivity.launchIntentPersistant(this, loginResponse.getRefNum());
        else if (loginResponse.getType().equalsIgnoreCase(GeneralHelper.getString(R.string.otp)))
            OtpSmsActivity.launchIntent(this, loginResponse);
        else
            WaitingActivity.launchIntent(this, true, loginResponse.getOtpExpiredInSecond(), loginResponse.getRefNum());
    }

    @Override
    public void onDeviceChanged(String desc, LoginResponse loginResponse) {
        isPinReady = loginResponse.isPinReady();
        refNum = loginResponse.getRefNum();
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this, GeneralHelper.getString(R.string.selamat_datang_kembali), desc);
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onCreatePin() {
        BuatPinActivity.launchIntent(this);
    }

    @Override
    public void onChangeUsername(String deskripsi) {
        Bundle bundle = new Bundle();
        bundle.putString(Constant.DESCRIPTION, deskripsi);
        Intent intentEditUsername = new Intent(this, EditUsernameActivity.class);

        intentEditUsername.putExtras(bundle);
        startActivity(intentEditUsername);
        finish();
    }

    @Override
    public void onLogout(String message) {
        showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false);
    }

    @Override
    public void setVisibleFingerprint(boolean visibility) {
//        isBiometricEnabled = visibility;
//        if (visibility) {
//            binding.btnLogin.setCompoundDrawablesWithIntrinsicBounds(R.drawable.fingerprint, 0, 0, 0);
//            binding.btnLogin.setCompoundDrawablePadding((int) getResources().getDimension(R.dimen.space_x2));
//        } else {
//            binding.btnLogin.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
//        }
    }

    @Override
    public void onChangeDevice(Integer otpExpiredSeconds) {
        WaitingActivity.launchIntent(this, true, otpExpiredSeconds, refNum);
    }

    @Override
    public void onException12() {

    }

    @Override
    public void onChangeMNV(String desc, LoginResponse loginResponse) {
        isPinReady = loginResponse.isPinReady();
        refNum = loginResponse.getRefNum();
        isMNV = true;
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this,
                GeneralHelper.getString(R.string.title_dialog_login_mnv), desc,
                GeneralHelper.getString(R.string.btnBatal), GeneralHelper.getString(R.string.lanjutkan));
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onExceptionLoginExceed(ExceptionResponse response) {
        GeneralHelper.showBlockedAccountDialog(
                getSupportFragmentManager(),
                response,
                submitBtnAction,
                cancelBtnAction);
    }

    @Override
    public void onClickYes() {
        if (isMNV) {
            PinFragment pinFragment = new PinFragment(this, this);
            pinFragment.show();
        } else if (isPinReady)
            KonfirmasiPinActivity.launchIntent(this);
        else
            activityPresenter.changeDevice(refNum);
    }

//    private void openInfoBiometric() {
//        String title = String.format(GeneralHelper.getString(R.string.title_info_biometric_off),
//                activityPresenter.getBioType());
//        String subtitle = String.format(GeneralHelper.getString(R.string.desc_info_biometric_off),
//                activityPresenter.getBioType());
//
//        BottomSheet2ButtonLogin bottomSheet2ButtonLogin = BottomSheet2ButtonLogin.Companion.newInstance(
//                GeneralHelper.getString(R.string.image_gagal_brimo),
//                title,
//                subtitle,
//                "",
//                "",
//                this, true);
//        bottomSheet2ButtonLogin.setCancelable(false);
//        bottomSheet2ButtonLogin.show(getSupportFragmentManager(), "");
//    }

//    private void openBioChanged() {
//        OpenBottomSheetGeneralNewSkinFragment.INSTANCE.showDialogInformation(
//                getSupportFragmentManager(),
//                "", // imgPath
//                "ic_sad_illustration", // imgName
//                "Terjadi Perubahan Biometrik", // titleTxt
//                "Tenang, kamu bisa atur kok. Sekarang login dulu menggunakan password, lalu atur lewat Pengaturan di menu Akun.", // subTitleTxt
//                () -> {
//                    goToInputPassword();
//                    return Unit.INSTANCE;
//                },
//
//                true, // isClickableOutside
//                "Mengerti", // firstBtnTxt
//                true,
//                true,
//                () -> {
//                    goToInputPassword();
//                    return Unit.INSTANCE;
//                }
//        );
//
//
////        String title = String.format(GeneralHelper.getString(R.string.title_bio_change),
////                activityPresenter.getBioType());
////
////        BottomSheet2ButtonLogin bottomSheet2ButtonLogin = BottomSheet2ButtonLogin.Companion.newInstance(
////                GeneralHelper.getString(R.string.image_gagal_brimo),
////                title,
////                getString(R.string.desc_bio_change),
////                "",
////                "",
////                this, true);
////        bottomSheet2ButtonLogin.setCancelable(false);
////        bottomSheet2ButtonLogin.show(getSupportFragmentManager(), "");
//    }

    private void checkBioChanged(){
        BiometricUtility.INSTANCE.checkBioChange(this);

        isBioChanged = Boolean.TRUE.equals(activityPresenter.getBioChanged());
        isActivasi = Boolean.TRUE.equals(activityPresenter.getStatusAktivasi());

        if (isBioChanged) {
            binding.btnLogin.setIcon(null);
        }
    }

    private void openBioChanged() {
        boolean isDialogShown = activityPresenter.isBioChangedDialogShown();

        if(!isDialogShown){
            try {
                activityPresenter.setBioChangedDialogShown(true); // Jangan tampil lagi
                OpenBottomSheetGeneralNewSkinFragment.INSTANCE.showDialogInformation(
                        getSupportFragmentManager(),
                        "", // imgPath
                        "ic_sad_illustration", // imgName
                        "Terjadi Perubahan Biometrik", // titleTxt
                        "Tenang, kamu bisa atur kok. Sekarang login dulu menggunakan password, lalu atur lewat Pengaturan di menu Akun.", // subTitleTxt
                        () -> {
                    goToInputPassword();
                            return Unit.INSTANCE;
                        },
                        true,
                        "Login dengan Password",
                        true,
                        true,
                        () -> {
                            return Unit.INSTANCE;
                        },
                        false
                );
            } catch (Exception e) {
                Log.e(TAG, "ERROR BOS: "+new Gson().toJson(e));
            }
        }
    }

    @Override
    public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
        switch (errorCode) {
            case Constant.ERROR_NEGATIVE_BUTTON:
                if (promoId.isEmpty()){
                    goToInputPassword();
                }else{
                    goToInputPasswordPromo(promoId);
                }
                break;
            case Constant.ERROR_USER_CANCELED:
                if (promoId.isEmpty()){
                    goToInputPassword();
                }else{
                    goToInputPasswordPromo(promoId);
                }
                break;
            case Constant.ERROR_CANCELED:
                if (promoId.isEmpty()){
                    goToInputPassword();
                }else{
                    goToInputPasswordPromo(promoId);
                }
                break;
            case Constant.ERROR_LOCKOUT:
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                    return;
                }
                mLastClickTime = SystemClock.elapsedRealtime();

                OpenBottomSheetGeneralNewSkinFragment.INSTANCE.showDialogInformation(
                        getSupportFragmentManager(),
                        "", // imgPath
                        "ic_sad_illustration", // imgName
                        "Terlalu Banyak Percobaan Gagal", // titleTxt
                        "Silakan login menggunakan password, ya.", // subTitleTxt
                        () -> {
                            if (promoId.isEmpty()){
                                goToInputPassword();
                            }else{
                                goToInputPasswordPromo(promoId);
                            }
                            return Unit.INSTANCE;
                        },

                        true, // isClickableOutside
                        "Login dengan Password", // firstBtnTxt
                        true,
                        true,
                        () -> {
                            return null;
                        },
                        false
                );

                break;
        }
    }

    @Override
    public void onAuthenticationSuccess() {
        runOnUiThread(() -> activityPresenter.loginFingerprint());
    }

    @Override
    public void onBiometricChanged() {
//        openBioChanged();
    }

    @Override
    public void onSendPinComplete(String pin) {
        if (showPinFromNFC) {
            activityPresenter.getDataPayloadNfc(pin);
            showPinFromNFC = false;
        } else {
            LoadingMNVActivity.launchIntent(this, pin, refNum);
        }
    }

    @Override
    public void onLupaPin() {
        if (showPinFromNFC) {
            LupaPinFastActivity.launchIntent(this);
            showPinFromNFC = false;
        } else {
            DialogInformation dialog = new DialogInformation(this, "ic_forbidden_lupa_pin",
                    GeneralHelper.getString(R.string.title_maaf_lupa_pin),
                    GeneralHelper.getString(R.string.desc_maaf_lupa_pin_username),
                    GeneralHelper.getString(R.string.ok),
                    this, true, false);
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
            ft.add(dialog, null);
            ft.commitAllowingStateLoss();
        }
    }

    @Override
    public void onClickAction() {
        //do nothing
    }

//    @Override
//    public void onException(String message) {
//        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
//            GeneralHelperNewSkin.INSTANCE.showErrorBottomDialog(this, message);
//        else {
//            GeneralHelperNewSkin.INSTANCE.showGeneralErrorBottomDialog(this);
//        }
//    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        activityPresenter.stop();
        fromFastMenuReminders = false;
        notifikasiModel = null;
        menuCode = "";
        typeFlagLimit = "";
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
//        if (data == null && requestCode != Constant.REQ_UBAH_PIN) return;
        switch (requestCode) {
            case Constant.REQ_PAYMENT:
                if (resultCode != RESULT_OK && data != null) {
                    String errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE);
                    if (errorMessage != null) {
                        onException(errorMessage);
                    }
                }
                break;

            case Constant.REQ_CASHBACK:
                if (resultCode == RESULT_OK && data != null) {
                    menuCode = data.getStringExtra(Constant.TAG_VALUE);
                }
                break;

            case Constant.REQ_MNV:
                if (resultCode == RESULT_CANCELED && data != null) {
                    GeneralHelper.showSnackBarRevamp(
                            findViewById(R.id.content),
                            data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                    );
                }
                break;

            case Constant.REQ_UBAH_PIN, Constant.REQ_FORGOT_PIN:
                if (resultCode == Activity.RESULT_OK) {
                    showSnackbarErrorMessageRevamp(
                            getString(R.string.txt_pin_berhasil_diubah),
                            ALERT_CONFIRM,
                            this,
                            false
                    );
                }
                break;

            case Constant.REQ_FORGOT_PASS, Constant.REQ_UBAH_KATA_KUNCI:
                if (resultCode == Activity.RESULT_OK) {
                    showSnackbarErrorMessageRevamp(
                            getString(R.string.txt_password_berhasil_diubah),
                            ALERT_CONFIRM,
                            this,
                            false
                    );
                }
                break;

            case Constant.REQ_EDIT_SAVED:
                handleEditSavedResult(resultCode, data);
                break;

            default:
                break;
        }
    }

    private void handleEditSavedResult(int resultCode, @Nullable Intent data) {
        if (data == null) return;

        switch (resultCode) {
            case Constant.RESULT_BACK:
                showSnackbarErrorMessageRevamp(
                        getResources().getString(R.string.fast_menu_sedang_disesuaikan),
                        ALERT_CONFIRM,
                        this,
                        false
                );
                break;

            case Constant.RESULT_KENDALA_SISTEM:
                showSnackbarErrorMessageRevamp(
                        data.getStringExtra(Constant.KENDALA_SISTEM),
                        ALERT_ERROR,
                        this,
                        false
                );
                break;

            case Constant.RESULT_CODE_09_CHANGE_DEVICE:
                showSnackbarErrorMessageRevamp(
                        data.getStringExtra(Constant.EDIT_FAST_MENU_AFTER_CHANGE_DEVICE),
                        ALERT_ERROR,
                        this,
                        false
                );
                break;
        }
    }

    private void parseDataNotifFastMenu(Intent intent) {
        if (intent == null || intent.getExtras() == null) return;

        notifikasiString = intent.getExtras().getString(Constant.TAG_NOTIF);
        if (notifikasiString == null || notifikasiString.isEmpty()) return;

        try {
            notifikasiModel = new Gson().fromJson(notifikasiString, NotifikasiModel.class);
            if (notifikasiModel == null) return;

            String notifType = notifikasiModel.getType();
            String blastId = notifikasiModel.getBlastId();

            switch (notifType) {
                case Constant.PROMO_BLAST_TYPE:
                    handlePromoBlast(blastId);
                    break;
                case Constant.INFO_BLAST_TYPE:
                    dialogPromo();
                    directToDashboard();
                    break;
                default:
                    directToDashboard();
                    break;
            }

            if (!GeneralHelper.isProd()) {
                Log.d(TAG, "parseDataNotifFastMenu: " + notifType);
            }

        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "parseDataNotif: ", e);
            }
        }
    }

    private void handlePromoBlast(String blastId) {
        if ("0".equalsIgnoreCase(blastId)) {
            dialogPromo();
        } else {
            Intent intent1 = new Intent(FastMenuNewSkinActivity.this, DetailPromoActivity.class);
            intent1.putExtra(Constant.TAG_NOTIF, notifikasiString);
            startActivity(intent1);
        }
    }

    private void directToDashboard() {
        Intent intent1 = new Intent(FastMenuNewSkinActivity.this, DashboardIBActivity.class);
        intent1.putExtra(TAG_NOTIF, notifikasiString);
        intent1.putExtra("fromFastMenuReminder", true);
        intent1.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent1);
        finish();
    }

    public void dialogPromo() {
        activityPresenter.getReadNotifFastMenu(notifikasiModel.getBlastId());
    }

    @Override
    protected void onClickYesRC99(int reqId) {
        if (reqId != Constant.DIALOG_LOGIN) return;

        try {
            String url = MyCryptStatic.decryptAsBase64(BuildConfig.M_RESET_URL);
            if (url != null) {
                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(url)));
            }
        } catch (IOException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "onClickYesRC99: ", e);
            }
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        setDeepLinkWA();
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected void onStop() {
        super.onStop();
        notifikasiModel = null;
        menuCode = "";
        typeFlagLimit = "";
    }

    @Override
    protected void onPause() {
        super.onPause();
        notifikasiModel = null;
        menuCode = "";
        typeFlagLimit = "";
    }

    private Intent addIntentNotifikasiTrx(Intent newIntent) {
        if (notifikasiModel != null && newIntent != null) {
            String type = notifikasiModel.getType();
            String notifikasiString = null;

            switch (type) {
                case Constant.BlastType.BIRTHDAY_GREETINGS:
                case Constant.BlastType.REMINDER:
                case Constant.BlastType.PROMO:
                case Constant.BlastType.INCOME_TRANSACTION_PFM:
                case Constant.BlastType.AFT_NOTIF_SCHEDULER_BLAST_TYPE:
                    notifikasiString = new Gson().toJson(notifikasiModel);
                    break;
                case Constant.BlastType.RECOMENDATION:
                case Constant.BlastType.TARTUN_NDS:
                    // notifikasiString is already null
                    break;
            }

            if (notifikasiString != null) {
                newIntent.putExtra(Constant.TAG_NOTIF, notifikasiString);
            }
        }
        notifikasiModel = null;
        return newIntent;
    }

//    private void callFragmentLogin() {
//        fragmentLogin = BottomFragmentLogin.newInstance(menuCode, typeFlagLimit, location);
//        if (!getSupportFragmentManager().isStateSaved()) {
//            fragmentLogin.show(getSupportFragmentManager(), "");
//        }
//    }

    private void goToInputPassword() {
//        FormPulsaDataReskinActivity.launchIntent(this, isFromFastMenu);
        Intent intent = InputPasswordNewSkinActivity.launchIntent(this, location);
        startActivityForResult(intent, Constant.REQ_FORGOT_PASS);
    }

    private void goToInputPasswordPromo(String promoId) {
        Intent intent = InputPasswordNewSkinActivity.launchIntent(this, location);
        intent.putExtra("ID_PROMO_FAST_MENU", promoId);
        startActivityForResult(intent, Constant.REQ_FORGOT_PASS);
    }

    private void getLocation() {
        GpsTracker gpsTracker = new GpsTracker(this);
        if (gpsTracker.canGetLocation()) {
            location = gpsTracker.getLatitude() + "," + gpsTracker.getLongitude();
            activityPresenter.getLocation(location);
        }
    }

    protected void requestPermission(Activity activity, String... permissions) {
        if (!hasPermissions(activity, permissions)) {
            ActivityCompat.requestPermissions(activity, permissions, Constant.REQUEST_LOCATION_MAP);
        } else {
            getLocation();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        try {
            if (requestCode == Constant.REQUEST_LOCATION_MAP) {
                getLocation();
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "onRequestPermissionsResult: ", e);
            }
        }
    }

    private void initSwitchButton() {
        binding.toolbar.switchButton.setText(LocaleUtilKt.getSavedLanguage(this));
        binding.toolbar.switchButton.setOnClickListener(() -> {
            new BilingualUI(this, getSupportFragmentManager(), LocaleUtilKt.getSavedLanguage(this), (value) -> {
                activityPresenter.updatePrefrencesLanguage(value);
                return Unit.INSTANCE;
            });
            return null;
        });
    }

    @Override
    public void onSuccessChangeLanguage() {
        recreatedActivity();
    }

    public void showSuccessSnackbarChangeLanguage() {
        if (getIntent().getBooleanExtra(Constant.IS_CHANGE_LANGUAGE, false)) {
            GeneralHelperNewSkin.INSTANCE.showSnackBarCustom(this, GeneralHelper.getString(R.string.txt_language_change_successfully), SnackBarType.SUCCESS, SnackBarPosition.TOP);
        }
        getIntent().removeExtra(Constant.IS_CHANGE_LANGUAGE);
    }

    @Override
    public void onMaintenanceAlert(MaintenanceAlert response) {
        goToMaintenanceAlertView(response);
    }

    @Override
    public void onInitiateMenu(List<FastMenu> menuModelList) {
        // Do Nothing
    }

    private void setDynamicScreenImage() {
//        imageUrlDecrypt = activityPresenter.getImageBannerUrlLocal();
//        title = activityPresenter.getBannerTitleLocal();
//        binding.tvDynamic.setText(title);
//
//        if (imageUrlDecrypt == null || imageUrlDecrypt.isEmpty()) {
//            Glide.with(this).load(R.drawable.slice_brimo_default).into(binding.imageViewDynamic);
//        } else {
//            Glide.with(this).load(imageUrlDecrypt).into(binding.imageViewDynamic);
//        }
//
//        // Always fetch the latest banner URL
//        activityPresenter.getImageBannerUrl();
    }

    @Override
    public void onInitiateMenuRevamp(List<FastMenuDefault> menuModelListDefault) {
        try {
            // Setup RecyclerView
            binding.rvFastMenu.setLayoutManager(new GridLayoutManager(this, 5, GridLayoutManager.VERTICAL, false));

            // Setup adapter
            adapter = new ListHomeAdapter(this, menuModelListDefault, this);
            binding.rvFastMenu.setAdapter(adapter);

            // Notify adapter of data changes
            adapter.notifyDataSetChanged();
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e("testdebug09374", "onInitiateMenuRevamp: ", e);
            }
        }
    }

    @Override
    public void onMenuLessThan3() {
//        binding.clSeekbar.setVisibility(View.GONE);
//        binding.hsvFastMenu.setOnTouchListener((view, motionEvent) -> true);
    }

    @Override
    public void onMenuMoreThan3() {
//        binding.clSeekbar.setVisibility(View.VISIBLE);
//        binding.hsvFastMenu.setOnScrollChangeListener((view, i, i1, i2, i3) -> {
//            int max = binding.hsvFastMenu.getChildAt(0).getWidth() - binding.hsvFastMenu.getWidth();
//            binding.sbCustom.setMax(max);
//            binding.sbCustom.setProgress(i);
//        });
    }

    @Override
    public void onSuccessGetNfcPayload(NfcPayloadResponse response) {
        NfcPaymentActivity.launchIntent(this, response, true);
    }

    @Override
    public void onBannerImageUrlSuccess(ImageBannerResponse response) {
//        imageUrlDecrypt = activityPresenter.getImageBannerUrlLocal();
//        title = activityPresenter.getBannerTitleLocal();
//        binding.tvDynamic.setText(title);
//        if (imageUrlDecrypt.isEmpty() && imageUrlDecrypt.equals("")) {
//            Glide.with(this).load(R.drawable.slice_brimo_default).into(binding.imageViewDynamic);
//        } else {
//            Glide.with(this).load(imageUrlDecrypt).into(binding.imageViewDynamic);
//        }
    }

    @Override
    public void onBannerImageUrlFailed() {
//        runOnUiThread(() -> {
//            Glide.with(this).load(R.drawable.slice_brimo_default).into(binding.imageViewDynamic);
//            binding.tvDynamic.setText(GeneralHelper.getString(R.string.hallo));
//        });
    }

    @Override
    public void onLoadDefaultFastmenu(List<FastMenu> fastMenuList) {
        this.mFastMenuList = fastMenuList;
    }

    @Override
    public void onMenuClick(int id) {
        switch (id) {
            case MenuConfig.MenuId.MENU_PULSA:
                FormPulsaDataReskinActivity.launchIntent(this, true);
                break;
            case MenuConfig.MenuId.MENU_BRIVA:
                Intent intentBriva = new Intent(this, PaymentActivity.class);
                intentBriva.putExtra("destination", "briva_form");
                intentBriva.putExtra("fromfastmenu", true);
                startActivity(intentBriva);
                break;
            case Constant.MENU_DOMPET_DIGITAL:
                FormDompetDigitalReskinActivity.launchIntent(this, true);
                break;
            case Constant.MENU_TRANSFER:
                Intent intentTransfer = new Intent(this, PaymentActivity.class);
                intentTransfer.putExtra("destination", "transfer_form");
                intentTransfer.putExtra("fromfastmenu", true);
                startActivity(intentTransfer);
                break;
            case MenuConfig.MenuId.MENU_BRIZZI:
                Intent intentBrizzi = new Intent(this, PaymentActivity.class);
                intentBrizzi.putExtra("destination", "brizzi_form");
                intentBrizzi.putExtra("fromfastmenu", true);
                startActivity(intentBrizzi);
                break;
            case MenuConfig.MenuId.MENU_QR:
                FormQrActivity.launchIntent(this, true);
                break;
            case MenuConfig.MenuId.MENU_QR_MCM:
                Intent intentQr = new Intent(this, PaymentActivity.class);
                intentQr.putExtra("destination", "qris_scan");
                intentQr.putExtra("fromfastmenu", true);
                startActivity(intentQr);
                break;
            case Constant.MENU_PROMO:
                AllPromoActivity.launchIntent(this, fromFastMenu);
                break;
            case Constant.MENU_PFM:
                CatatanKeuanganActivity.launchIntent(this, false, true, null, false, MenuConfig.PFMMenuFragment.PFM_OUTCOME);
                break;
            case MenuConfig.MenuId.MENU_SETOR_TUNAI:
                FormSetorTunaiActivity.launchIntent(this, true);
                break;
            case MenuConfig.MenuId.MENU_NFC:
                if (isNfcActive) {
                    showInputPin();
                } else {
                    showBottomSheetActiveInActiveNfc();
                }
                break;
            default:
                GeneralHelper.showBottomDialog(this, Constant.COMING_SOON);
        }
    }

    private void showInputPin() {
        if (nfcPayment.isDefaultService()) {
            showPinFromNFC = true;
            PinFragment pinFragment = new PinFragment(this, this);
            pinFragment.show();
        } else {
            nfcPayment.changeDefaultPaymentService(this);
        }
    }

    private void showBottomSheetActiveInActiveNfc() {
        BottomSheetDialogGeneral bottomSheetDialogGeneral = new BottomSheetDialogGeneral.Builder()
                .setType(BottomSheetDialogType.SINGLE_BUTTON)
                .setImageResId(R.drawable.ic_activate_nfc)
                .setTitle(GeneralHelper.getString(R.string.txt_masalah_pada_nfc))
                .setDescription(GeneralHelper.getString(R.string.desc_masalah_pada_nfc))
                .setPositiveText(GeneralHelper.getString(R.string.txt_aktifkan_nfc))
                .setConfirmButtonVerticalDrawable(R.drawable.button_primary_bg)
                .setConfirmButtonVerticalStyle(R.style.CustomButtonPrimaryStyle, Typeface.BOLD)
                .setConfirmButtonVerticalTextColor(R.color.whiteColor)
                .setOnPositiveButtonClickListener(firstBtnFunction)
                .setCancelable(true)
                .setOnCanceledOnTouchOutside(true)
                .build();

        if (!getSupportFragmentManager().isStateSaved()) {
            bottomSheetDialogGeneral.show(getSupportFragmentManager(), bottomSheetDialogGeneral.getTag());
        }

    }

    private final Function1<BottomSheetDialogGeneral, Unit> firstBtnFunction = bottomSheetDialogGeneral -> {
        nfcSettingsContract.launch(Unit.INSTANCE);
        return Unit.INSTANCE; // Ensure the return type is Unit
    };

    private void setPromoAdapter(List<DetailPromoResponse> promo){
        promoAdapter = new PromoFastMenuNewSkinAdapter(this, promo, this, false);
        binding.rvPromo.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        binding.rvPromo.setHasFixedSize(true);
        binding.rvPromo.setAdapter(promoAdapter);
    }

    private void setPromoAdapterSkeleton() {
        promoAdapter = new PromoFastMenuNewSkinAdapter(this, new ArrayList<>(), this, true);
        binding.rvPromo.setLayoutManager(new LinearLayoutManager(this));
        binding.rvPromo.setAdapter(promoAdapter);
    }

        @Override
    public void onAuthenticationFailed() {
    }

//    @Override
//    public void onClickOk() {
//        // Do Nothing
//    }
//
//    @Override
//    public void onClickCancel() {
//        // Do Nothing
//    }

    @Override
    public void onSuccessGetPromo(@NonNull PromoResponse response) {
        setPromoAdapter(response.getPromo());
    }

    public void onSuccessGetPromo() {
        Type listType = new TypeToken<List<DetailPromoResponse>>() {}.getType();

        List<DetailPromoResponse> promoResponse = new Gson().fromJson(mockResponse, listType);
        setPromoAdapter(promoResponse);
    }

    @Override
    public void showPromo(boolean isVisible) {
        runOnUiThread(() -> binding.rvPromo.setVisibility(isVisible ? View.VISIBLE : View.GONE));
    }

    @Override
    public void onClickPromoItem(@NonNull DetailPromoResponse promoResponse) {
        boolean isLockedPermanently = BiometricUtility.INSTANCE.isFingerprintLockedPermanently();
        boolean isBioUpdated = Boolean.TRUE.equals(activityPresenter.getStatusUpdateBio());
        boolean isBioChanged = Boolean.TRUE.equals(activityPresenter.getBioChanged());
        boolean hasBiometricEnrolled = BiometricUtils.Companion.hasBiometricEnrolled(getBaseContext());
        boolean isActivasi = Boolean.TRUE.equals(activityPresenter.getStatusAktivasi());
        promoId = promoResponse.getId();


        if (isLockedPermanently) {
            // Jika fingerprint terkunci permanen, langsung pakai password
            goToInputPasswordPromo(promoResponse.getId());
            return;
        }

        if (!isBioUpdated) {
            if (hasBiometricEnrolled) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    BiometricUtility.INSTANCE.setBiometricTypeFinger(this);
                    BiometricUtility.INSTANCE.displayPromptForEnroll(this, false);
                } else {
                    goToInputPasswordPromo(promoResponse.getId());
                }
            }
        } else if (isBioChanged && !activityPresenter.isBioChangedDialogShown()) {
            openBioChanged();
        } else {
            if (hasBiometricEnrolled) {
                if (isActivasi) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        BiometricUtility.INSTANCE.displayPromptForLogin(this);
                    }
                } else {
                    goToInputPasswordPromo(promoResponse.getId());
                }
            } else {
                activityPresenter.updateStatusAktivasi(false);
                goToInputPasswordPromo(promoResponse.getId());
            }
        }


//        startActivity(new Intent(this, InputPasswordNewSkinActivity.class)
//                .putExtra("ID_PROMO_FAST_MENU", promoResponsfe.getId()));
    }

    @Override
    public void onSuccessGetDetailItem(@Nullable PromoResponse promoResponse) {
        Intent dashboardIntent = new Intent(this, DashboardIBActivity.class);
        dashboardIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(dashboardIntent);

        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            DetailPromoActivity.launchIntent(this, promoResponse != null ? promoResponse.getPromo() : null, true, false, false);
            finish();
        }, 300);
    }

    public interface OnWindowFocusChangeListener {
        void onWindowFocusChanged(Boolean hasFocus);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (focusChangeListener != null) {
            focusChangeListener.onWindowFocusChanged(hasFocus);
        }
    }

    public void setFocusChangeListener(OnWindowFocusChangeListener listener) {
        this.focusChangeListener = listener;
    }

    @Override
    public void showProgress() {
        super.showProgress();
    }

    private static String mockResponse = "[\n" +
            "        {\n" +
            "            \"id\": 95474,\n" +
            "            \"class\": \"food\",\n" +
            "            \"class_order_id\": 1,\n" +
            "            \"kategori\": \"Food & Beverage\",\n" +
            "            \"title\": \"KOPI KENANGAN DISKON 40% DENGAN PROMO SPESIAL BRI\",\n" +
            "            \"overview\": \"KOPI KENANGAN DISKON 40% DENGAN PROMO SPESIAL BRI\",\n" +
            "            \"img_detail\": \"http://172.18.136.93:4010/brimo-promo/promo/ca7177960d171ff74e1b22039a2dc2f8.jpg\",\n" +
            "            \"image_thumbnail\": \"http://172.18.136.93:4010/brimo-promo/promo/banner-emas.png\",\n" +
            "            \"nbm_header\": \"http://172.18.136.93:4010/brimo-promo/promo/ca7177960d171ff74e1b22039a2dc2f8.jpg\",\n" +
            "            \"nbm_screen\": \"http://172.18.136.93:4010/brimo-promo/promo/ca7177960d171ff74e1b22039a2dc2f8.jpg\",\n" +
            "            \"nbm_thumbnail\": \"http://172.18.136.93:4010/brimo-promo/promo/ca7177960d171ff74e1b22039a2dc2f8.jpg\",\n" +
            "            \"start_date\": \"2024-06-03T17:00:00.000Z\",\n" +
            "            \"end_date\": \"2024-06-12T17:00:00.000Z\",\n" +
            "            \"inserted_date\": \"2024-06-04T04:10:33.000Z\",\n" +
            "            \"modified_date\": \"2025-04-14T08:59:24.000Z\",\n" +
            "            \"status\": 1\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 10150,\n" +
            "            \"class\": \"food\",\n" +
            "            \"class_order_id\": 1,\n" +
            "            \"kategori\": \"Food & Beverage\",\n" +
            "            \"title\": \"Maxx Coffe BOGO Everyday!\",\n" +
            "            \"overview\": \"Maxx Coffe BOGO Everyday!!\",\n" +
            "            \"img_detail\": \"http://172.18.136.93:4010/brimo-promo/promo/e3588048cd17b37b5e0c929056d850c6.jpg\",\n" +
            "            \"image_thumbnail\": \"http://172.18.136.93:4010/brimo-promo/promo/banner-bakery.png\",\n" +
            "            \"nbm_header\": \"http://172.18.136.93:4010/brimo-promo/promo/e3588048cd17b37b5e0c929056d850c6.jpg\",\n" +
            "            \"nbm_screen\": \"http://172.18.136.93:4010/brimo-promo/promo/e3588048cd17b37b5e0c929056d850c6.jpg\",\n" +
            "            \"nbm_thumbnail\": \"http://172.18.136.93:4010/brimo-promo/promo/e3588048cd17b37b5e0c929056d850c6.jpg\",\n" +
            "            \"term_condition\": \"<p>coba aja</p>\",\n" +
            "            \"start_date\": \"2023-12-31T17:00:00.000Z\",\n" +
            "            \"end_date\": \"2024-01-30T17:00:00.000Z\",\n" +
            "            \"inserted_date\": \"2024-01-24T08:01:12.000Z\",\n" +
            "            \"modified_date\": \"2025-04-14T08:59:24.000Z\",\n" +
            "            \"status\": 1\n" +
            "        }\n" +
            "    ]";
}