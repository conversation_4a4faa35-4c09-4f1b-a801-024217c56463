package id.co.bri.brimo.ui.fragments.pin

import android.annotation.SuppressLint
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import id.co.bri.brimo.adapters.PinNumberAdapterNewSkin
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import id.co.bri.brimo.adapters.pinadapter.OtpInputAdapterNewSkin
import id.co.bri.brimo.databinding.PinEntryViewBinding
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers.Companion.getPinNumberList
import id.co.bri.brimo.util.extension.isSequentialOrSame

class PinEntryFragment : Fragment(),
    PinNumberAdapterNewSkin.OnPinNumberListener,
    BasePinAdapter.PinAdapterListener {

    private var _binding: PinEntryViewBinding? = null
    private val binding get() = _binding!!

    private var otpInputAdapter: OtpInputAdapterNewSkin? = null
    private var pinNumberAdapter: PinNumberAdapterNewSkin? = null
    private val handler = Handler(Looper.getMainLooper())

    private var isInErrorState = false
    private var isProcessing = false

    var isSequentialCheckEnabled: Boolean = true

    private var listener: PinEntryListener? = null

    fun setPinEntryListener(listener: PinEntryListener) {
        this.listener = listener
    }

    var headerTitle: String = "PIN"
    var descriptionText: String = ""
    var infoText: String = ""
    var infoTextColor: Int? = null
    var infoTextTypeface: Typeface? = null
    var errorTextColor: Int? = null
    var errorTextTypeface: Typeface? = null
    var isForgotPinVisible: Boolean = false

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = PinEntryViewBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        setupView()
    }

    private fun setupView() {
        binding.headerTittle.text = headerTitle
        binding.tvDeksripsi.text = descriptionText
        binding.tvDeksripsi.visibility =
            if (descriptionText.isNotEmpty()) View.VISIBLE else View.GONE

        binding.tvInfoText.text = infoText
        binding.tvInfoText.visibility = if (infoText.isNotEmpty()) View.VISIBLE else View.GONE
        infoTextColor?.let { binding.tvInfoText.setTextColor(it) }
        infoTextTypeface?.let { binding.tvInfoText.typeface = it }

        errorTextColor?.let { binding.tvError.setTextColor(it) }
        errorTextTypeface?.let { binding.tvError.typeface = it }

        binding.tvLupaPin.visibility = if (isForgotPinVisible) View.VISIBLE else View.GONE
        binding.tvLupaPin.setOnClickListener {
            listener?.onForgotPinClicked()
        }

        binding.llBack.setOnClickListener { activity?.onBackPressedDispatcher?.onBackPressed() }

        otpInputAdapter = OtpInputAdapterNewSkin(requireContext()).apply {
            setListener(this@PinEntryFragment)
        }

        pinNumberAdapter = PinNumberAdapterNewSkin(getPinNumberList(requireContext())).apply {
            onPinNumberListener = this@PinEntryFragment
        }

        binding.rvBox.layoutManager = GridLayoutManager(requireContext(), 6)
        binding.rvBox.adapter = otpInputAdapter

        binding.rvInput.layoutManager = GridLayoutManager(requireContext(), 3)
        binding.rvInput.adapter = pinNumberAdapter

        binding.tvError.visibility = View.GONE
    }

    override fun onPinClicked(pinNumber: Int) {
        if (isProcessing) return
        if (isInErrorState) clearError()
        otpInputAdapter?.addPin(pinNumber.toString())
    }

    override fun onDeleteClicked() {
        if (isProcessing) return
        if (isInErrorState) clearError()
        otpInputAdapter?.deletePin()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun notifyChanges() {
        otpInputAdapter?.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        if (isProcessing) return
        isProcessing = true

        val errorMessage: String? = when {
            isSequentialCheckEnabled && string.isSequentialOrSame() ->
                "PIN tidak boleh berurutan atau angka yang sama"
            string.length < 6 ->
                "PIN harus terdiri dari 6 angka"
            else -> null
        }

        if (errorMessage != null) {
            setErrorText(errorMessage)
            listener?.onPinError(errorMessage)
            handler.postDelayed({
                resetView()
                isProcessing = false
            }, 300)
            return
        }

        listener?.onPinComplete(string)

        handler.postDelayed({
            resetView()
            isProcessing = false
        }, 300)
    }


    fun setErrorText(message: String) {
        isInErrorState = true
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
        binding.tvInfoText.visibility = View.GONE
        binding.tvLupaPin.visibility = if (isForgotPinVisible) View.VISIBLE else View.GONE
    }

    private fun clearError() {
        isInErrorState = false
        binding.tvError.visibility = View.GONE
        binding.tvError.text = ""
        binding.tvInfoText.visibility = if (infoText.isNotEmpty()) View.VISIBLE else View.GONE
        binding.tvLupaPin.visibility = if (isForgotPinVisible) View.VISIBLE else View.GONE
        otpInputAdapter?.resetError()
    }

    fun resetView() {
        otpInputAdapter?.deleteAllPin()
        otpInputAdapter?.resetError()
        isProcessing = false
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

interface PinEntryListener {
    fun onPinComplete(pin: String)
    fun onPinError(errorMessage: String)
    fun onForgotPinClicked()
}