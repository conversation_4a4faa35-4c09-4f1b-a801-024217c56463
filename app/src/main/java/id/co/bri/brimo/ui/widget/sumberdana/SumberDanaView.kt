package id.co.bri.brimo.ui.widget.sumberdana

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.graphics.toColorInt
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.SumberDanaViewBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.ui.activities.pulsadata.reskin.toggle
import id.co.bri.brimo.util.fadeIn

class SumberDanaView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {
    private val binding: SumberDanaViewBinding
    var buttonType: ButtonTypeSumberDana = ButtonTypeSumberDana.closedBill
        set(value) {
            field = value
            applyButtonType()
        }

    private lateinit var skeletonSumberdana: SkeletonScreen

    var account: AccountModel?= null
        set(value) {
            field = value

            applySumberDana()
        }

    var payAmount: Int?= null
        set(value) {
            field = value
            applySumberDana()
        }

    var isFastMenu: Boolean = false
        set(value) {
            field = value
            applyShowHideCurrency()
        }

    var payAmountString: String = ""
        set(value) {
            field = value
            applyPaymentButton()
        }

    private var hideCurrency = false

    init {
        val inflater = LayoutInflater.from(context)
        binding = SumberDanaViewBinding.inflate(inflater, this, true)

        if(account == null && !isInEditMode) {
            skeletonSumberdana = Skeleton.bind(binding.llSumberdana)
                .shimmer(true)
                .angle(20)
                .duration(1200).color(R.color.ns_graysoft)
                .load(R.layout.skeleton_sumberdana_inquiry_reskin)
                .show()
        }

        binding.ivShowhideCurrency.setOnClickListener {
            hideCurrency = hideCurrency.toggle()
            binding.ivShowhideCurrency.setImageResource(if(!hideCurrency) R.drawable.icon_unhide_eye else R.drawable.icon_hide_eye)
            applySumberDana()
        }

        context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.SumberDanaView,
            0, 0
        ).apply {
            try {
                val typeValue = getInt(R.styleable.SumberDanaView_buttonType, ButtonTypeSumberDana.closedBill.value)
                buttonType = ButtonTypeSumberDana.fromValue(typeValue)
            } finally {
                recycle()
            }
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        binding.root.isEnabled = enabled
    }

    fun setOnClickSumberDana(listener: (() -> Unit)) {
        binding.llSumberdana.setOnClickListener {
            if(account!=null) listener.invoke()
        }
    }

    fun setOnClickRefreshSaldo(listener: (() -> Unit)) {
        binding.ivRefreshSaldo.setOnClickListener {
            listener.invoke()
            binding.ivRefreshSaldo.visibility = GONE
            skeletonSumberdana.show()
        }
    }

    fun setOnClickRefreshAccount(listener: (() -> Unit)) {
        binding.ivRefresh.setOnClickListener {
            listener.invoke()
            binding.ivRefresh.visibility = GONE
            binding.ivChange.visibility = VISIBLE
            skeletonSumberdana.show()
        }
    }

    fun setOnClickButton(listener: (() -> Unit)) {
        if (buttonType == ButtonTypeSumberDana.closedBill) {
            binding.btnPayment.setOnItemClickListener {
                listener.invoke()
            }
        } else {
            binding.button.setOnClickListener { listener.invoke() }
        }
    }

    fun isEnableButton(isEnabled: Boolean) {
        val targetButton = if (buttonType == ButtonTypeSumberDana.closedBill) {
            binding.btnPayment
        } else {
            binding.button
        }
        targetButton.isEnabled = isEnabled
    }

    fun setErrorFetching() {
        skeletonSumberdana.hide()
        binding.tvNumberAccount.text = GeneralHelper.getString(R.string.gagal_memuat)
        binding.tvNominalAccount.text = GeneralHelper.getString(R.string.gagal_memuat)
        binding.ivChange.visibility = GONE
        binding.ivShowhideCurrency.visibility = GONE
        binding.ivRefresh.visibility = VISIBLE
    }

    private fun applyPaymentButton() {
        binding.btnPayment.setAmountText(payAmountString)
    }

    private fun applyShowHideCurrency() {
        binding.ivShowhideCurrency.visibility = if(isFastMenu) GONE else VISIBLE
    }

    private fun applySumberDana() {
        var isLowBalance = true

        payAmount?.let {
            if(it>0) {
                account?.saldoReponse?.let { saldo ->
                    val balance = saldo.balance.toInt()
                    isLowBalance = balance < it
                }
            }
            else {
                isLowBalance = false
            }
        }

        isEnableButton(!isLowBalance && account!=null)

        account?.let { data ->
            skeletonSumberdana.hide()
            binding.ivRefresh.visibility = GONE

            binding.tvNumberAccount.text = if (hideCurrency) {
                maskAccountNumber(data.acoountString)
            } else {
                data.acoountString
            }

            GeneralHelper.loadIconTransaction(
                context,
                data.imagePath,
                data.imageName,
                binding.ivRekening,
                R.drawable.img_card_bg)

            val saldoResponse = data.saldoReponse ?: return
            val balanceStr = saldoResponse.balanceString.uppercase()
            val errorMap = mapOf(
                "TO" to R.string.empty,
                "12" to R.string.empty,
                "05" to R.string.empty
            )
            errorMap[balanceStr]?.let { errorResId ->
                binding.ivRefresh.visibility = VISIBLE
                binding.tvNominalAccount.text = GeneralHelper.getString(errorResId)
                return
            }
            binding.tvNominalAccount.text = when {
                hideCurrency -> maskAmount(saldoResponse.balance)
                saldoResponse.signedBalanceString.isNotEmpty() -> saldoResponse.signedBalanceString
                saldoResponse.currency != null -> GeneralHelper.formatNominalIDR(
                    saldoResponse.currency,
                    saldoResponse.balanceString
                )
                else -> saldoResponse.balanceString
            }
            if(payAmount != null) {
                binding.tvWarningCurrency.apply {
                    visibility = if (isLowBalance) VISIBLE else GONE
                    fadeIn()
                }
                binding.tvNominalAccount.apply {
                    setTextColor(if (isLowBalance) "#E84040".toColorInt() else "#181C21".toColorInt())
                    fadeIn()
                }
            }
        }?: skeletonSumberdana.show()
    }

    private fun applyButtonType() {
        binding.button.visibility = if(buttonType == ButtonTypeSumberDana.closedBill) GONE else VISIBLE
        binding.btnPayment.visibility = if(buttonType == ButtonTypeSumberDana.closedBill) VISIBLE else GONE

        (if (buttonType == ButtonTypeSumberDana.closedBill) {
            binding.btnPayment
        } else {
            binding.button
        }).isEnabled = false
    }

    private fun maskAccountNumber(account: String): String {
        val raw = account.replace(" ", "")

        if (raw.length < 8) return "****"

        val prefixLength = 4
        val suffixLength = 3

        val prefix = raw.take(prefixLength)
        val suffix = raw.takeLast(suffixLength)

        val maskedLength = raw.length - prefixLength - suffixLength
        val masked = "*".repeat(maskedLength)

        val combined = prefix + masked + suffix
        return combined.chunked(4).joinToString(" ")
    }

    private fun maskAmount(amount: Double): String {
        return "Rp" + "•".repeat(8)
    }
}

enum class ButtonTypeSumberDana(val value: Int) {
    closedBill(0),
    openBill(1);

    companion object {
        fun fromValue(value: Int): ButtonTypeSumberDana {
            return values().find { it.value == value } ?: closedBill
        }
    }
}