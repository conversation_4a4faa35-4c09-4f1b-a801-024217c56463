package id.co.bri.brimo.ui.fragments;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListRekeningAdapter;
import id.co.bri.brimo.contract.IPresenter.saldo.ISumberDanaPresenter;
import id.co.bri.brimo.contract.IView.saldo.ISumberDanaView;
import id.co.bri.brimo.databinding.FragmentSumberDanaBinding;
import id.co.bri.brimo.domain.GetListDataSumberDanaCallback;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralResponse;
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.widget.OnCloseClickListener;

/**
 * Created by FNS on 09/12/2019
 */
public class SumberDanaFragment extends BottomSheetDialogFragment implements GetListDataSumberDanaCallback, ListRekeningAdapter.ClickItem, ISumberDanaView {

    private FragmentSumberDanaBinding binding;
    private ListRekeningAdapter adapter;

    private List<AccountModel> list;
    private final SelectSumberDanaInterface selectSumberDanaInterface;
    private SelectSumberDanaWithPinInterface selectSumberDanaWithPinInterface;
    private List<Integer> listPositionFailedFromResponse;
    private List<Integer> listFailedHit;
    boolean isAllSuccess;
    private Integer counter;
    private boolean isFromFastMenu = false;
    private int selectedIndex = 0;
    private int payAmount = 0;

    @Inject
    ISumberDanaPresenter<ISumberDanaView> activityPresenter;

    public SumberDanaFragment(List<AccountModel> list, SelectSumberDanaInterface selectSumberDanaInterface, Integer counter, List<Integer> listPositionFailed, boolean isFromFastMenu) {
        this.list = list;
        this.selectSumberDanaInterface = selectSumberDanaInterface;
        this.counter = counter;
        this.isFromFastMenu = isFromFastMenu;
        this.listFailedHit = listPositionFailed;
    }

    public SumberDanaFragment(List<AccountModel> list, SelectSumberDanaInterface selectSumberDanaInterface) {
        this.list = list;
        this.selectSumberDanaInterface = selectSumberDanaInterface;
    }

    public SumberDanaFragment(List<AccountModel> list, SelectSumberDanaInterface selectSumberDanaInterface, Integer counter, List<Integer> listPositionFailed) {
        this.list = list;
        this.selectSumberDanaInterface = selectSumberDanaInterface;
        this.counter = counter;
        this.listFailedHit = listPositionFailed;
    }

    public SumberDanaFragment(List<AccountModel> list, SelectSumberDanaInterface selectSumberDanaInterface, Integer counter, List<Integer> listPositionFailed, int selectedIndex, int payAmount, boolean isFromFastMenu) {
        this.list = list;
        this.selectSumberDanaInterface = selectSumberDanaInterface;
        this.counter = counter;
        this.listFailedHit = listPositionFailed;
        this.selectedIndex = selectedIndex;
        this.payAmount = payAmount;
        this.isFromFastMenu = isFromFastMenu;
    }

    public SumberDanaFragment(List<AccountModel> list, SelectSumberDanaInterface selectSumberDanaInterface, SelectSumberDanaWithPinInterface selectSumberDanaWithPinInterface, Integer counter, List<Integer> listPositionFailed) {
        this.list = list;
        this.selectSumberDanaInterface = selectSumberDanaInterface;
        this.selectSumberDanaWithPinInterface = selectSumberDanaWithPinInterface;
        this.counter = counter;
        this.listFailedHit = listPositionFailed;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentSumberDanaBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        GeneralHelperNewSkin.showBlur(requireActivity());
        binding.bbslRoot.setOnCloseClickListener(new OnCloseClickListener() {
            @Override
            public void onCloseClick() {
                dismiss();
            }
        });

        binding.rvListBank.setHasFixedSize(true);
        binding.rvListBank.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new ListRekeningAdapter(getActivity(), list, this, selectedIndex, payAmount, isFromFastMenu);
        binding.rvListBank.setAdapter(adapter);

        injectDependency();

        activityPresenter.setView(this);
        activityPresenter.start();

        if (listFailedHit != null && !listFailedHit.isEmpty()) {
            for (Integer i : listFailedHit) {
                list.get(i).setSaldoReponse(null);
            }
            adapter.notifyDataSetChanged();
        }

        if (!isFromFastMenu) {
            if (counter == 1) {
                activityPresenter.getSaldo(list);
            } else
                activityPresenter.getSaldoFailed(list, listFailedHit);
        } else {
            if (counter == 1) {
                activityPresenter.getSaldoFM(list);
            } else
                activityPresenter.getSaldoFailedFM(list, listFailedHit);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        injectDependency();
    }

    private void injectDependency() {
        ((BaseActivity) getActivity()).getActivityComponent().inject(this);
    }

    @Override
    public void onUpdateList(List<AccountModel> list) {
        this.list = list;
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onClickItem(AccountModel accountModel) {
        dismiss();
        checkSumberDanaInterface(accountModel);

        checkSumberDanaWithPinInterface(accountModel);
    }

    @Override
    public void onGetSaldo(List<AccountModel> listAccount) {
        list = listAccount;
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onGetSaldoComplete(List<Integer> accountModelListFailed) {
        if (accountModelListFailed.isEmpty())
            isAllSuccess = true;
        listPositionFailedFromResponse = accountModelListFailed;
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
    }


    @Override
    public void onRootedDevice() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(getActivity())
                .setTitle("Peringatan")
                .setMessage("Maaf, Anda tidak diijinkan untuk masuk ke aplikasi BRImo karena perangkat Anda terindikasi telah di-root")
                .setNeutralButton("Close", (dialogInterface, i) -> getActivity().finish())
                .setIcon(R.drawable.ic_close_black_24dp)
                .setCancelable(false);
        alertDialog.show();
    }

    @Override
    public void showProgress() {
        // do nothing
    }

    @Override
    public void hideProgress() {
        // do nothing
    }

    @Override
    public void onSessionEnd(String message) {
        hideProgress();
        FastMenuNewSkinActivity.launchIntentSessionEnd(getActivity(), message);
    }

    @Override
    public void onDestroyView() {
        activityPresenter.stop();
        super.onDestroyView();
    }

    @Override
    public void onException(String message) {
        // do nothing
    }

    @Override
    public void onExceptionRevamp(String message) {

    }

    @Override
    public void onException06(ExceptionResponse response) {
        GeneralHelper.showDialogCustom(getActivity(), response);
    }

    @Override
    public void onException99(String message) {
        // do nothing
    }

    @Override
    public void onExceptionFO(EmptyStateResponse response) {

    }

    @Override
    public void onExceptionLimitExceed(GeneralResponse response) {
        // do nothing
    }

    @Override
    public void onExceptionNoBackAction(String message) {
        // do nothing
    }

    @Override
    public void onExceptionStatusNotMatch() {
        // do nothing
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        GeneralHelperNewSkin.hideBlur(requireActivity());

        if (selectSumberDanaWithPinInterface != null) {
            selectSumberDanaWithPinInterface.onSOFDismissed();
        }

        if (listPositionFailedFromResponse == null) {
            return;
        }
        selectSumberDanaInterface.onSendFailedList(listPositionFailedFromResponse);
        if (activityPresenter != null) {
            activityPresenter.stop();
        }
        super.onDismiss(dialog);
    }

    private void checkSumberDanaInterface(AccountModel accountModel) {
        if (selectSumberDanaInterface != null) {
            selectSumberDanaInterface.onSelectSumberDana(accountModel);
        }
    }

    private void checkSumberDanaWithPinInterface(AccountModel accountModel) {
        if (selectSumberDanaWithPinInterface != null) {
            selectSumberDanaWithPinInterface.onSelectSof(accountModel, list);
            selectSumberDanaWithPinInterface.onInputPinWhenSelectSof();
        }
    }

    public interface SelectSumberDanaInterface {
        void onSelectSumberDana(AccountModel bankModel);

        void onSendFailedList(List<Integer> list);
    }

    public interface SelectSumberDanaWithPinInterface {

        void onSelectSof(AccountModel bankModel, List<AccountModel> listAccount);

        void onInputPinWhenSelectSof();

        void onSOFDismissed();
    }
}