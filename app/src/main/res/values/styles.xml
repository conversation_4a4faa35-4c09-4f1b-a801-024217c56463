<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="CircleImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:fontFamily">@font/avenir_next_medium</item>
        <item name="snackbarStyle">@style/CustomSnackbar</item>
        <!--        <item name="android:windowAnimationStyle">@null</item>-->
    </style>

    <style name="AppThemeBlueBar" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="CustomTheme" parent="Theme.AppCompat.Light.NoActionBar" />

    <style name="AppThemeBlueBarRevamp" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:statusBarColor">@color/primary_blue80</item>
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowIsFloating">false</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="AppThemeBlueBarNewSkin" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/ns_primary500</item>
        <item name="colorPrimaryDark">@color/ns_primary500</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:statusBarColor">@color/ns_primary500</item>
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="AppThemeBlueBarRevampAppCompat" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:statusBarColor">@color/primary_blue80</item>
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="AppThemeBlueBarRevampFull" parent="AppThemeBlueBarRevamp">
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="CustomButtonStyle" parent="Widget.AppCompat.Button">
        <item name="android:background">@null</item>
        <item name="backgroundTint">@null</item>
    </style>

    <style name="AppThemeTransparentStatusBar" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <style name="TabWidgetRevamp" parent="@android:style/TextAppearance.Widget.TabWidget">
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
        <item name="textSize">@dimen/body_small_text</item>
        <item name="tabTextColor">@color/primary_blue100</item>
    </style>

    <style name="AppThemeBlueBarWebView" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:fontFamily">@font/avenir_next_medium</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">false</item>
    </style>

    <style name="AppThemeWhiteBar" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/colorButtonWhite</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="AlertDialogTheme" parent="Theme.MaterialComponents.Light.Dialog.Alert">
        <item name="buttonBarPositiveButtonStyle">@style/Alert.Button.Positive</item>
        <item name="buttonBarNegativeButtonStyle">@style/Alert.Button.Neutral</item>
        <item name="buttonBarNeutralButtonStyle">@style/Alert.Button.Neutral</item>
    </style>

    <style name="Alert.Button.Positive" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="backgroundTint">@color/transparent</item>
        <item name="rippleColor">@color/colorAccent</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="Alert.Button.Neutral" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="backgroundTint">@color/transparent</item>
        <item name="rippleColor">@color/colorAccent</item>
    </style>

    <style name="AppThemeBlackBar" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>


    <style name="CustomTabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/avenir_next_medium</item>
        <item name="android:textColor">#01539D</item>

    </style>

    <style name="CustomToolbarStyle" parent="ThemeOverlay.AppCompat">
        <item name="android:fontFamily">@font/avenir_next_bold</item>
    </style>

    <style name="CardViewPINNumberStyle" parent="CardView">
        <item name="android:layout_marginLeft">1dp</item>
        <item name="android:layout_marginRight">1dp</item>
        <item name="cardCornerRadius">0dp</item>
        <item name="cardElevation">0dp</item>
        <item name="cardUseCompatPadding">true</item>
    </style>

    <style name="Widget.BottomNavigationView" parent="Widget.Design.BottomNavigationView">
        <item name="fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="StackedAlertDialogStyle" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="buttonBarButtonStyle">@style/StackedButtonBarButtonStyle</item>
    </style>

    <style name="StackedButtonBarButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:layout_gravity">right</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:colorAccent">@color/colorBlack</item>
        <item name="android:textColor">@color/colorBlack</item>
        <item name="android:textColorPrimary">@color/colorBlack</item>
        <item name="android:textSize">@dimen/_13sdp</item>
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="ToolbarTitleStyle">
        <item name="android:layout_gravity">center</item>
        <item name="android:textAlignment">center</item>
        <item name="android:textSize">@dimen/_16sdp</item>
        <item name="android:textColor">@color/colorTextWhite</item>
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="Body1LargeText.Bold.NeutralBaseWhite.Toolbar">
        <item name="android:layout_gravity">center</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="Body1LargeText.Medium.NeutralBaseWhite.Toolbar">
        <item name="android:layout_gravity">center</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="Body1LargeText.Bold.NeutralBaseBlack.Toolbar">
        <item name="android:layout_gravity">center</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="ToolbarTitleStyleRight">
        <item name="android:layout_gravity">right</item>
        <item name="android:textAlignment">center</item>
        <item name="android:textSize">@dimen/_16sdp</item>
        <item name="android:textColor">@color/colorTextWhite</item>
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="ToolbarTitleStyleRightFilter">
        <item name="android:layout_gravity">right</item>
        <item name="android:textAlignment">center</item>
        <item name="android:textSize">@dimen/_11sdp</item>
        <item name="android:textColor">@color/colorTextWhite</item>
        <item name="android:fontFamily">@font/bri_digital_text_semi_bold</item>
    </style>

    <style name="EditTextHint" parent="@android:style/TextAppearance">
        <item name="android:textSize">13dp</item>
        <item name="android:textColor">@color/hintblack</item>
    </style>

    <style name="EditTextHintNew" parent="@android:style/TextAppearance">
        <item name="android:fontFamily">@font/avenir_next_regular</item>
        <item name="android:textSize">16dp</item>
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="EditTextHintNeutralLight60" parent="@android:style/TextAppearance">
        <item name="fontFamily">@font/bri_digital_text_regular</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/neutralLight60</item>
    </style>

    <style name="UbahPasswordHint" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/_13sdp</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowBackground">@drawable/splash_screen</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="AppTheme.Dialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppTheme.Dialog.Transparent">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
    </style>

    <style name="AppTheme.Transparent">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowTitleStyle">@null</item>
    </style>

    <style name="Widget.Button.Toggle" parent="android:Widget">
        <item name="android:background">@drawable/ic_toggle_bg</item>
        <item name="android:disabledAlpha">?android:attr/disabledAlpha</item>
    </style>

    <style name="toggleButton">
        <item name="android:buttonStyleToggle">@style/Widget.Button.Toggle</item>
        <item name="android:textOn"></item>
        <item name="android:textOff"></item>
    </style>

    <style name="CustomBottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="CustomBottomSheetDialogThemeMaterial" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="BottomSheetDialogStyle" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="CustomBottomSheetDialogThemeInput" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="CustomBottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="MyCustomTablayout" parent="Widget.Design.TabLayout">
        <item name="tabTextAppearance">@style/TablayoutTextViewCustomStyle</item>
    </style>

    <style name="TablayoutTextViewCustomStyle" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/avenir_next_medium</item>
        <item name="android:textColor">@color/colorPfmFontNotes</item>
        <item name="textSize">12dp</item>

    </style>

    <!-- ToolBar -->
    <style name="flexyToolbarStyle" parent="Theme.AppCompat">
        <item name="android:textColorPrimary">@android:color/white</item>
        <item name="android:textColorSecondary">@android:color/white</item>
        <item name="actionMenuTextColor">@android:color/white</item>
    </style>

    <!-- FAB Style -->
    <style name="FabStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@color/white</item>
        <item name="elevation">4dp</item>
        <item name="pressedTranslationZ">12dp</item>
        <item name="rippleColor">@android:color/white</item>
    </style>

    <style name="RatingBar" parent="Theme.AppCompat">
        <item name="colorControlNormal">@color/colorErrorMinTrx</item>
        <item name="colorControlActivated">@color/colorText</item>
    </style>

    <style name="DefaultNumberPickerTheme" parent="AppTheme">
        <item name="colorControlNormal">@color/transparent</item>
        <item name="android:textColor">@color/black_ns_600</item>
        <item name="android:textSize">@dimen/size_18dp</item>
        <item name="android:fontFamily">@font/bri_digital_text_semi_bold</item>
    </style>

    <style name="NewStyleNumberPickerTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorControlNormal">@color/transparent</item>
        <item name="android:textColorPrimary">@color/highlightColor</item>
        <item name="fontFamily">@font/bold</item>
        <item name="text_size">16dp</item>
    </style>

    <!--Base Text style-->
    <style name="BaseAppTheme.Text" parent="TextAppearance.AppCompat" />

    <!--Text style-->
    <style name="Headline1Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/h1_text</item>
    </style>

    <style name="Headline2Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/h2_text</item>
    </style>

    <style name="Headline3Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/h3_text</item>
    </style>

    <style name="Headline4Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/h4_text</item>
    </style>

    <style name="Headline5Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/h5_text</item>
    </style>

    <style name="Headline6Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/h6_text</item>
    </style>

    <style name="SubTitleText" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/subtitle_text</item>
    </style>

    <style name="BodyMediumText" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/body_medium_text</item>
    </style>

    <style name="BodySmallText" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/body_small_text</item>
    </style>

    <style name="CaptionText" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/micro_text</item>
    </style>

    <!--Headline1 Text-->
    <style name="Headline1Text.Regular">
        <item name="android:fontFamily">@font/avenir_next_regular</item>
    </style>

    <style name="Headline1Text.Regular.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="Headline1Text.Regular.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="Headline1Text.Regular.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="Headline1Text.Regular.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="Headline1Text.Regular.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="Headline1Text.Regular.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="Headline1Text.Regular.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="Headline1Text.Regular.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="Headline1Text.Regular.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="Headline1Text.Regular.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="Headline1Text.Regular.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="Headline1Text.Regular.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="Headline1Text.Regular.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--Headline2 Text-->
    <style name="Headline2Text.Medium">
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="Headline2Text.Medium.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="Headline2Text.Medium.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="Headline2Text.Medium.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="Headline2Text.Medium.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="Headline2Text.Medium.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="Headline2Text.Medium.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="Headline2Text.Medium.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="Headline2Text.Medium.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="Headline2Text.Medium.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="Headline2Text.Medium.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="Headline2Text.Medium.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="Headline2Text.Medium.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="Headline2Text.Medium.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--Headline3 Medium Text-->
    <style name="Headline3Text.Medium">
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="Headline3Text.Medium.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="Headline3Text.Medium.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="Headline3Text.Medium.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="Headline3Text.Medium.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="Headline3Text.Medium.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="Headline3Text.Medium.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="Headline3Text.Medium.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="Headline3Text.Medium.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="Headline3Text.Medium.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="Headline3Text.Medium.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="Headline3Text.Medium.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="Headline3Text.Medium.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="Headline3Text.Medium.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--Headline3 Demi Bold Text-->
    <style name="Headline3Text.DemiBold">
        <item name="android:fontFamily">@font/avenir_next_demi</item>
    </style>

    <style name="Headline3Text.DemiBold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="Headline3Text.DemiBold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="Headline3Text.DemiBold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="Headline3Text.DemiBold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="Headline3Text.DemiBold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="Headline3Text.DemiBold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="Headline3Text.DemiBold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="Headline3Text.DemiBold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="Headline3Text.DemiBold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="Headline3Text.DemiBold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="Headline3Text.DemiBold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="Headline3Text.DemiBold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="Headline3Text.DemiBold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--Headline4 Demi Bold Text-->
    <style name="Headline4Text.DemiBold">
        <item name="android:fontFamily">@font/avenir_next_demi</item>
    </style>

    <style name="Headline4Text.DemiBold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="Headline4Text.DemiBold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="Headline4Text.DemiBold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="Headline4Text.DemiBold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="Headline4Text.DemiBold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="Headline4Text.DemiBold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="Headline4Text.DemiBold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="Headline4Text.DemiBold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="Headline4Text.DemiBold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="Headline4Text.DemiBold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="Headline4Text.DemiBold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="Headline4Text.DemiBold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="Headline4Text.DemiBold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--Headline4 Bold Text-->
    <style name="Headline4Text.Bold">
        <item name="android:fontFamily">@font/avenir_next_bold</item>
    </style>

    <style name="Headline4Text.Bold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="Headline4Text.Bold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="Headline4Text.Bold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="Headline4Text.Bold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="Headline4Text.Bold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="Headline4Text.Bold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="Headline4Text.Bold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="Headline4Text.Bold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="Headline4Text.Bold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="Headline4Text.Bold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="Headline4Text.Bold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="Headline4Text.Bold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="Headline4Text.Bold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--Headline5 Bold Text-->
    <style name="Headline5Text.Bold">
        <item name="android:fontFamily">@font/avenir_next_bold</item>
    </style>

    <style name="Headline5Text.Bold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="Headline5Text.Bold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="Headline5Text.Bold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="Headline5Text.Bold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="Headline5Text.Bold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="Headline5Text.Bold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="Headline5Text.Bold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="Headline5Text.Bold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="Headline5Text.Bold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="Headline5Text.Bold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="Headline5Text.Bold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="Headline5Text.Bold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="Headline5Text.Bold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--Headline6 Bold Text-->
    <style name="Headline6Text.Bold">
        <item name="android:fontFamily">@font/avenir_next_bold</item>
    </style>

    <style name="Headline6Text.Bold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="Headline6Text.Bold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="Headline6Text.Bold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="Headline6Text.Bold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="Headline6Text.Bold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="Headline6Text.Bold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="Headline6Text.Bold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="Headline6Text.Bold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="Headline6Text.Bold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="Headline6Text.Bold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="Headline6Text.Bold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="Headline6Text.Bold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="Headline6Text.Bold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--Subtitle Bold Text-->
    <style name="SubTitleText.Bold">
        <item name="android:fontFamily">@font/avenir_next_bold</item>
    </style>

    <style name="SubTitleText.Bold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="SubTitleText.Bold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="SubTitleText.Bold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="SubTitleText.Bold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="SubTitleText.Bold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="SubTitleText.Bold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="SubTitleText.Bold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="SubTitleText.Bold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="SubTitleText.Bold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="SubTitleText.Bold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="SubTitleText.Bold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="SubTitleText.Bold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="SubTitleText.Bold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--BodyMedium Medium Text-->
    <style name="BodyMediumText.Medium">
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="BodyMediumText.Medium.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="BodyMediumText.Medium.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="BodyMediumText.Medium.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="BodyMediumText.Medium.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="BodyMediumText.Medium.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="BodyMediumText.Medium.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="BodyMediumText.Medium.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="BodyMediumText.Medium.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="BodyMediumText.Medium.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="BodyMediumText.Medium.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="BodyMediumText.Medium.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="BodyMediumText.Medium.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="BodyMediumText.Medium.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--BodyMedium DemiBold Text-->
    <style name="BodyMediumText.DemiBold">
        <item name="android:fontFamily">@font/avenir_next_demi</item>
    </style>

    <style name="BodyMediumText.DemiBold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="BodyMediumText.DemiBold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="BodyMediumText.DemiBold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="BodyMediumText.DemiBold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="BodyMediumText.DemiBold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="BodyMediumText.DemiBold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="BodyMediumText.DemiBold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="BodyMediumText.DemiBold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="BodyMediumText.DemiBold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="BodyMediumText.DemiBold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="BodyMediumText.DemiBold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="BodyMediumText.DemiBold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="BodyMediumText.DemiBold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--BodyMedium Bold Text-->
    <style name="BodyMediumText.Bold">
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="BodyMediumText.Bold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="BodyMediumText.Bold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="BodyMediumText.Bold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="BodyMediumText.Bold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="BodyMediumText.Bold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="BodyMediumText.Bold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="BodyMediumText.Bold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="BodyMediumText.Bold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="BodyMediumText.Bold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="BodyMediumText.Bold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="BodyMediumText.Bold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="BodyMediumText.Bold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="BodyMediumText.Bold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <style name="BodyMediumText.Bold.System30">
        <item name="android:textColor">@color/system30</item>
    </style>

    <!--BodySmall Medium Text-->
    <style name="BodySmallText.Medium">
        <item name="android:fontFamily">@font/avenir_next_medium</item>
    </style>

    <style name="BodySmallText.Medium.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="BodySmallText.Medium.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="BodySmallText.Medium.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="BodySmallText.Medium.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="BodySmallText.Medium.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="BodySmallText.Medium.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="BodySmallText.Medium.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="BodySmallText.Medium.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="BodySmallText.Medium.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="BodySmallText.Medium.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="BodySmallText.Medium.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="BodySmallText.Medium.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="BodySmallText.Medium.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <style name="BodySmallText.Medium.System30">
        <item name="android:textColor">@color/system30</item>
    </style>

    <style name="BodySmallText.Medium.System20">
        <item name="android:textColor">@color/system20</item>
    </style>

    <!--BodySmall DemiBold Text-->
    <style name="BodySmallText.DemiBold">
        <item name="android:fontFamily">@font/bri_digital_text_semi_bold</item>
    </style>

    <style name="BodySmallText.DemiBold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="BodySmallText.DemiBold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="BodySmallText.DemiBold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="BodySmallText.DemiBold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="BodySmallText.DemiBold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="BodySmallText.DemiBold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="BodySmallText.DemiBold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="BodySmallText.DemiBold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="BodySmallText.DemiBold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="BodySmallText.DemiBold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="BodySmallText.DemiBold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="BodySmallText.DemiBold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="BodySmallText.DemiBold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <!--BodySmall Bold Text-->
    <style name="BodySmallText.Bold">
        <item name="android:fontFamily">@font/avenir_next_bold</item>
    </style>

    <style name="BodySmallText.Bold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="BodySmallText.Bold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="BodySmallText.Bold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="BodySmallText.Bold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="BodySmallText.Bold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="BodySmallText.Bold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="BodySmallText.Bold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="BodySmallText.Bold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="BodySmallText.Bold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="BodySmallText.Bold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="BodySmallText.Bold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="BodySmallText.Bold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="BodySmallText.Bold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>
    .

    <!--Caption Medium Text-->
    <style name="CaptionText.Medium">
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <style name="CaptionText.Medium.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="CaptionText.Medium.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="CaptionText.Medium.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="CaptionText.Medium.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="CaptionText.Medium.PrimaryBlue20">
        <item name="android:textColor">@color/primary_blue20</item>
    </style>

    <style name="CaptionText.Medium.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="CaptionText.Medium.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="CaptionText.Medium.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="CaptionText.Medium.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="CaptionText.Medium.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="CaptionText.Medium.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="CaptionText.Medium.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="CaptionText.Medium.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="CaptionText.Medium.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>
    .

    <!--Caption Bold Text-->
    <style name="CaptionText.Bold">
        <item name="android:fontFamily">@font/avenir_next_bold</item>
    </style>

    <style name="CaptionText.Bold.BluePrimary">
        <item name="android:textColor">@color/primaryColor</item>
    </style>

    <style name="CaptionText.Bold.Orange">
        <item name="android:textColor">@color/secondaryColor</item>
    </style>

    <style name="CaptionText.Bold.BlueLight">
        <item name="android:textColor">@color/highlightColor</item>
    </style>

    <style name="CaptionText.Bold.BlueHeader">
        <item name="android:textColor">@color/headerColor</item>
    </style>

    <style name="CaptionText.Bold.Accent1">
        <item name="android:textColor">@color/accent1Color</item>
    </style>

    <style name="CaptionText.Bold.Accent2">
        <item name="android:textColor">@color/accent2Color</item>
    </style>

    <style name="CaptionText.Bold.Accent3">
        <item name="android:textColor">@color/accent3Color</item>
    </style>

    <style name="CaptionText.Bold.Grey">
        <item name="android:textColor">@color/greyColor</item>
    </style>

    <style name="CaptionText.Bold.Black">
        <item name="android:textColor">@color/blackColor</item>
    </style>

    <style name="CaptionText.Bold.White">
        <item name="android:textColor">@color/whiteColor</item>
    </style>

    <style name="CaptionText.Bold.Red">
        <item name="android:textColor">@color/dangerColor</item>
    </style>

    <style name="CaptionText.Bold.Yellow">
        <item name="android:textColor">@color/warningColor</item>
    </style>

    <style name="CaptionText.Bold.Green">
        <item name="android:textColor">@color/successColor</item>
    </style>

    <style name="CustomTabLayoutTextStyle" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:textSize">14dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/primary_blue100</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="CustomTabLayoutTextStyleRevamp" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:textSize">14dp</item>
        <item name="lineHeight">20dp</item>
        <item name="fontWeight">600</item>
        <item name="android:fontFamily">@font/bri_digital_text_semibold</item>
        <item name="android:textColor">@color/neutral_dark40</item>
        <item name="android:textAlignment">center</item>
    </style>

    <!--Button Style-->
    <style name="ButtonPrimary" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_primary_bg</item>
        <item name="android:textColor">@color/whiteColor</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/avenir_next_bold</item>
    </style>

    <style name="ButtonPrimaryRevamp" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_primary_bg</item>
        <item name="android:textColor">@color/whiteColor</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonPrimaryRevamp.Blue80Light20." parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_primary_bg_blue80_light20</item>
        <item name="android:textColor">@drawable/button_primary_bg_text_blue80_light20</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonPrimaryRevampOutline" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_primary_outline_bg</item>
        <item name="android:textColor">@color/primary_blue80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="iconTint">@color/primary_blue80</item>
    </style>

    <style name="ButtonPrimaryRevampMaterial" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:background">@drawable/button_primary_fragment_bg</item>
        <item name="android:textColor">@color/whiteColor</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="fontFamily">@font/bri_digital_text_bold</item>
        <item name="iconTint">@color/white</item>
        <item name="iconPadding">8dp</item>
    </style>

    <style name="ButtonPrimaryWhite" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:background">@drawable/button_primary_white_bg</item>
        <item name="android:textColor">@color/primaryBlue80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonPrimaryOrange" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:background">@drawable/button_primary_orange80_bg</item>
        <item name="android:textColor">@color/neutral_baseWhite</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonPrimary.OutlineMaterial" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:theme">@style/AppThemeBlueBarRevamp</item>
        <item name="android:background">@drawable/button_primary_outline_bg</item>
        <item name="android:textColor">@color/primary_blue80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/bri_digital_text_bold</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="iconTint">@color/primary_blue80</item>
        <item name="iconPadding">8dp</item>
    </style>

    <style name="ButtonPrimary.OutlineMaterial.Red" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/semanticRed80</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textColor">@color/semanticRed80</item>
        <item name="iconTint">@color/semanticRed80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="iconPadding">8dp</item>
        <item name="android:padding">@dimen/space_x1_half</item>
    </style>

    <style name="ButtonPrimary.Outline" parent="ButtonPrimary">
        <item name="android:background">@drawable/button_primary_outline_bg</item>
        <item name="android:textColor">@color/primary_blue80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="iconTint">@color/primary_blue80</item>
    </style>

    <style name="ButtonPrimaryRevamp.Outline" parent="ButtonPrimaryRevamp">
        <item name="android:background">@drawable/button_primary_outline_bg</item>
        <item name="android:textColor">@color/primary_blue80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonPrimaryRevamp.Outline.Blue80Light20" parent="ButtonPrimaryRevamp">
        <item name="android:background">@drawable/button_primary_outline_bg_blue80_light20</item>
        <item name="android:textColor">@drawable/button_primary_outline_bg_text_blue80_light20</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonPrimaryRevamp.Outline.Revamp" parent="ButtonPrimaryRevamp">
        <item name="android:background">@drawable/bg_primary_outline_revamp_bg</item>
        <item name="android:textColor">@color/primary_blue80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonPrimaryRevamp.OutlineTransparent" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:textColor">@color/primary_blue80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonSecondaryRevamp" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_secondary_bg</item>
        <item name="android:textColor">@color/whiteColor</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonTertiaryRevamp" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_tertiary_bg</item>
        <item name="android:textColor">@color/primary_blue80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonWhiteOutline" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_white_outline_bg</item>
        <item name="android:textColor">@color/primary_blue80</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="iconTint">@color/primary_blue80</item>
    </style>

    <style name="ButtonSecondary" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_secondary_bg</item>
        <item name="android:textColor">@color/whiteColor</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="ButtonSecondaryWhiteDisable" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_secondary_white</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <!--Button New Skin-->
    <style name="ButtonPrimaryNewSkin" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_primary_new_skin</item>
        <item name="android:textColor">@color/selector_button_primary_text_new_skin</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_semi_bold</item>
    </style>

    <style name="ButtonSecondaryWhiteNewSkin" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_secondary_white_background</item>
        <item name="android:textColor">@color/colorBlue_0054F3</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_semi_bold</item>
    </style>


    <style name="ButtonPrimaryBorderNewSkin" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/button_primary_outline_bg_newskin</item>
        <item name="android:textColor">@color/selector_text_color_button_primary_border</item>
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_semi_bold</item>
    </style>


    <!--Search View-->
    <style name="AppSearchView" parent="Widget.AppCompat.Light.SearchView">
        <item name="android:textSize">@dimen/body_medium_text</item>
    </style>

    <style name="AppSearchViewSmall" parent="Widget.AppCompat.Light.SearchView">
        <item name="android:textSize">@dimen/body_medium_text</item>
        <item name="fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="AppSearchViewSmallText" parent="Widget.AppCompat.Light.SearchView">
        <item name="android:textSize">@dimen/micro_text</item>
    </style>

    <!-- TEXT STYLE REVAMP -->
    <!-- TITLE -->
    <!-- Text Size Title -->
    <style name="Title1Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/text_h1</item>
    </style>

    <style name="Title2Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/text_h2</item>
    </style>

    <style name="Title3Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/text_h3</item>
    </style>

    <style name="Title4Text" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/text_h4</item>
    </style>

    <!-- Font Text Title Light -->
    <style name="Title1Text.Light">
        <item name="android:fontFamily">@font/bri_digital_text_light</item>
    </style>

    <style name="Title2Text.Light">
        <item name="android:fontFamily">@font/bri_digital_text_light</item>
    </style>

    <style name="Title3Text.Light">
        <item name="android:fontFamily">@font/bri_digital_text_light</item>
    </style>

    <style name="Title4Text.Light">
        <item name="android:fontFamily">@font/bri_digital_text_light</item>
    </style>

    <!-- Font Text Title Regular -->
    <style name="Title1Text.Regular">
        <item name="android:fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="Title2Text.Regular">
        <item name="android:fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="Title3Text.Regular">
        <item name="android:fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="Title4Text.Regular">
        <item name="android:fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <!-- Font Text Title Medium -->
    <style name="Title1Text.Medium">
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <style name="Title2Text.Medium">
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <style name="Title3Text.Medium">
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <style name="Title4Text.Medium">
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <!-- Font Text Title SemiBold -->
    <style name="Title1Text.SemiBold">
        <item name="android:fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <style name="Title2Text.SemiBold">
        <item name="android:fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <style name="Title3Text.SemiBold">
        <item name="android:fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <style name="Title4Text.SemiBold">
        <item name="android:fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <!-- Font Text Title Bold -->
    <style name="Title1Text.Bold">
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="Title2Text.Bold">
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="Title3Text.Bold">
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="Title4Text.Bold">
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <!-- Font Text Title Heavy -->
    <style name="Title1Text.Heavy">
        <item name="android:fontFamily">@font/bri_digital_text_heavy</item>
    </style>

    <style name="Title2Text.Heavy">
        <item name="android:fontFamily">@font/bri_digital_text_heavy</item>
    </style>

    <style name="Title3Text.Heavy">
        <item name="android:fontFamily">@font/bri_digital_text_heavy</item>
    </style>

    <style name="Title4Text.Heavy">
        <item name="android:fontFamily">@font/bri_digital_text_heavy</item>
    </style>

    <!-- Font Text Title Light Color Primary Blue 80 -->
    <style name="Title1Text.Light.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title2Text.Light.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title3Text.Light.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title4Text.Light.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Title Light Color Primary Blue 70 -->
    <style name="Title1Text.Light.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title2Text.Light.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title3Text.Light.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title4Text.Light.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Title Light Color Neutral Base White -->
    <style name="Title1Text.Light.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title2Text.Light.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title3Text.Light.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title4Text.Light.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Title Light Color Neutral Base Black -->
    <style name="Title1Text.Light.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title2Text.Light.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title3Text.Light.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title4Text.Light.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Title Light Color Neutral Dark 40 -->
    <style name="Title1Text.Light.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title2Text.Light.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title3Text.Light.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title4Text.Light.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Title Light Color Neutral Dark 10 -->
    <style name="Title1Text.Light.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title2Text.Light.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title3Text.Light.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title4Text.Light.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Title Light Color Neutral Light 60 -->
    <style name="Title1Text.Light.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title2Text.Light.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title3Text.Light.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title4Text.Light.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Title Light Color Success -->
    <style name="Title1Text.Light.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title2Text.Light.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title3Text.Light.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title4Text.Light.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Title Light Color Warning -->
    <style name="Title1Text.Light.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title2Text.Light.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title3Text.Light.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title4Text.Light.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Title Light Color Error -->
    <style name="Title1Text.Light.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title2Text.Light.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title3Text.Light.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title4Text.Light.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Title Regular Color Primary Blue 80 -->
    <style name="Title1Text.Regular.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title2Text.Regular.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title3Text.Regular.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title4Text.Regular.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Title Regular Color Primary Blue 70 -->
    <style name="Title1Text.Regular.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title2Text.Regular.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title3Text.Regular.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title4Text.Regular.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Title Regular Color Neutral Base White -->
    <style name="Title1Text.Regular.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title2Text.Regular.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title3Text.Regular.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title4Text.Regular.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Title Regular Color Neutral Base Black -->
    <style name="Title1Text.Regular.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title2Text.Regular.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title3Text.Regular.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title4Text.Regular.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Title Regular Color Neutral Dark 40 -->
    <style name="Title1Text.Regular.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title2Text.Regular.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title3Text.Regular.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title4Text.Regular.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Title Regular Color Neutral Dark 10 -->
    <style name="Title1Text.Regular.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title2Text.Regular.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title3Text.Regular.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title4Text.Regular.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Title Regular Color Neutral Light 60 -->
    <style name="Title1Text.Regular.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title2Text.Regular.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title3Text.Regular.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title4Text.Regular.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Title Regular Color Success -->
    <style name="Title1Text.Regular.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title2Text.Regular.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title3Text.Regular.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title4Text.Regular.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Title Regular Color Warning -->
    <style name="Title1Text.Regular.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title2Text.Regular.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title3Text.Regular.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title4Text.Regular.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Title Regular Color Error -->
    <style name="Title1Text.Regular.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title2Text.Regular.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title3Text.Regular.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title4Text.Regular.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Title Medium Color Primary Blue 80 -->
    <style name="Title1Text.Medium.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title2Text.Medium.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title3Text.Medium.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title4Text.Medium.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Title Medium Color Primary Blue 70 -->
    <style name="Title1Text.Medium.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title2Text.Medium.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title3Text.Medium.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title4Text.Medium.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Title Medium Color Neutral Base White -->
    <style name="Title1Text.Medium.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title2Text.Medium.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title3Text.Medium.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title4Text.Medium.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Title Medium Color Neutral Base Black -->
    <style name="Title1Text.Medium.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title2Text.Medium.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title3Text.Medium.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title4Text.Medium.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Title Medium Color Neutral Dark 40 -->
    <style name="Title1Text.Medium.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title2Text.Medium.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title3Text.Medium.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title4Text.Medium.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Title Medium Color Neutral Dark 10 -->
    <style name="Title1Text.Medium.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title2Text.Medium.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title3Text.Medium.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title4Text.Medium.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Title Medium Color Neutral Light 60 -->
    <style name="Title1Text.Medium.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title2Text.Medium.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title3Text.Medium.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title4Text.Medium.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Title Medium Color Success -->
    <style name="Title1Text.Medium.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title2Text.Medium.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title3Text.Medium.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title4Text.Medium.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Title Medium Color Warning -->
    <style name="Title1Text.Medium.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title2Text.Medium.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title3Text.Medium.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title4Text.Medium.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Title Medium Color Error -->
    <style name="Title1Text.Medium.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title2Text.Medium.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title3Text.Medium.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title4Text.Medium.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Title Semi Bold Color Neutral Dark 40 -->
    <style name="Title1Text.SemiBold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title2Text.SemiBold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title3Text.SemiBold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title4Text.SemiBold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Title Bold Color Primary Blue 100 -->
    <style name="Title1Text.Bold.Primary100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <style name="Title2Text.Bold.Primary100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <style name="Title3Text.Bold.Primary100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <style name="Title4Text.Bold.Primary100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <!-- Font Text Title Bold Color Primary Blue 80 -->
    <style name="Title1Text.Bold.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title2Text.Bold.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title3Text.Bold.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title4Text.Bold.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Title Bold Color Primary Blue 70 -->
    <style name="Title1Text.Bold.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title2Text.Bold.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title3Text.Bold.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title4Text.Bold.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Title Bold Color Neutral Base White -->
    <style name="Title1Text.Bold.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Title2Text.Bold.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Title3Text.Bold.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Title4Text.Bold.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <!-- Font Text Title Bold Color Neutral Base White -->
    <style name="Title1Text.Bold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title2Text.Bold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title3Text.Bold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title4Text.Bold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Title Bold Color Neutral Base Black -->
    <style name="Title1Text.Bold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title2Text.Bold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title3Text.Bold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title4Text.Bold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Title Bold Color Neutral Dark 40 -->
    <style name="Title1Text.Bold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title2Text.Bold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title3Text.Bold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title4Text.Bold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Title Bold Color Neutral Dark 10 -->
    <style name="Title1Text.Bold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title2Text.Bold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title3Text.Bold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title4Text.Bold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Title Bold Color Neutral Light 60 -->
    <style name="Title1Text.Bold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title2Text.Bold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title3Text.Bold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title4Text.Bold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Title Bold Color Success -->
    <style name="Title1Text.Bold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title2Text.Bold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title3Text.Bold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title4Text.Bold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Title Bold Color Warning -->
    <style name="Title1Text.Bold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title2Text.Bold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title3Text.Bold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title4Text.Bold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Title Bold Color Error -->
    <style name="Title1Text.Bold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title2Text.Bold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title3Text.Bold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title4Text.Bold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Title Bold Color Primary Orange 80 -->
    <style name="Title1Text.Bold.PrimaryOrange80">
        <item name="android:textColor">@color/primary_orange80</item>
    </style>

    <style name="Title2Text.Bold.PrimaryOrange80">
        <item name="android:textColor">@color/primary_orange80</item>
    </style>

    <style name="Title3Text.Bold.PrimaryOrange80">
        <item name="android:textColor">@color/primary_orange80</item>
    </style>

    <style name="Title4Text.Bold.PrimaryOrange80">
        <item name="android:textColor">@color/primary_orange80</item>
    </style>

    <!-- Font Text Title Heavy Color Primary Blue 80 -->
    <style name="Title1Text.Heavy.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title2Text.Heavy.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title3Text.Heavy.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Title4Text.Heavy.Primary80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Title Heavy Color Primary Blue 70 -->
    <style name="Title1Text.Heavy.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title2Text.Heavy.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title3Text.Heavy.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Title4Text.Heavy.Primary70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Title Heavy Color Neutral Base White -->
    <style name="Title1Text.Heavy.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title2Text.Heavy.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title3Text.Heavy.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Title4Text.Heavy.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Title Heavy Color Neutral Base Black -->
    <style name="Title1Text.Heavy.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title2Text.Heavy.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title3Text.Heavy.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Title4Text.Heavy.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Title Heavy Color Neutral Dark 40 -->
    <style name="Title1Text.Heavy.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title2Text.Heavy.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title3Text.Heavy.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Title4Text.Heavy.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Title Heavy Color Neutral Dark 10 -->
    <style name="Title1Text.Heavy.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title2Text.Heavy.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title3Text.Heavy.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Title4Text.Heavy.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Title Heavy Color Neutral Light 60 -->
    <style name="Title1Text.Heavy.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title2Text.Heavy.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title3Text.Heavy.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Title4Text.Heavy.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Title Heavy Color Success -->
    <style name="Title1Text.Heavy.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title2Text.Heavy.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title3Text.Heavy.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Title4Text.Heavy.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Title Heavy Color Warning -->
    <style name="Title1Text.Heavy.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title2Text.Heavy.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title3Text.Heavy.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Title4Text.Heavy.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Title Heavy Color Error -->
    <style name="Title1Text.Heavy.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title2Text.Heavy.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title3Text.Heavy.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Title4Text.Heavy.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- BODY -->
    <!-- Text Size Body -->
    <style name="Body1LargeText" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/text_body1</item>
    </style>

    <style name="Body2MediumText" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/text_body2</item>
    </style>

    <style name="Body3SmallText" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/text_body3</item>
    </style>

    <!-- Font Text Body Light -->
    <style name="Body1LargeText.Light">
        <item name="fontFamily">@font/bri_digital_text_light</item>
    </style>

    <style name="Body2MediumText.Light">
        <item name="fontFamily">@font/bri_digital_text_light</item>
    </style>

    <style name="Body3SmallText.Light">
        <item name="fontFamily">@font/bri_digital_text_light</item>
    </style>

    <!-- Font Text Body Regular -->
    <style name="Body1LargeText.Regular">
        <item name="fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="Body2MediumText.Regular">
        <item name="fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="Body3SmallText.Regular">
        <item name="fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <!-- Font Text Body Medium -->
    <style name="Body1LargeText.Medium">
        <item name="fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <style name="Body2MediumText.Medium">
        <item name="fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <style name="Body3SmallText.Medium">
        <item name="fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <!-- Font Text Body SemiBold -->
    <style name="Body1LargeText.SemiBold">
        <item name="fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <style name="Body2MediumText.SemiBold">
        <item name="fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <style name="Body3SmallText.SemiBold">
        <item name="fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <!-- Font Text Body Bold -->
    <style name="Body1LargeText.Bold">
        <item name="fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="Body2MediumText.Bold">
        <item name="fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="Body3SmallText.Bold">
        <item name="fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <!-- Font Text Body Heavy -->
    <style name="Body1LargeText.Heavy">
        <item name="fontFamily">@font/bri_digital_text_heavy</item>
    </style>

    <style name="Body2MediumText.Heavy">
        <item name="fontFamily">@font/bri_digital_text_heavy</item>
    </style>

    <style name="Body3SmallText.Heavy">
        <item name="fontFamily">@font/bri_digital_text_heavy</item>
    </style>

    <!-- Font Text Body Light Color Primary Blue 80 -->
    <style name="Body1LargeText.Light.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body2MediumText.Light.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body3SmallText.Light.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Body Light Color Primary Blue 70 -->
    <style name="Body1LargeText.Light.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body2MediumText.Light.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body3SmallText.Light.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Body Light Color Neutral Base White -->
    <style name="Body1LargeText.Light.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body2MediumText.Light.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body3SmallText.Light.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Body Light Color Neutral Base Black -->
    <style name="Body1LargeText.Light.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body2MediumText.Light.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body3SmallText.Light.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Body Light Color Neutral Dark 40 -->
    <style name="Body1LargeText.Light.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body2MediumText.Light.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body3SmallText.Light.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Body Light Color Neutral Dark 10 -->
    <style name="Body1LargeText.Light.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body2MediumText.Light.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body3SmallText.Light.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Body Light Color Light 60 -->
    <style name="Body1LargeText.Light.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body2MediumText.Light.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body3SmallText.Light.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Body Light Color Success -->
    <style name="Body1LargeText.Light.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body2MediumText.Light.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body3SmallText.Light.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Body Light Color Warning -->
    <style name="Body1LargeText.Light.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body2MediumText.Light.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body3SmallText.Light.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Body Light Color Error -->
    <style name="Body1LargeText.Light.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body2MediumText.Light.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body3SmallText.Light.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Body Regular Color Primary Blue 80 -->
    <style name="Body1LargeText.Regular.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body2MediumText.Regular.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body3SmallText.Regular.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Body Regular Color Primary Blue 70 -->
    <style name="Body1LargeText.Regular.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body2MediumText.Regular.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body3SmallText.Regular.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Body Regular Color Neutral Base White -->
    <style name="Body1LargeText.Regular.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body2MediumText.Regular.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body3SmallText.Regular.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Body Regular Color Neutral Base Black -->
    <style name="Body1LargeText.Regular.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body2MediumText.Regular.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body3SmallText.Regular.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Body Regular Color Neutral Dark 40 -->
    <style name="Body1LargeText.Regular.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body2MediumText.Regular.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body3SmallText.Regular.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Body Regular Color Neutral Dark 10 -->
    <style name="Body1LargeText.Regular.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body2MediumText.Regular.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body3SmallText.Regular.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Body Regular Color Light 20 -->
    <style name="Body1LargeText.Regular.NeutralLight20">
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <style name="Body2MediumText.Regular.NeutralLight20">
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <style name="Body3SmallText.Regular.NeutralLight20">
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <!-- Font Text Body Regular Color Light 60 -->
    <style name="Body1LargeText.Regular.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body2MediumText.Regular.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body3SmallText.Regular.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Body Regular Color Light 80 -->
    <style name="Body1LargeText.Regular.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Body2MediumText.Regular.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Body3SmallText.Regular.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <!-- Font Text Body Regular Color Success -->
    <style name="Body1LargeText.Regular.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body2MediumText.Regular.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body3SmallText.Regular.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body1LargeText.SemiBold.NSPrimary500">
        <item name="android:textColor">@color/ns_primary500</item>
    </style>

    <style name="Body2MediumText.SemiBold.NSPrimary500">
        <item name="android:textColor">@color/ns_primary500</item>
    </style>

    <style name="Body3SmallText.SemiBold.NSPrimary500">
        <item name="android:textColor">@color/ns_primary500</item>
    </style>

    <!-- Font Text Body Regular Color Warning -->
    <style name="Body1LargeText.Regular.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body2MediumText.Regular.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body3SmallText.Regular.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Body Regular Color Error -->
    <style name="Body1LargeText.Regular.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body2MediumText.Regular.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body3SmallText.Regular.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Body Medium Color Primary Blue 80 -->
    <style name="Body1LargeText.Medium.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body2MediumText.Medium.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body3SmallText.Medium.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Body Medium Color Primary Blue 70 -->
    <style name="Body1LargeText.Medium.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body2MediumText.Medium.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body3SmallText.Medium.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Body Medium Color Neutral Base White -->
    <style name="Body1LargeText.Medium.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body2MediumText.Medium.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Body Medium Color Neutral Base Black -->
    <style name="Body1LargeText.Medium.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body2MediumText.Medium.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Body Medium Color Neutral Dark 40 -->
    <style name="Body1LargeText.Medium.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body2MediumText.Medium.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Body Medium Color Neutral Dark 10 -->
    <style name="Body1LargeText.Medium.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body2MediumText.Medium.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <!-- Font Text Body Medium Color Light 60 -->
    <style name="Body1LargeText.Medium.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body2MediumText.Medium.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Body Medium Color Light 80 -->
    <style name="Body1LargeText.Medium.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Body2MediumText.Medium.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <!-- Font Text Body Medium Color Success -->
    <style name="Body1LargeText.Medium.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body2MediumText.Medium.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body3SmallText.Medium.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Body Medium Color Warning -->
    <style name="Body1LargeText.Medium.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body2MediumText.Medium.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body3SmallText.Medium.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Body Medium Color Error -->
    <style name="Body1LargeText.Medium.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body2MediumText.Medium.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body3SmallText.Medium.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Body Semi Bold Color Primary Blue 100 -->
    <style name="Body1LargeText.SemiBold.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <style name="Body2MediumText.SemiBold.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <style name="Body3SmallText.SemiBold.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <!-- Font Text Body Semi Bold Color Primary Blue 80 -->
    <style name="Body1LargeText.SemiBold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body2MediumText.SemiBold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body3SmallText.SemiBold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Body Semi Bold Color Primary Blue 70 -->
    <style name="Body1LargeText.SemiBold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body2MediumText.SemiBold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body3SmallText.SemiBold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Body Semi Bold Color Neutral Base White -->
    <style name="Body1LargeText.SemiBold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body2MediumText.SemiBold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body3SmallText.SemiBold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Body Semi Bold Color Neutral Base Black -->
    <style name="Body1LargeText.SemiBold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body2MediumText.SemiBold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body3SmallText.SemiBold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Body Semi Bold Color Neutral Dark 40 -->
    <style name="Body1LargeText.SemiBold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body2MediumText.SemiBold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body3SmallText.SemiBold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Body Semi Bold Color Neutral Dark 10 -->
    <style name="Body1LargeText.SemiBold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body2MediumText.SemiBold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body3SmallText.SemiBold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Body Semi Bold Color Light 60 -->
    <style name="Body1LargeText.SemiBold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body2MediumText.SemiBold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body3SmallText.SemiBold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Body Semi Bold Color Light 80 -->
    <style name="Body1LargeText.SemiBold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Body2MediumText.SemiBold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Body3SmallText.SemiBold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <!-- Font Text Body Semi Bold Color Neutral Dark 20 -->
    <style name="Body1LargeText.SemiBold.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <style name="Body2MediumText.SemiBold.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <style name="Body3SmallText.SemiBold.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <!-- Font Text Body Semi Bold Color Success -->
    <style name="Body1LargeText.SemiBold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body2MediumText.SemiBold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body3SmallText.SemiBold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Body Semi Bold Color Warning -->
    <style name="Body1LargeText.SemiBold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body2MediumText.SemiBold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body3SmallText.SemiBold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Body Semi Bold Color Error -->
    <style name="Body1LargeText.SemiBold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body2MediumText.SemiBold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body3SmallText.SemiBold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Body Bold Color Primary Blue 100-->
    <style name="Body1LargeText.Bold.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body2MediumText.Bold.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <style name="Body3SmallText.Bold.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <!-- Font Text Body Bold Color Primary Blue 80-->
    <style name="Body1LargeText.Bold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body2MediumText.Bold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body3SmallText.Bold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Body Bold Color Primary Blue 70-->
    <style name="Body1LargeText.Bold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body2MediumText.Bold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body3SmallText.Bold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Body Bold Color colorBlue_0054F3-->
    <style name="Body1LargeText.Bold.colorBlue_0054F3">
        <item name="android:textColor">@color/colorBlue_0054F3</item>
    </style>

    <style name="Body2MediumText.Bold.colorBlue_0054F3">
        <item name="android:textColor">@color/colorBlue_0054F3</item>
    </style>

    <style name="Body3SmallText.Bold.colorBlue_0054F3">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Base White -->
    <style name="Body1LargeText.Bold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body2MediumText.Bold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body3SmallText.Bold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Base Black -->
    <style name="Body1LargeText.Bold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body2MediumText.Bold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body3SmallText.Bold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Dark 40 -->
    <style name="Body1LargeText.Bold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body2MediumText.Bold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body3SmallText.Bold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Dark 20 -->
    <style name="Body1LargeText.Bold.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <style name="Body2MediumText.Bold.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <style name="Body3SmallText.Bold.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Dark 10 -->
    <style name="Body1LargeText.Bold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body2MediumText.Bold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body3SmallText.Bold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Light 10 -->
    <style name="Body1LargeText.Bold.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Body2MediumText.Bold.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Body3SmallText.Bold.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Light 40 -->
    <style name="Body1LargeText.Bold.NeutralLight40">
        <item name="android:textColor">@color/neutral_light40</item>
    </style>

    <style name="Body2MediumText.Bold.NeutralLight40">
        <item name="android:textColor">@color/neutral_light40</item>
    </style>

    <style name="Body3SmallText.Bold.NeutralLight40">
        <item name="android:textColor">@color/neutral_light40</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Light 60 -->
    <style name="Body1LargeText.Bold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body2MediumText.Bold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body3SmallText.Bold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Light 80 -->
    <style name="Body1LargeText.Bold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Body2MediumText.Bold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Body3SmallText.Bold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Success -->
    <style name="Body1LargeText.Bold.Succcess">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body2MediumText.Bold.Succcess">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body3SmallText.Bold.Succcess">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Warning -->
    <style name="Body1LargeText.Bold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body2MediumText.Bold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body3SmallText.Bold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Body Bold Color Neutral Error -->
    <style name="Body1LargeText.Bold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body2MediumText.Bold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body3SmallText.Bold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Body Heavy Color Primary Blue 80-->
    <style name="Body1LargeText.Heavy.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body2MediumText.Heavy.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Body3SmallText.Heavy.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Body Bold Color Primary Blue 70-->
    <style name="Body1LargeText.Heavy.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body2MediumText.Heavy.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Body3SmallText.Heavy.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Body Heavy Color Neutral Base White -->
    <style name="Body1LargeText.Heavy.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body2MediumText.Heavy.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Body3SmallText.Heavy.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Body Heavy Color Neutral Base Black -->
    <style name="Body1LargeText.Heavy.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body2MediumText.Heavy.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Body3SmallText.Heavy.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Body Heavy Color Neutral Dark 40 -->
    <style name="Body1LargeText.Heavy.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body2MediumText.Heavy.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Body3SmallText.Heavy.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Body Heavy Color Neutral Dark 10 -->
    <style name="Body1LargeText.Heavy.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body2MediumText.Heavy.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Body3SmallText.Heavy.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Body Heavy Color Neutral Light 60 -->
    <style name="Body1LargeText.Heavy.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body2MediumText.Heavy.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Body3SmallText.Heavy.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Body Heavy Color Neutral Success -->
    <style name="Body1LargeText.Heavy.Succcess">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body2MediumText.Heavy.Succcess">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Body3SmallText.Heavy.Succcess">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Heavy Bold Color Neutral Error -->
    <style name="Body1LargeText.Heavy.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body2MediumText.Heavy.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Body3SmallText.Heavy.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Body Heavy Color Neutral Warning -->
    <style name="Body1LargeText.Heavy.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body2MediumText.Heavy.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Body3SmallText.Heavy.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- CAPTION -->
    <!-- Text Size Caption -->
    <style name="Caption1SmallText" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/text_caption1</item>
    </style>

    <style name="Caption2MicroText" parent="BaseAppTheme.Text">
        <item name="android:textSize">@dimen/text_caption2</item>
    </style>

    <!-- Font Text Caption Light -->
    <style name="Caption1SmallText.Light">
        <item name="android:fontFamily">@font/bri_digital_text_light</item>
    </style>

    <style name="Caption2MicroText.Light">
        <item name="android:fontFamily">@font/bri_digital_text_light</item>
    </style>

    <!-- Font Text Caption Regular -->
    <style name="Caption1SmallText.Regular">
        <item name="android:fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="Caption2MicroText.Regular">
        <item name="android:fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <!-- Font Text Caption Medium -->
    <style name="Caption1SmallText.Medium">
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <style name="Caption2MicroText.Medium">
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <!-- Font Text Caption SemiBold -->
    <style name="Caption1SmallText.SemiBold">
        <item name="android:fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <style name="Caption2MicroText.SemiBold">
        <item name="android:fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <!-- Font Text Caption Medium Color Sematic Red 80  -->
    <style name="Caption1SmallText.Medium.SematicRed80">
        <item name="android:textColor">@color/semanticRed80</item>
    </style>

    <!-- Font Text Caption Bold -->
    <style name="Caption1SmallText.Bold">
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <style name="Caption2MicroText.Bold">
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>

    <!-- Font Text Caption Heavy -->
    <style name="Caption1SmallText.Heavy">
        <item name="android:fontFamily">@font/bri_digital_text_heavy</item>
    </style>

    <style name="Caption2MicroText.Heavy">
        <item name="android:fontFamily">@font/bri_digital_text_heavy</item>
    </style>

    <!-- Font Text Caption Light Color Primary Blue 80 -->
    <style name="Caption1SmallText.Light.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Caption2MicroText.Light.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Caption Light Color Primary Blue 70 -->
    <style name="Caption1SmallText.Light.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Caption2MicroText.Light.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Caption Light Color Neutral Base White -->
    <style name="Caption1SmallText.Light.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Caption2MicroText.Light.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Caption Light Color Neutral Base Black -->
    <style name="Caption1SmallText.Light.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Caption2MicroText.Light.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Caption Light Color Neutral Dark 40 -->
    <style name="Caption1SmallText.Light.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Caption2MicroText.Light.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Caption Light Color Neutral Dark 10 -->
    <style name="Caption1SmallText.Light.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Caption2MicroText.Light.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Caption Light Color Neutral Light 60 -->
    <style name="Caption1SmallText.Light.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Caption2MicroText.Light.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Caption Light Color Success -->
    <style name="Caption1SmallText.Light.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Caption2MicroText.Light.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Caption Light Color Warning -->
    <style name="Caption1SmallText.Light.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Caption2MicroText.Light.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Caption Light Color Error -->
    <style name="Caption1SmallText.Light.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Caption2MicroText.Light.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Caption Regular Color Primary Blue 80 -->
    <style name="Caption1SmallText.Regular.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Caption2MicroText.Regular.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Caption Regular Color Primary Blue 70 -->
    <style name="Caption1SmallText.Regular.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Caption2MicroText.Regular.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Caption Regular Color Neutral Base White -->
    <style name="Caption1SmallText.Regular.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Caption2MicroText.Regular.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Caption Regular Color Neutral Base Black -->
    <style name="Caption1SmallText.Regular.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Caption2MicroText.Regular.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Caption Regular Color Neutral Dark 40 -->
    <style name="Caption1SmallText.Regular.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Caption2MicroText.Regular.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Caption Regular Color Neutral Dark 10 -->
    <style name="Caption1SmallText.Regular.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Caption2MicroText.Regular.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Caption Regular Color Neutral Light 20 -->
    <style name="Caption1SmallText.Regular.NeutralLight20">
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <style name="Caption2MicroText.Regular.NeutralLight20">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Caption Regular Color Neutral Light 60 -->
    <style name="Caption1SmallText.Regular.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Caption2MicroText.Regular.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Caption Regular Color Neutral Light 80 -->
    <style name="Caption1SmallText.Regular.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Caption2MicroText.Regular.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <!-- Font Text Caption Regular Color Success -->
    <style name="Caption1SmallText.Regular.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Caption2MicroText.Regular.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Caption Regular Color Warning -->
    <style name="Caption1SmallText.Regular.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Caption2MicroText.Regular.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Caption Regular Color Error -->
    <style name="Caption1SmallText.Regular.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Caption2MicroText.Regular.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Caption Medium Color Primary Blue 100 -->
    <style name="Caption1SmallText.Medium.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <style name="Caption2MicroText.Medium.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <!-- Font Text Caption Medium Color Primary Blue 80 -->
    <style name="Caption1SmallText.Medium.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Caption2MicroText.Medium.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Caption Medium Color Primary Blue 70 -->
    <style name="Caption1SmallText.Medium.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Caption2MicroText.Medium.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Base White -->
    <style name="Caption1SmallText.Medium.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Base Black -->
    <style name="Caption1SmallText.Medium.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Dark 40 -->
    <style name="Caption1SmallText.Medium.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Body Medium Color Primary Blue 70 -->
    <style name="Body1LargeText.Medium.PrimaryBlue20">
        <item name="android:textColor">@color/primary_blue20</item>
    </style>

    <style name="Body2MediumText.Medium.PrimaryBlue20">
        <item name="android:textColor">@color/primary_blue20</item>
    </style>

    <style name="Body3SmallText.Medium.PrimaryBlue20">
        <item name="android:textColor">@color/primary_blue20</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Dark 20 -->
    <style name="Caption1SmallText.Medium.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Dark 10 -->
    <style name="Caption1SmallText.Medium.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Light 60 -->
    <style name="Caption1SmallText.Medium.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Light 80 -->
    <style name="Caption1SmallText.Medium.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Light 10 -->
    <style name="Caption1SmallText.Medium.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <!-- Font Text Caption Medium Color Success -->
    <style name="Caption1SmallText.Medium.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Caption2MicroText.Medium.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Caption Medium Color Warning -->
    <style name="Caption1SmallText.Medium.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Caption2MicroText.Medium.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Caption Medium Color Error -->
    <style name="Caption1SmallText.Medium.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Caption Medium Color gray -->
    <style name="Caption1SmallText.Medium.System20">
        <item name="android:textColor">@color/system20</item>
    </style>

    <style name="Caption2MicroText.Medium.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Caption SemiBold Color Primary Blue 100 -->
    <style name="Caption1SmallText.SemiBold.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <style name="Caption2MicroText.SemiBold.PrimaryBlue100">
        <item name="android:textColor">@color/primary_blue100</item>
    </style>

    <!-- Font Text Caption SemiBold Color Primary Blue 80 -->
    <style name="Caption1SmallText.SemiBold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Caption2MicroText.SemiBold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Caption SemiBold Color Primary Blue 70 -->
    <style name="Caption1SmallText.SemiBold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Caption2MicroText.SemiBold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>
    <style name="Caption2MicroText.SemiBold.SemanticRed80" >
        <item name="android:textColor">@color/semanticRed80</item>
    </style>
    <!-- Font Text Caption SemiBold Color Neutral Base White -->
    <style name="Caption1SmallText.SemiBold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Caption2MicroText.SemiBold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Caption SemiBold Color Primary Orange -->
    <style name="Caption1SmallText.SemiBold.PrimaryOrange10" >
        <item name="android:textColor">@color/primary_orange10</item>
    </style>
    <style name="Caption2MicroText.SemiBold.PrimaryOrange10" >
        <item name="android:textColor">@color/primary_orange10</item>
    </style>

    <!-- Font Text Caption SemiBold Color Neutral Base Black -->
    <style name="Caption1SmallText.SemiBold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Caption2MicroText.SemiBold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Caption SemiBold Color Neutral Dark 40 -->
    <style name="Caption1SmallText.SemiBold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Caption2MicroText.SemiBold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Caption SemiBold Color Neutral Dark 10 -->
    <style name="Caption1SmallText.SemiBold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Caption2MicroText.SemiBold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Caption SemiBold Color Neutral Light 20 -->
    <style name="Caption1SmallText.SemiBold.NeutralLight20" >
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <!-- Font Text Caption SemiBold Color Neutral Light 20 -->
    <style name="Caption2MicroText.SemiBold.NeutralLight20" >
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <!-- Font Text Caption SemiBold Color Neutral Light 60 -->
    <style name="Caption1SmallText.SemiBold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Caption2MicroText.SemiBold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Caption SemiBold Color Neutral Light 80 -->
    <style name="Caption1SmallText.SemiBold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Caption2MicroText.SemiBold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <!-- Font Text Caption SemiBold Color Success -->
    <style name="Caption1SmallText.SemiBold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Caption2MicroText.SemiBold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Caption SemiBold Color Warning -->
    <style name="Caption1SmallText.SemiBold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Caption2MicroText.SemiBold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Caption SemiBold Color Error -->
    <style name="Caption1SmallText.SemiBold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Caption2MicroText.SemiBold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Caption Bold Color Primary Blue 80 -->
    <style name="Caption1SmallText.Bold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Caption2MicroText.Bold.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Caption Bold Color Primary Blue 70 -->
    <style name="Caption1SmallText.Bold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Caption2MicroText.Bold.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Base White -->
    <style name="Caption1SmallText.Bold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Base Black -->
    <style name="Caption1SmallText.Bold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Dark 20 -->
    <style name="Caption1SmallText.Bold.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralDark20">
        <item name="android:textColor">@color/neutral_dark20</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Dark 40 -->
    <style name="Caption1SmallText.Bold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Dark 30 -->
    <style name="Caption1SmallText.Bold.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Dark 10 -->
    <style name="Caption1SmallText.Bold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Light 10 -->
    <style name="Caption1SmallText.Bold.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Light 60 -->
    <style name="Caption1SmallText.Bold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Light 80 -->
    <style name="Caption1SmallText.Bold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralLight80">
        <item name="android:textColor">@color/neutral_light80</item>
    </style>

    <!-- Font Text Caption Bold Color Success -->
    <style name="Caption1SmallText.Bold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Caption2MicroText.Bold.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Caption Bold Color Warning -->
    <style name="Caption1SmallText.Bold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Caption2MicroText.Bold.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Caption Bold Color Error -->
    <style name="Caption1SmallText.Bold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Caption2MicroText.Bold.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Caption Heavy Color Primary Blue 80 -->
    <style name="Caption1SmallText.Heavy.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <style name="Caption2MicroText.Heavy.PrimaryBlue80">
        <item name="android:textColor">@color/primary_blue80</item>
    </style>

    <!-- Font Text Caption Heavy Color Primary Blue 70 -->
    <style name="Caption1SmallText.Heavy.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <style name="Caption2MicroText.Heavy.PrimaryBlue70">
        <item name="android:textColor">@color/primary_blue70</item>
    </style>

    <!-- Font Text Caption Heavy Color Neutral Base White -->
    <style name="Caption1SmallText.Heavy.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <style name="Caption2MicroText.Heavy.NeutralBaseWhite">
        <item name="android:textColor">@color/neutral_baseWhite</item>
    </style>

    <!-- Font Text Caption Heavy Color Neutral Base Black -->
    <style name="Caption1SmallText.Heavy.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <style name="Caption2MicroText.Heavy.NeutralBaseBlack">
        <item name="android:textColor">@color/neutral_baseBlack</item>
    </style>

    <!-- Font Text Caption Heavy Color Neutral Dark 40 -->
    <style name="Caption1SmallText.Heavy.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <style name="Caption2MicroText.Heavy.NeutralDark40">
        <item name="android:textColor">@color/neutral_dark40</item>
    </style>

    <!-- Font Text Caption Heavy Color Neutral Dark 10 -->
    <style name="Caption1SmallText.Heavy.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <style name="Caption2MicroText.Heavy.NeutralDark10">
        <item name="android:textColor">@color/neutral_dark10</item>
    </style>

    <!-- Font Text Caption Heavy Color Neutral Light 60 -->
    <style name="Caption1SmallText.Heavy.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <style name="Caption2MicroText.Heavy.NeutralLight60">
        <item name="android:textColor">@color/neutral_light60</item>
    </style>

    <!-- Font Text Caption Heavy Color Success -->
    <style name="Caption1SmallText.Heavy.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <style name="Caption2MicroText.Heavy.Success">
        <item name="android:textColor">@color/success80</item>
    </style>

    <!-- Font Text Caption Heavy Color Warning -->
    <style name="Caption1SmallText.Heavy.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <style name="Caption2MicroText.Heavy.Warning">
        <item name="android:textColor">@color/warning90</item>
    </style>

    <!-- Font Text Caption Heavy Color Error -->
    <style name="Caption1SmallText.Heavy.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <style name="Caption2MicroText.Heavy.Error">
        <item name="android:textColor">@color/error80</item>
    </style>

    <!-- Font Text Caption Bold Color Primary BRI Blue 80 -->
    <style name="Caption1SmallText.Bold.PrimaryBlueBRI80">
        <item name="android:textColor">@color/blue_BRI80</item>
    </style>

    <style name="Caption2MicroText.Bold.PrimaryBlueBRI80">
        <item name="android:textColor">@color/blue_BRI80</item>
    </style>

    <style name="HelperTextAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">@android:color/holo_green_dark</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold|italic</item>
        <item name="android:layout_marginLeft">-16dp</item>
    </style>

    <!-- Custom Number Keyboard -->
    <style name="Keyboard" />

    <style name="Keyboard.Row">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/size_48dp</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="Keyboard.Key">
        <item name="android:layout_width">0px</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textSize">@dimen/size_30dp</item>
    </style>


    <style name="Widget.App.Chip" parent="Widget.MaterialComponents.Chip.Entry">
        <item name="chipStrokeWidth">0dp</item>
        <item name="checkedIconVisible">false</item>
        <item name="checkedIconEnabled">false</item>
        <item name="checkedIcon">@null</item>
        <item name="icon">@null</item>
        <item name="closeIcon">@null</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>

    <style name="AppChip.Red" parent="Widget.App.Chip">
        <item name="chipBackgroundColor">@color/error10</item>
        <item name="chipStrokeWidth">0dp</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipEndPadding">0dp</item>
        <item name="chipStartPadding">0dp</item>
        <item name="paddingTopNoTitle">0dp</item>
        <item name="paddingBottomNoButtons">0dp</item>
        <item name="android:layout_margin">0dp</item>
    </style>

    <style name="AppChip.Red.Color" parent="TextAppearance.MaterialComponents.Chip">
        <item name="android:textSize">@dimen/text_caption2</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
    </style>


    <!-- Font Text Body Regular Color Neutral Dark 30 -->
    <style name="Body1LargeText.Regular.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <style name="Body2MediumText.Regular.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <style name="Body3SmallText.Regular.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <!-- Font Text Body Semi Bold Color Neutral Dark 30 -->
    <style name="Body1LargeText.SemiBold.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <style name="Body2MediumText.SemiBold.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <style name="Body3SmallText.SemiBold.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>


    <!-- Font Text Body Medium Color Light 10 -->
    <style name="Body1LargeText.Medium.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Body2MediumText.Medium.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralLight10">
        <item name="android:textColor">@color/neutral_light10</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralLight20">
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <!-- Font Text Body Medium Color Neutral Dark 30 -->
    <style name="Body1LargeText.Medium.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <style name="Body2MediumText.Medium.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralDark30">
        <item name="android:textColor">@color/neutral_dark30</item>
    </style>

    <!-- Font Text Body Medium Color Light 40 -->
    <style name="Body1LargeText.Medium.NeutralLight40">
        <item name="android:textColor">@color/neutral_light40</item>
    </style>

    <style name="Body2MediumText.Medium.NeutralLight40">
        <item name="android:textColor">@color/neutral_light40</item>
    </style>

    <style name="Body3SmallText.Medium.NeutralLight40">
        <item name="android:textColor">@color/neutral_light40</item>
    </style>
    <!-- Font Text Caption Medium Color Neutral Light 50 -->
    <style name="Caption1SmallText.Medium.NeutralLight50">
        <item name="android:textColor">@color/neutral_light50</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralLight50">
        <item name="android:textColor">@color/neutral_light50</item>
    </style>

    <style name="Caption1SmallText.Medium.NeutralLight30">
        <item name="android:textColor">@color/neutral_light30</item>
    </style>

    <!-- Font Text Caption Bold Color Neutral Light 40 -->
    <style name="Caption1SmallText.Bold.NeutralLight40">
        <item name="android:textColor">@color/neutral_light40</item>
    </style>

    <style name="Caption2MicroText.Bold.NeutralLight40">
        <item name="android:textColor">@color/neutral_light40</item>
    </style>

    <style name="Caption2MicroText.Bold.PrimaryOrange80">
        <item name="android:textColor">@color/primary_orange80</item>
    </style>

    <style name="Caption2MicroText.Bold.semanticGreen80">
        <item name="android:textColor">@color/semanticGreen80</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Light 20 -->
    <style name="Caption1SmallText.Medium.NeutralLight20">
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <style name="Caption2MicroText.Medium.NeutralLight20">
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <!-- Font Text Caption Medium Color Neutral Light 40 -->
    <style name="Caption1SmallText.Medium.NeutralLight40">
        <item name="android:textColor">@color/neutral_light40</item>
    </style>


    <!-- Font Text Caption Medium Color Neutral Blue 30 -->
    <style name="Caption2MicroText.Medium.PrimaryBlue30">
        <item name="android:textColor">@color/primary_blue30</item>
    </style>

    <!-- Font Text Body Bold Color Primary Orange 80-->
    <style name="Body1LargeText.Bold.PrimaryOrange80">
        <item name="android:textColor">@color/primary_orange80</item>
    </style>

    <style name="Body2MediumText.Bold.PrimaryOrange80">
        <item name="android:textColor">@color/primary_orange80</item>
    </style>

    <style name="Body3SmallText.Bold.PrimaryOrange80">
        <item name="android:textColor">@color/primary_orange80</item>
    </style>


    <!-- Font Text Body Bold Color Green 80-->
    <style name="Body2MediumText.Bold.SematicGreen80">
        <item name="android:textColor">@color/semanticGreen80</item>
    </style>

    <style name="Title4Text.Bold.SematicGreen80">
        <item name="android:textColor">@color/semanticGreen80</item>
    </style>

    <style name="TextInputLayoutRevamp" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeWidthFocused">1dp</item>
        <item name="boxStrokeColor">@color/selector_text_input_layout_box</item>
        <item name="android:textColorHint">@color/neutral_light30</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/space_x1</item>
        <item name="boxCornerRadiusBottomStart">@dimen/space_x1</item>
        <item name="boxCornerRadiusTopEnd">@dimen/space_x1</item>
        <item name="boxCornerRadiusTopStart">@dimen/space_x1</item>
    </style>

    <style name="TextInputLayoutRevamp.RadiusSix" parent="TextInputLayoutRevamp">
        <item name="boxCornerRadiusBottomEnd">@dimen/size_6dp</item>
        <item name="boxCornerRadiusBottomStart">@dimen/size_6dp</item>
        <item name="boxCornerRadiusTopEnd">@dimen/size_6dp</item>
        <item name="boxCornerRadiusTopStart">@dimen/size_6dp</item>
    </style>

    <style name="TextInputLayoutRangeRevamp" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeWidthFocused">1dp</item>
        <item name="boxStrokeColor">@color/selector_text_input_layout_box_nt30</item>
        <item name="android:textColorHint">@color/neutral_light30</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/space_x1</item>
        <item name="boxCornerRadiusBottomStart">@dimen/space_x1</item>
        <item name="boxCornerRadiusTopEnd">@dimen/space_x1</item>
        <item name="boxCornerRadiusTopStart">@dimen/space_x1</item>
    </style>

    <style name="TextInputLayoutRevamp.NoBorder" parent="TextInputLayoutRevamp">
        <item name="boxStrokeWidth">0dp</item>
        <item name="boxStrokeWidthFocused">0dp</item>
    </style>

    <style name="TextInputLayoutDropDownRevamp" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeColor">@color/selector_text_input_layout_box</item>
        <item name="android:textColorHint">@color/neutral_light30</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/space_x1</item>
        <item name="boxCornerRadiusBottomStart">@dimen/space_x1</item>
        <item name="boxCornerRadiusTopEnd">@dimen/space_x1</item>
        <item name="boxCornerRadiusTopStart">@dimen/space_x1</item>
    </style>

    <style name="TextInputLayoutPrefix" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/text_caption1</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="android:textColor">@color/neutral_light80</item>
        <item name="android:layout_margin">@dimen/space_x2</item>
        <item name="android:paddingStart">@dimen/space_x2</item>
        <item name="android:paddingEnd">@dimen/space_x2</item>
    </style>

    <style name="HelperTextStyle" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingLeft">0dp</item> <!-- Untuk kompatibilitas -->
    </style>

    <style name="ToolbarTitleNewStyle">
        <item name="android:layout_gravity">center</item>
        <item name="android:textAlignment">center</item>
        <item name="android:textSize">@dimen/size_18dp</item>
        <item name="android:textColor">@color/colorTextWhite</item>
        <item name="android:fontFamily">@font/bri_text_bold</item>
    </style>

    <style name="CustomRadioButton">
        <item name="android:textColor">@color/blackColor</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorHighlight">@color/whiteColor</item>
        <item name="android:textColorLink">@color/whiteColor</item>
        <item name="android:textSize">@dimen/body_small_text</item>
    </style>

    <style name="CustomSegmentedGroupLayoutTextStyle" parent="CustomRadioButton">
        <item name="textAllCaps">false</item>
        <item name="android:button">@null</item>
        <item name="android:minHeight">33dp</item>
        <item name="android:minWidth">70dp</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingLeft">5dp</item>
        <item name="android:paddingRight">5dp</item>
    </style>


    <!--Revamp DASHBOARD -->

    <!-- PFM -->
    <style name="Body2MediumText.Regular.PfmTop">
        <item name="android:textColor">@color/neutral_light60</item>
        <item name="android:textSize">@dimen/space_x1_half</item>
    </style>

    <style name="Body2MediumText.Regular.PfmBottom">
        <item name="android:textColor">@color/neutral_light60</item>
        <item name="android:textSize">14dp</item>
    </style>

    <style name="Body2MediumText.Regular.DashboardDetail">
        <item name="android:textColor">@color/primary_blue80</item>
        <item name="android:textSize">14dp</item>
    </style>


    <style name="Title1Text.Bold.Primary80.TextButton">
        <item name="android:textSize">@dimen/space_x1_half</item>
    </style>

    <style name="DialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:layout_margin">7dp</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="CustomBottomSheetDialogReceipt" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyleReceipt</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="CustomBottomSheetStyleReceipt" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
        <item name="behavior_peekHeight">630dp</item>
    </style>

    <style name="FilterTextNormal">
        <item name="android:textColor">@color/neutral_baseWhite</item>
        <item name="android:fontFamily">@font/bri_text_medium</item>
        <item name="android:textSize">@dimen/size_14dp</item>
    </style>

    <style name="FilterTextSelected">
        <item name="android:textColor">@color/primary_blue100</item>
        <item name="android:fontFamily">@font/bri_text_bold</item>
        <item name="android:textSize">@dimen/size_14dp</item>
    </style>

    <style name="FilterTextLifestyleNormal">
        <item name="android:textColor">@color/neutral_light80</item>
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
        <item name="android:textSize">@dimen/size_14dp</item>
    </style>

    <style name="ShapeAppearanceOverlay.App.CornerSize50Percent" parent="">
        <item name="cornerSize">50%</item>
    </style>

    <style name="ShapeableImageViewStyle50Percent" parent="">
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.App.CornerSize50Percent</item>
    </style>

    <style name="ShapeAppearanceOverlay.App.CornerTopLeftBottomLeftRounded" parent="">
        <item name="cornerSizeTopLeft">8dp</item>
        <item name="cornerSizeTopRight">0dp</item>
        <item name="cornerSizeBottomLeft">8dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerFamily">rounded</item>
    </style>

    <style name="Widget.Android.AppWidget.Container" parent="android:Widget">
        <item name="android:id">@android:id/background</item>
        <item name="android:background">?android:attr/colorBackground</item>
    </style>

    <style name="Widget.Android.AppWidget.InnerView" parent="android:Widget">
        <item name="android:background">?android:attr/colorBackground</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="ShapeAppearanceOverlay.App.CornerSize20Percent" parent="">
        <item name="cornerSize">20%</item>
    </style>

    <style name="ZeroSizeErrorTextAppearance">
        <item name="android:textSize">0sp</item>
    </style>

    <style name="ChipAftMonthly" parent="Widget.MaterialComponents.Chip.Entry">
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipStrokeColor">@color/neutral_light20</item>
        <item name="chipEndPadding">@dimen/space_half</item>
        <item name="chipStartPadding">@dimen/space_half</item>
        <item name="android:padding">@dimen/space_x1_half</item>
        <item name="chipMinTouchTargetSize">0dp</item>
        <item name="checkedIconVisible">false</item>
        <item name="checkedIconEnabled">false</item>
        <item name="checkedIcon">@null</item>
        <item name="icon">@null</item>
        <item name="closeIcon">@null</item>
        <item name="chipBackgroundColor">@color/white</item>

        <item name="android:textSize">@dimen/text_body2</item>
        <item name="fontFamily">@font/bri_digital_text_medium</item>
    </style>

    <style name="CornerSize16Dp" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
        <item name="cornerSizeBottomLeft">16dp</item>
        <item name="cornerSizeBottomRight">16dp</item>
    </style>

    <!--material chip used in calendar monhly aft-->
    <style name="ChipAftMonthlySelected" parent="ChipAftMonthly">
        <item name="chipStrokeWidth">0dp</item>
        <item name="chipBackgroundColor">@color/primary_blue80</item>
        <item name="android:textSize">@dimen/text_body2</item>
        <item name="text_color">@color/blue</item>
    </style>

    <style name="ChipAftMonthlyNonActive" parent="ChipAftMonthly">
        <item name="chipStrokeWidth">0dp</item>
        <item name="chipBackgroundColor">@color/neutralLight30</item>
        <item name="android:textColor">@color/red</item>
        <item name="android:textSize">@dimen/text_body2</item>
    </style>

    <style name="ButtonMaterialRevamp" parent="Widget.MaterialComponents.Button.OutlinedButton.Icon">
        <item name="cornerRadius">@dimen/space_x1</item>
        <item name="android:textSize">@dimen/text_caption1</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="android:textColor">@color/selector_button_background_primary</item>
    </style>

    <style name="ButtonMaterialRevamp.Primary.OutlineIcon" parent="ButtonMaterialRevamp">
        <item name="cornerRadius">@dimen/space_x1</item>
        <item name="backgroundTint">@color/selector_button_background_icon</item>
        <item name="strokeColor">@color/selector_button_background_primary</item>
        <item name="strokeWidth">1dp</item>
        <item name="android:textSize">@dimen/text_caption1</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="android:textColor">@color/selector_button_background_primary</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="iconTint">@color/selector_button_icon_tint</item>
    </style>
    <style name="ButtonMaterialRevamp.Primary.Icon" parent="ButtonMaterialRevamp">
        <item name="cornerRadius">@dimen/space_x1</item>
        <item name="backgroundTint">@color/selector_button_color_primary</item>
        <item name="strokeWidth">0dp</item>
        <item name="android:textSize">@dimen/text_caption1</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="iconTint">@color/white</item>
        <item name="android:textColor">@color/selector_text_color_button_primary</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
    </style>

    <style name="ChipFilter" parent="Widget.MaterialComponents.Chip.Entry">
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipEndPadding">@dimen/space_x2</item>
        <item name="chipStartPadding">@dimen/space_x2</item>
        <item name="chipMinTouchTargetSize">0dp</item>
        <item name="checkedIconVisible">false</item>
        <item name="checkedIconEnabled">false</item>
        <item name="checkedIcon">@null</item>
        <item name="icon">@null</item>
        <item name="closeIcon">@null</item>
        <item name="android:textAlignment">center</item>
        <item name="cornerRadius">@dimen/space_x1</item>
        <item name="android:textSize">@dimen/text_caption1</item>
        <item name="fontFamily">@font/bri_digital_text_semi_bold</item>
        <item name="chipBackgroundColor">@color/chip_filter_selector_bg</item>
        <item name="android:textColor">@color/chip_filter_selector_text</item>
        <item name="chipStrokeColor">@color/chip_filter_selector_stroke</item>
    </style>
    <style name="Checkbox.Material" parent="Widget.MaterialComponents.CompoundButton.CheckBox">
        <item name="buttonTint">@color/selector_checkbox_material_button_tint</item>
    </style>
    <style name="CardDataView" parent="Widget.MaterialComponents.Chip.Entry">
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipEndPadding">@dimen/size_6dp</item>
        <item name="chipStartPadding">@dimen/size_6dp</item>
        <item name="chipMinTouchTargetSize">0dp</item>
        <item name="checkedIconVisible">false</item>
        <item name="checkedIconEnabled">false</item>
        <item name="checkedIcon">@null</item>
        <item name="icon">@null</item>
        <item name="closeIcon">@null</item>
        <item name="android:textAlignment">center</item>
        <item name="cornerSize">10%</item>
        <item name="android:textSize">@dimen/text_caption1</item>
        <item name="fontFamily">@font/bri_digital_text_semibold</item>
        <item name="android:fontFamily">@font/bri_digital_text_semibold</item>
        <item name="chipBackgroundColor">@color/transparent</item>
        <item name="android:textColor">@color/neutral_dark40</item>
        <item name="chipStrokeColor">@color/transparent</item>
    </style>
    <style name="CardDataViewDefault" parent="CardDataView">
        <item name="chipBackgroundColor">@color/light_10</item>
        <item name="android:textColor">@color/neutral_light80</item>
    </style>
    <style name="CardDataViewDanger" parent="CardDataView">
        <item name="chipBackgroundColor">@color/error10</item>
        <item name="android:textColor">@color/error80</item>
    </style>
    <style name="CardDataViewSuccess" parent="CardDataView">
        <item name="chipBackgroundColor">@color/success10</item>
        <item name="android:textColor">@color/success80</item>
    </style>
    <style name="CardDataViewWarning" parent="CardDataView">
        <item name="chipBackgroundColor">@color/primary_orange10</item>
        <item name="android:textColor">@color/primary_orange80</item>
    </style>
    <style name="Alert.Outline" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">@dimen/space_half</item>
        <item name="strokeWidth">1dp</item>
        <item name="cardElevation">0dp</item>
        <item name="contentPadding">@dimen/space_x1_half</item>
    </style>

    <style name="Alert.Outline.Warning" parent="Alert.Outline">
        <item name="strokeColor">@color/warning80</item>
        <item name="cardBackgroundColor">@color/warning10</item>
    </style>

    <style name="Alert.Outline.Success" parent="Alert.Outline">
        <item name="strokeColor">@color/success80</item>
        <item name="cardBackgroundColor">@color/success10</item>
    </style>

    <style name="Alert.Outline.Error" parent="Alert.Outline">
        <item name="strokeColor">@color/error80</item>
        <item name="cardBackgroundColor">@color/error10</item>
    </style>

    <style name="Alert.Outline.Information" parent="Alert.Outline">
        <item name="strokeColor">@color/primary_blue50</item>
        <item name="cardBackgroundColor">@color/primary_blue10</item>
    </style>

    <style name="Alert.Outline.Default" parent="Alert.Outline">
        <item name="strokeColor">@color/neutral_light20</item>
        <item name="cardBackgroundColor">@color/neutral_light10</item>
    </style>

    <style name="CustomSnackbar" parent="Widget.Design.Snackbar">
        <!-- Atur properti latar belakang dan teks aksi yang Anda inginkan -->
        <item name="android:background">@color/primary_blue10</item>
        <item name="android:textColor">@color/neutral_light20</item>
    </style>

    <style name="CustomButtonPrimaryStyle" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="customBackground">@drawable/button_primary_fragment_bg</item>
        <item name="customTextColor">@color/whiteColor</item>
        <item name="customTextAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
        <item name="customTextStyle">bold</item>
        <item name="customElevation">0dp</item>
        <item name="customStateListAnimator">@null</item>
    </style>

    <style name="CustomButtonPrimaryOutlineStyle" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="customBackground">@drawable/button_primary_outline_bg</item>
        <item name="customTextColor">@color/primary_blue80</item>
        <item name="customTextAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
        <item name="customTextStyle">bold</item>
        <item name="customElevation">0dp</item>
        <item name="customStateListAnimator">@null</item>
    </style>

    <style name="CustomButtonPrimaryRevampOutlineTransparent" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="customBackground">@color/transparent</item>
        <item name="customTextColor">@color/primary_blue80</item>
        <item name="customTextSize">@dimen/body_medium_text</item>
        <item name="customTextAllCaps">false</item>
        <item name="android:fontFamily">@font/bri_digital_text_bold</item>
        <item name="customTextStyle">bold</item>
    </style>

    <style name="TextAppearance.Bold" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textStyle">bold</item>
    </style>


    <!-- NEW SKIN -->

    <style name="TextHint.Small" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/ns_hint_color</item>
        <item name="android:fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="PromoFastMenuImageCornerStyle" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
        <item name="cornerSizeBottomLeft">16dp</item>
        <item name="cornerSizeBottomRight">16dp</item>
    </style>

    <style name="FastMenuWelcomeTextStyle" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">24dp</item>
        <item name="cornerSizeTopRight">80dp</item>
        <item name="cornerSizeBottomLeft">24dp</item>
        <item name="cornerSizeBottomRight">24dp</item>
    </style>

    <style name="ButtonPrimary.MaterialDrawable" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:theme">@style/AppThemeBlueBarNewSkin</item>
        <item name="android:background">@drawable/button_primary_new_skin</item>
        <item name="android:textColor">@color/selector_button_primary_text_new_skin</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/TitleText.Small.SemiBold.NeutralBaseWhite</item>
        <item name="iconTint">@color/white</item>
        <item name="iconPadding">8dp</item>
    </style>

    <style name="AppThemeNewSkinFull" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/ns_primary500</item>
        <item name="colorPrimaryDark">@color/ns_primary500</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:fontFamily">@font/bri_digital_text_medium</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

    <style name="AppThemeNewSkinFullTransparent" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:fitsSystemWindows">true</item>
    </style>

    <style name="TopRoundedCardShape" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>

    <style name="Title" parent="BaseAppTheme.Text">
        <item name="fontFamily">@font/bri_digital_text_semi_bold</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Title.mdreg">
        <item name="android:lineHeight">@dimen/size_28dp</item>
    </style>

    <style name="HeadlineNs" parent="BaseAppTheme.Text">
        <item name="fontFamily">@font/bri_digital_text_semi_bold</item>
        <item name="android:fontWeight">600</item>
    </style>

    <style name="HeadlineNs.smbold">
        <item name="android:lineHeight">46dp</item>
    </style>

    <style name="BodyNs" parent="BaseAppTheme.Text">
        <item name="fontFamily">@font/bri_digital_text_regular</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="BodyNs.mdreg">
        <item name="android:lineHeight">20dp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Theme.App.BottomSheetDialog.FullScreen" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <!-- Make the dialog window itself non-floating and fill the screen -->
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowFullscreen">true</item> <!-- Try adding this -->
        <item name="android:windowBackground">@android:color/transparent
        </item> <!-- Transparent window background -->

        <!-- Remove window insets / padding -->
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">true
        </item> <!-- Or false if you don't want dimming -->
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="ReactNativeAppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Jangan override fontFamily di sini -->
        <!-- Tambahkan item lain jika perlu, tapi biarkan fontFamily default -->
    </style>
    <style name="HintTextFloatingLabel" parent="TextAppearance.AppCompat.Small">
        <item name="android:textSize">10sp</item> <!-- Your desired size -->
        <item name="android:textColor">@color/black82</item> <!-- Optional color change -->
    </style>

    <style name="TabTextAppearancePayment" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">18sp</item>
        <item name="textAllCaps">false</item>
        <item name="fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="TabTextAppearancePaymentSelected" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">18sp</item>
        <item name="textAllCaps">false</item>
        <item name="fontFamily">@font/bri_digital_text_semi_bold</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="HeadingSemibold">
        <item name="fontFamily">@font/bri_digital_text_semibold</item>
        <item name="android:lineSpacingExtra">@dimen/text_heading_line_spacing_extra_6sp</item>
        <item name="android:textColor">@color/black_ns_main</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="LabelBlackRegular">
        <item name="fontFamily">@font/bri_digital_text_regular</item>
        <item name="android:lineSpacingExtra">@dimen/text_label_line_spacing_extra_6sp</item>
        <item name="android:textColor">@color/black_ns_main</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="LabelGreyRegular">
        <item name="fontFamily">@font/bri_digital_text_regular</item>
        <item name="android:lineSpacingExtra">@dimen/text_label_line_spacing_extra_6sp</item>
        <item name="android:textColor">@color/black_ns_600</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="LabelSemiBlackRegular">
        <item name="fontFamily">@font/bri_digital_text_regular</item>
        <item name="android:lineSpacingExtra">@dimen/text_label_line_spacing_extra_6sp</item>
        <item name="android:textColor">@color/black_ns_700</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="BRIReskinRegular">
        <item name="android:fontFamily">@font/bri_digital_text_regular</item>
    </style>

    <style name="BRIReskinSemiBold">
        <item name="android:fontFamily">@font/bri_digital_text_semibold</item>
    </style>

    <style name="Headline.lg.smbold" parent="BRIReskinSemiBold">
        <item name="android:textStyle">bold</item> <!-- SemiBold -->
        <item name="android:textSize">40sp</item>
        <item name="android:lineSpacingExtra">10sp</item> <!-- 50px - 40px -->
        <item name="android:letterSpacing">-0.05</item> <!-- -2px / 40px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Headline.md.smbold" parent="BRIReskinSemiBold">
        <item name="android:textStyle">bold</item> <!-- SemiBold -->
        <item name="android:textSize">36sp</item>
        <item name="android:lineSpacingExtra">12sp</item> <!-- 48px - 36px -->
        <item name="android:letterSpacing">-0.0278</item> <!-- -1px / 36px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Headline.sm.smbold" parent="BRIReskinSemiBold">
        <item name="android:textStyle">bold</item> <!-- SemiBold -->
        <item name="android:textSize">32sp</item>
        <item name="android:lineSpacingExtra">14sp</item> <!-- 46px - 32px -->
        <item name="android:letterSpacing">-0.03125</item> <!-- -1px / 32px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Title.xl.reg" parent="BRIReskinRegular">
        <item name="android:textSize">28sp</item>
        <item name="android:lineSpacingExtra">12sp</item> <!-- 40px - 28px -->
        <item name="android:letterSpacing">0.0</item> <!-- 0px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Title.xl.smbold" parent="Title.xl.reg">
        <item name="android:textStyle">bold</item> <!-- SemiBold -->
    </style>

    <style name="Title.lg.reg" parent="BRIReskinRegular">
        <item name="android:textSize">24sp</item>
        <item name="android:lineSpacingExtra">8sp</item> <!-- 32px - 24px -->
        <item name="android:letterSpacing">0.0</item> <!-- 0px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Title.lg.smbold" parent="Title.lg.reg">
        <item name="android:textStyle">bold</item> <!-- SemiBold -->
    </style>

    <style name="Title.md.reg" parent="BRIReskinRegular">
        <item name="android:textSize">20sp</item>
        <item name="android:lineSpacingExtra">8sp</item> <!-- 28px - 20px -->
        <item name="android:letterSpacing">0.0</item> <!-- 0px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Title.md.smbold" parent="Title.md.reg">
        <item name="android:textStyle">bold</item> <!-- SemiBold -->
    </style>

    <style name="Title.sm.reg" parent="BRIReskinRegular">
        <item name="android:textSize">16sp</item>
        <item name="android:lineSpacingExtra">8sp</item> <!-- 24px - 16px -->
        <item name="android:letterSpacing">0.0</item> <!-- 0px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Title.sm.smbold" parent="Title.sm.reg">
        <item name="android:textStyle">bold</item> <!-- SemiBold -->
    </style>

    <style name="Body.lg.reg" parent="BRIReskinRegular">
        <item name="android:textSize">14sp</item>
        <item name="android:lineSpacingExtra">6sp</item> <!-- 20px - 14px -->
        <item name="android:letterSpacing">0.0</item> <!-- 0px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Body.lg.smbold" parent="Body.lg.reg">
        <item name="android:textStyle">bold</item> <!-- SemiBold -->
    </style>

    <style name="Body.md.reg" parent="BRIReskinRegular">
        <item name="android:textSize">12sp</item>
        <item name="android:lineSpacingExtra">6sp</item> <!-- 18px - 12px -->
        <item name="android:letterSpacing">0.0</item> <!-- 0px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Body.md.smbold" parent="Body.md.reg">
        <item name="android:textStyle">bold</item> <!-- SemiBold -->
    </style>

    <style name="Body.sm.reg" parent="BRIReskinRegular">
        <item name="android:textSize">10sp</item>
        <item name="android:lineSpacingExtra">6sp</item> <!-- 16px - 10px -->
        <item name="android:letterSpacing">0.0</item> <!-- 0px -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Body.sm.smbold" parent="Body.sm.reg">
        <item name="android:textStyle">bold</item>
    </style>

</resources>