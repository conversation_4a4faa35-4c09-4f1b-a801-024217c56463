<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:card_view="http://schemas.android.com/apk/res-auto"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:background="@drawable/bg_new_skin_activity_container"
		>

	<!-- Toolbar -->
	<include
			android:id="@+id/toolbar"
			layout="@layout/toolbar_new_skin"
			android:layout_width="match_parent"
			android:layout_height="wrap_content" />

	<androidx.coordinatorlayout.widget.CoordinatorLayout
			android:id="@+id/content"
			android:layout_width="match_parent"
			android:layout_height="match_parent">

		<ScrollView
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:layout_marginTop="70dp"
				android:paddingHorizontal="16dp"
				android:orientation="vertical"
				android:paddingBottom="100dp"
				android:background="@drawable/bg_card_rounded_ns"
				android:clipToPadding="false">

			<LinearLayout
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:orientation="vertical"
					android:paddingTop="@dimen/size_24dp">

				<TextView
						android:id="@+id/tv_title"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:text="@string/txt_hubungkan_e_wallet"
						android:textAlignment="center"
						android:textSize="@dimen/size_20sp"
						android:textColor="@color/black_ns_main"
						android:textStyle="bold"/>

				<LinearLayout
						android:layout_width="@dimen/size_72dp"
						android:layout_height="@dimen/size_72dp"
						android:layout_marginTop="@dimen/size_24dp"
						android:layout_gravity="center"
						android:layout_marginEnd="@dimen/size_12dp"
						android:background="@drawable/round_icon_ns"
						>


				<ImageView
						android:id="@+id/iv_logo_dompet"
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:scaleType="fitXY"
						android:layout_gravity="center"
						android:src="@drawable/ic_ewallet_gopay"/>

				</LinearLayout>
				<TextView
						android:id="@+id/tv_no_pelanggan"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						tools:text="085667787665"
						android:textAlignment="center"
						android:layout_marginTop="@dimen/size_16dp"
						android:paddingVertical="@dimen/size_16dp"
						android:textSize="@dimen/size_20sp"
						android:background="@drawable/bg_input_black_100_brimo_ns"
						android:textColor="@color/text_desc_ns"
						android:textStyle="bold"/>

				<TextView
						android:id="@+id/tv_desc"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:layout_marginTop="@dimen/size_24dp"
						android:textSize="@dimen/size_14sp"
						android:textColor="@color/black_ns_main"
						android:text="@string/txt_desc_hubungkan_e_wallet"
						android:textAlignment="center"/>
			</LinearLayout>

		</ScrollView>
	</androidx.coordinatorlayout.widget.CoordinatorLayout>

	<!-- Bottom Button -->
	<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_gravity="bottom"
			android:layout_alignParentBottom="true"
			android:orientation="vertical"
			android:padding="16dp"
			android:elevation="8dp"
			android:background="@android:color/white">

		<Button
				android:id="@+id/btnSubmit"
				android:layout_width="match_parent"
				android:layout_height="56dp"
				style="@style/CustomButtonStyle"
				android:text="@string/action_dompet_digital_hubungkan"
				android:textSize="16sp"
				android:textAllCaps="false"
				android:background="@drawable/rounded_button_ns"
				android:textColor="@color/selector_text_color_button_primary_ns"
				/>
	</LinearLayout>

</FrameLayout>