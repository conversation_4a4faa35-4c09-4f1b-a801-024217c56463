<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_confirmation_reskin">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/ly_total_detail">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="0dp"
                android:layout_height="?attr/actionBarSize"
                android:background="@android:color/transparent"
                android:outlineProvider="bounds"
                app:layout_scrollFlags="scroll|enterAlways"
                app:elevation="0dp"
                app:liftOnScroll="false"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
                <TextView
                    android:id="@+id/textTitle"
                    android:text="Konfirmasi"
                    style="@style/Title.md.smbold"
                    android:textColor="@color/white_ns_main"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:singleLine="true"
                    android:layout_gravity="center" />
            </androidx.appcompat.widget.Toolbar>

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:id="@id/content"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toBottomOf="@+id/toolbar"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:orientation="vertical">

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/appbarDashboard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/transparent"
                    app:elevation="0dp"
                    app:liftOnScroll="false">

                    <com.google.android.material.appbar.CollapsingToolbarLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent"
                        android:minHeight="0dp"
                        app:expandedTitleTextAppearance="@style/TextAppearance.AppCompat.Title"
                        app:layout_scrollFlags="scroll|snap|exitUntilCollapsed"
                        app:scrimAnimationDuration="0"
                        app:titleEnabled="false">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toBottomOf="@+id/toolbar"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            android:layout_marginTop="24dp"
                            android:layout_marginBottom="24dp">
                            <TextView
                                android:id="@+id/tv_capt_bill"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                android:text="Total Transaksi"
                                style="@style/Title.sm.reg"
                                android:textColor="@color/white" />
                            <TextView
                                android:id="@+id/totalTagihan"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                app:layout_constraintTop_toBottomOf="@+id/tv_capt_bill"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                android:text="Rp502.000"
                                style="@style/Headline.sm.smbold"
                                android:textColor="@color/white" />


                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.appbar.CollapsingToolbarLayout>

                </com.google.android.material.appbar.AppBarLayout>

                <androidx.core.widget.NestedScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_card_rounded_ns"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingHorizontal="16dp"
                        android:paddingVertical="24dp">
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Tujuan Transaksi"
                            android:textColor="@color/black"
                            style="@style/Body.lg.smbold"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            android:id="@+id/textView6" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            android:layout_marginTop="16dp"
                            android:padding="16dp"
                            app:layout_constraintTop_toBottomOf="@+id/textView6"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            android:background="@drawable/bg_input_black_100_brimo_ns"
                            android:id="@+id/linearLayout6">

                            <RelativeLayout
                                android:layout_width="@dimen/size_32dp"
                                android:layout_height="@dimen/size_32dp">

                                <ImageView
                                    android:id="@+id/iv_area"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_alignParentTop="true"
                                    android:src="@drawable/ikon_wilayah" />
                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                tools:ignore="RtlSymmetry"
                                android:layout_marginStart="@dimen/space_x2">

                                <TextView
                                    android:id="@+id/tv_name_cust"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="@dimen/size_2dp"
                                    android:paddingVertical="@dimen/size_2dp"
                                    tools:text="Antonius"
                                    android:textColor="@color/black"
                                    style="@style/Body.lg.smbold"/>

                                <TextView
                                    android:id="@+id/tv_number_cust"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="@dimen/size_2dp"
                                    android:paddingVertical="@dimen/size_2dp"
                                    tools:text="PDAM - 000055812"
                                    style="@style/Body.md.reg"
                                    android:textColor="@color/black" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ll_saved_as"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_input_blue_brimo_ns"
                            android:gravity="center_vertical"
                            android:padding="@dimen/size_12dp"
                            android:layout_marginTop="16dp"
                            android:visibility="gone"
                            >

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/ic_info_ns"
                                android:layout_marginEnd="@dimen/size_8dp"/>

                            <TextView
                                android:id="@+id/tv_saved_as"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                tools:text="Tersimpan sebagai Brahmantio"
                                android:textSize="12sp"
                                android:textColor="@color/black_ns_main"
                                android:textStyle="bold"/>
                        </LinearLayout>

                        <RelativeLayout
                            android:id="@+id/rl_favorit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_input_black_100_brimo_ns"
                            android:backgroundTint="#F5F7FB"
                            android:paddingHorizontal="16dp"
                            android:paddingVertical="@dimen/size_15dp"
                            android:layout_marginTop="16dp"
                            app:layout_constraintTop_toBottomOf="@+id/linearLayout6"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_centerVertical="true"
                                android:layout_toStartOf="@+id/switch_save"
                                android:end="@id/switch_pp"
                                android:textColor="@color/black_ns_main"
                                android:text="Simpan Favorit"
                                style="@style/Body.md.reg" />

                            <androidx.appcompat.widget.SwitchCompat
                                android:id="@+id/switch_save"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:thumbTint="@drawable/switch_thumb_color"
                                app:trackTint="@drawable/switch_track_color"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:thumb="@drawable/tumb_selector"
                                android:track="@drawable/track_selector"
                                app:switchMinWidth="52dp"
                                app:switchPadding="4dp"
                                tools:ignore="UseSwitchCompatOrMaterialXml" />

                        </RelativeLayout>
                        <id.co.bri.brimo.ui.widget.input_til.BaseInputView
                            android:id="@+id/biv_save_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:visibility="visible"
                            app:hintText="Nama Tersimpan" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:layout_marginTop="24dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Sumber Dana"
                                android:textColor="@color/black"
                                style="@style/Body.lg.smbold"
                                android:layout_marginBottom="16dp"
                                app:layout_constraintTop_toBottomOf="@+id/rl_favorit"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/bg_input_black_100_brimo_ns"
                                android:padding="16dp">
                                <ImageView
                                    android:id="@+id/iv_rekening"
                                    android:layout_width="48dp"
                                    android:layout_height="32dp"
                                    android:src="@drawable/ic_card_britama"
                                    android:scaleType="fitCenter"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    android:layout_marginEnd="12dp" />

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="16dp"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:layout_constraintLeft_toRightOf="@+id/iv_rekening"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintBottom_toBottomOf="parent">
                                    <TextView
                                        android:id="@+id/tv_number_account"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        app:layout_constraintTop_toTopOf="parent"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"
                                        tools:text="agung_hars"
                                        style="@style/Body.md.reg"
                                        android:textColor="@color/black_ns_main" />

                                    <TextView
                                        android:id="@+id/tv_nominal_account"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        app:layout_constraintTop_toBottomOf="@+id/tv_number_account"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"
                                        tools:text="0290 3445 9681 112"
                                        style="@style/Body.md.reg"
                                        android:textColor="@color/black_ns_main"
                                        android:layout_marginTop="4dp" />

                                </androidx.constraintlayout.widget.ConstraintLayout>
                            </androidx.constraintlayout.widget.ConstraintLayout>

                        </LinearLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Detail Tagihan"
                            android:textColor="@color/black"
                            style="@style/Body.lg.smbold"
                            android:id="@+id/textView8"
                            app:layout_constraintTop_toBottomOf="@+id/rl_favorit"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            android:layout_marginTop="24dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp"
                            app:layout_constraintTop_toBottomOf="@+id/textView8"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            android:background="@drawable/bg_input_black_100_brimo_ns"
                            android:layout_marginTop="16dp">

                            <LinearLayout
                                android:id="@+id/ll_more_content"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:animateLayoutChanges="true"
                                android:visibility="gone" />

                            <View
                                android:id="@+id/v_line_bill_detail"
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#E9EEF6"
                                android:layout_marginVertical="8dp"
                                android:visibility="gone"/>

                            <!-- Nominal Row -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="12dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Nominal"
                                    android:textColor="#9CA3AF"
                                    style="@style/Body.lg.reg" />

                                <TextView
                                    android:id="@+id/tv_nominal"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    tools:text="Rp500.000"
                                    android:textColor="#000000"
                                    style="@style/Body.lg.reg" />
                            </LinearLayout>

                            <!-- Biaya Admin Row -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Biaya Admin"
                                    android:textColor="#9CA3AF"
                                    style="@style/Body.lg.reg" />

                                <TextView
                                    android:id="@+id/tv_admin_fee"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    tools:text="Rp2.000"
                                    android:textColor="#000000"
                                    style="@style/Body.lg.reg" />
                            </LinearLayout>


                            <!-- Lihat Lebih -->
                            <TextView
                                android:id="@+id/btnLihatLebih"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/size_12dp"
                                android:text="Lihat Lebih "
                                android:drawableEnd="@drawable/ic_arrow_down_blue"
                                android:textColor="#2563EB"
                                android:textStyle="bold"
                                android:textSize="14sp"
                                android:gravity="center"
                                android:layout_gravity="center_horizontal" />
                        </LinearLayout>
                    </LinearLayout>

                </androidx.core.widget.NestedScrollView>
            </androidx.coordinatorlayout.widget.CoordinatorLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_1dp"
            android:background="#E9EEF6"
            app:layout_constraintBottom_toTopOf="@+id/ly_total_detail" />

        <LinearLayout
            android:id="@+id/ly_total_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/whiteColor"
            android:orientation="vertical"
            android:visibility="visible"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <id.co.bri.brimo.ui.widget.BayarButtonView
                android:id="@+id/btnSubmit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:labelText="Bayar Sekarang"
                tools:amountText="Rp500.000"/>

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</FrameLayout>
