<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        xmlns:tools="http://schemas.android.com/tools"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x2"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:clickable="true"
                android:focusable="true"
                tools:src="@drawable/ic_menu_qna_tagihan_listrik"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_title"
                        style="@style/BodyMediumText.DemiBold.Black"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:text="Ratna Sarumpaet Sihombing Sitompel"
                        android:ellipsize="end"
                        android:textSize="14sp"
                        android:maxLines="1"
                        android:layout_weight="1"
                        android:layout_marginStart="@dimen/space_x1_half" />

                    <TextView
                        android:id="@+id/tv_datetime"
                        style="@style/Body2MediumText.Regular"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:text="12 Jan 2023, 10:45"
                        android:gravity="end"
                        android:textColor="#7B90A6"
                        android:textSize="12sp"
                        android:layout_weight="1"
                        android:layout_marginStart="@dimen/space_x1_half" />
                </LinearLayout>
                <TextView
                    android:id="@+id/tv_subtitle"
                    style="@style/Body2MediumText.Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    tools:text="Token Listrik - 009928566091"
                    android:textSize="12sp"
                    android:layout_marginStart="@dimen/space_x1_half" />
                <TextView
                    android:id="@+id/tv_noref"
                    style="@style/Body2MediumText.Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    tools:text="No. Ref - 0123 5467 8900 1221"
                    android:textSize="12sp"
                    android:layout_marginStart="@dimen/space_x1_half" />
            </LinearLayout>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_1dp"
            android:layout_marginTop="@dimen/space_x1"
            android:background="#E9EEF6" />
    </LinearLayout>
</layout>