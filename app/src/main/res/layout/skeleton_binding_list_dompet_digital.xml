<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:weightSum="2">

        <View
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_margin="12dp"
            android:layout_weight="1"
            android:background="@drawable/bg_skeleton_rounded_reskin" />

        <View
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_margin="12dp"
            android:layout_weight="1"
            android:background="@drawable/bg_skeleton_rounded_reskin" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:weightSum="2">

        <View
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_margin="12dp"
            android:layout_weight="1"
            android:background="@drawable/bg_skeleton_rounded_reskin" />

        <View
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_margin="12dp"
            android:layout_weight="1"
            android:background="@drawable/bg_skeleton_rounded_reskin" />
        </LinearLayout>
    </LinearLayout>
</layout>