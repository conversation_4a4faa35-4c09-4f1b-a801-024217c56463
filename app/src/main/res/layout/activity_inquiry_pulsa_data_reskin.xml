<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_new_skin_activity_container"
        android:orientation="vertical">
        <include
            android:id="@+id/toolbar"
            layout="@layout/toolbar_ns"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_card_rounded_ns">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <androidx.coordinatorlayout.widget.CoordinatorLayout
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:paddingTop="16dp"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:background="@null"
                    app:layout_constraintBottom_toTopOf="@+id/sdn_view">

                    <com.google.android.material.appbar.AppBarLayout
                        android:id="@+id/appbarDashboard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:elevation="0dp"
                        android:elevation="0dp"
                        android:background="@null">

                        <com.google.android.material.appbar.CollapsingToolbarLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:minHeight="0dp"
                            android:elevation="0dp"
                            app:expandedTitleTextAppearance="@style/TextAppearance.AppCompat.Title"
                            app:layout_scrollFlags="scroll|exitUntilCollapsed"
                            app:scrimAnimationDuration="0"
                            app:titleEnabled="false">

                            <id.co.bri.brimo.ui.widget.input_til.BaseInputView
                                android:id="@+id/biv_no_pelanggan"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="24dp"
                                app:prefixText="+62"
                                app:expanded="true"
                                app:hintText="Nomor HP" />

                        </com.google.android.material.appbar.CollapsingToolbarLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:layout_marginTop="24dp">
                            <com.google.android.material.tabs.TabLayout
                                android:id="@+id/tabLayout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:tabIndicatorColor="@color/ns_primary500"
                                app:tabIndicatorHeight="3dp"
                                app:tabTextColor="@color/ns_black500"
                                app:tabSelectedTextColor="@color/ns_primary500"
                                app:tabMode="scrollable"
                                app:tabIndicatorFullWidth="true"
                                app:tabGravity="start"
                                app:elevation="0dp"
                                android:elevation="0dp"
                                app:layout_scrollFlags="enterAlways"
                                app:tabTextAppearance="@style/CustomTabLayoutTextStyleRevamp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/size_1dp"
                                android:background="@color/border_gray_soft_ns" />
                        </LinearLayout>

                    </com.google.android.material.appbar.AppBarLayout>

                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/viewPager"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layout_behavior="@string/appbar_scrolling_view_behavior"
                        android:layout_weight="1"/>
                </androidx.coordinatorlayout.widget.CoordinatorLayout>
                <id.co.bri.brimo.ui.widget.sumberdana.SumberDanaView
                    android:id="@+id/sdn_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:buttonType="openBill"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>
    </LinearLayout>
</layout>