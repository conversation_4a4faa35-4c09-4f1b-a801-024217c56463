<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/rounded_button_ns"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:id="@+id/tvLabel"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            tools:text="Bayar Sekarang"
            android:textColor="@drawable/button_text_color_reskin"
            style="@style/Title.sm.smbold" />

        <TextView
            android:id="@+id/tvAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="Rp502.000"
            android:textColor="@drawable/button_text_color_reskin"
            style="@style/Title.sm.smbold" />
    </LinearLayout>
</layout>