	<LinearLayout
			xmlns:android="http://schemas.android.com/apk/res/android"
			xmlns:card_view="http://schemas.android.com/apk/res-auto"
			xmlns:tools="http://schemas.android.com/tools"
			android:id="@+id/ll_container"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:gravity="center_vertical"
			android:orientation="horizontal"
			android:layout_marginBottom="@dimen/size_16dp"
			android:background="@drawable/bg_input_black_100_brimo_ns"
			android:padding="@dimen/size_16dp">

		<LinearLayout
				android:layout_width="@dimen/size_30dp"
				android:layout_height="@dimen/size_30dp"
				android:background="@drawable/round_icon_ns"
				android:layout_gravity="center_vertical"
				android:layout_marginEnd="@dimen/size_16dp"
				>

			<ImageView
					android:id="@+id/img_banner"
					android:layout_width="match_parent"
					android:layout_height="match_parent"
					android:scaleType="fitXY"
					tools:src="@drawable/ic_ewallet_gopay"/>

		</LinearLayout>

		<LinearLayout
				android:id="@+id/ll_detail"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_weight="1"
				android:layout_gravity="center_vertical"
				android:orientation="vertical">

			<TextView
					android:id="@+id/tv_title"
					android:textColor="@color/black_ns_main"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:ellipsize="end"
					android:textSize="@dimen/size_12sp"
					tools:text="Brahmana - Gopay"/>

			<TextView
					android:id="@+id/tv_description"
					android:layout_width="match_parent"
					android:textStyle="bold"
					android:layout_height="wrap_content"
					android:layout_marginTop="@dimen/_4sdp"
					android:ellipsize="end"
					android:maxLines="1"
					android:textColor="@color/black_ns_main"
					android:textSize="@dimen/size_14sp"
					tools:text="GoPay"/>
		</LinearLayout>

		<ImageView
				android:id="@+id/iv_unbind"
				android:layout_width="@dimen/size_24dp"
				android:layout_height="@dimen/size_24dp"
				android:src="@drawable/ic_trash_ns"
				android:scaleType="fitXY"/>

	</LinearLayout>