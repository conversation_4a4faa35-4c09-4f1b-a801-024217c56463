<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:background="@android:color/transparent"
        android:outlineProvider="bounds"
        android:textAlignment="center"
        app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <TextView
            android:id="@+id/textTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Title.md.smbold"
            android:textColor="@color/white"
            tool:textColor="@color/black"
            android:layout_gravity="center"
            android:textAlignment="center"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            tool:text="Services" />

    </androidx.appcompat.widget.Toolbar>
</layout>