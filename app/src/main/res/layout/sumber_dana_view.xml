<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_1dp"
            android:background="#E9EEF6" />
        <LinearLayout
            android:id="@+id/ly_total_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="16dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:orientation="vertical"
            android:background="@android:color/white">

            <LinearLayout
                android:id="@+id/ll_sumberdana"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical"
                android:background="@android:color/white">

                <!-- Card Image -->
                <ImageView
                    android:id="@+id/iv_rekening"
                    android:layout_width="48dp"
                    android:layout_height="32dp"
                    tools:src="@drawable/ic_card_britama"
                    android:scaleType="fitCenter"
                    android:layout_marginEnd="12dp" />

                <!-- Text Container -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <!-- Account Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center|left">
                        <TextView
                            android:id="@+id/tv_number_account"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="0290 3445 9681 112"
                            style="@style/Body.sm.reg"
                            android:textColor="#000000" />

                        <ImageView
                            android:id="@+id/iv_showhide_currency"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_marginStart="5dp"
                            android:src="@drawable/icon_unhide_eye" />
                    </LinearLayout>

                    <!-- Balance -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:orientation="horizontal">
                        <TextView
                            android:id="@+id/tv_nominal_account"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Rp120"
                            style="@style/Body.lg.smbold"
                            android:textColor="#181C21" />

                        <ImageView
                            android:id="@+id/iv_refresh_saldo"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_marginStart="5dp"
                            android:layout_gravity="center"
                            android:visibility="gone"
                            android:src="@drawable/ic_circle_button" />

                        <TextView
                            android:id="@+id/tv_warning_currency"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:text="Saldo tidak cukup"
                            style="@style/Body.md.reg"
                            android:textColor="#E84040"
                            android:visibility="gone" />
                    </LinearLayout>
                </LinearLayout>

                <ImageView
                    android:id="@+id/iv_refresh"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="5dp"
                    android:visibility="gone"
                    android:src="@drawable/ic_circle_button" />

                <ImageView
                    android:id="@+id/iv_change"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_chevron_down_black_reskin" />
            </LinearLayout>

            <Button
                android:id="@+id/button"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginHorizontal="16dp"
                style="@style/CustomButtonStyle"
                android:text="Lanjutkan"
                android:textSize="16sp"
                android:textAllCaps="false"
                android:background="@drawable/rounded_button_ns"
                android:textColor="@color/selector_text_color_button_primary_ns"
                android:visibility="gone"
                tools:visibility="visible"/>

            <id.co.bri.brimo.ui.widget.BayarButtonView
                android:id="@+id/btn_payment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                app:labelText="Bayar Sekarang"
                tools:amountText="Rp500.000"
                android:visibility="gone" />
        </LinearLayout>
    </LinearLayout>
</layout>