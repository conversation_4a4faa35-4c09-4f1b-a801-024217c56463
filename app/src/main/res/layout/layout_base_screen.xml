<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_new_skin_activity_container"
        android:orientation="vertical">
        <include
            android:id="@+id/incl_toolbar"
            layout="@layout/toolbar_ns"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_card_rounded_ns">
            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <LinearLayout
                    android:id="@+id/ll_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    android:orientation="vertical" />
            </androidx.core.widget.NestedScrollView>
        </FrameLayout>

    </LinearLayout>
</layout>